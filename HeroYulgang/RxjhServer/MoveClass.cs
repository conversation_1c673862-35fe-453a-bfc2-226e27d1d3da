namespace RxjhServer;

public class MoveClass
{
	private int int_0;

	private float float_0;

	private float float_1;

	private float float_2;

	private int int_1;

	private float float_3;

	private float float_4;

	private float float_5;

	public int MAP
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public float X
	{
		get
		{
			return float_0;
		}
		set
		{
			float_0 = value;
		}
	}

	public float Y
	{
		get
		{
			return float_1;
		}
		set
		{
			float_1 = value;
		}
	}

	public float Z
	{
		get
		{
			return float_2;
		}
		set
		{
			float_2 = value;
		}
	}

	public int ToMAP
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public float ToX
	{
		get
		{
			return float_3;
		}
		set
		{
			float_3 = value;
		}
	}

	public float ToY
	{
		get
		{
			return float_4;
		}
		set
		{
			float_4 = value;
		}
	}

	public float ToZ
	{
		get
		{
			return float_5;
		}
		set
		{
			float_5 = value;
		}
	}
}
