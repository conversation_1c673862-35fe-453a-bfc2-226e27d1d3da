using System;
using System.Data.SqlClient;

namespace RxjhServer.DbClss;

public class DbPoolClass
{
    public string Conn { get; set; }

    public SqlParameter[] Prams { get; set; }

    public int Type { get; set; }

    public string Sql { get; set; }

    public static int DbPoolClassRun(string string_2, string string_3, SqlParameter[] sqlParameter_0, int int_1)
	{
		try
		{
			SqlConnection sqlConnection_ = new(string_2);
			return (int_1 == 1) ? ((SqlDBA.RunProcSql(sqlConnection_, string_3, sqlParameter_0) == -1) ? (-1) : 0) : ((SqlDBA.RunProc(sqlConnection_, string_3, sqlParameter_0) == -1) ? (-1) : 0);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "DB Pool Class Run error | " + ex);
			return -1;
		}
	}
}
