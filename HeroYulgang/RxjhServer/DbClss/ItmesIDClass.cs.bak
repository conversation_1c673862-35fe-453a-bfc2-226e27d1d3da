using System;
using System.Collections.Generic;

namespace RxjhServer.DbClss;

public class ItmesIDClass
{
	private object AsyncLocksw = new();

	private long long_0;

	private long long_1;

	public ItmesIDClass()
	{
		try
		{
			long_0 = 0L;
			long_1 = 0L;
			long_0 = long.Parse(DBA.GetDBValue_3("EXEC   XWWL_GetItemSerial2   1000").ToString());
			long_1 = long_0 + 1000;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Global_ID Error !!! | " + ex.Message);
			World.conn.Dispose();
			List<Players> list = new();
			foreach (var value in World.allConnectedChars.Values)
			{
				list.Add(value);
			}
			foreach (var item in list)
			{
				try
				{
					if (item.Client != null)
					{
						item.Client.Dispose();
					}
				}
				catch (Exception ex2)
				{
					LogHelper.WriteLine(LogLevel.Error, "SaveCharacterData Sai lầm | " + ex2.Message);
				}
			}
			list.Clear();
		}
	}

	public long AcquireBuffer()
	{
		using (new Lock(AsyncLocksw, "AcquireBuffer"))
		{
			if (long_0 < long_1)
			{
				return long_0++;
			}
			long_0 = long.Parse(DBA.GetDBValue_3("EXEC   XWWL_GetItemSerial2   1000").ToString());
			long_1 = long_0 + 1000;
			return long_0++;
		}
	}
}
