using System;
using System.Data;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.Database;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class EventClass : IDisposable
{
	private string jlsqlzj = string.Empty;

	private System.Timers.Timer ThoiGian1;

	private System.Timers.Timer ThoiGian2;

	private System.Timers.Timer ThoiGian3;

	private System.Timers.Timer ThoiGian4;

	private System.Timers.Timer ThoiGian5;

	private DateTime kssj;

	private DateTime kssjgj;

	private int kssjint;

	private int TheLucChiensj;

	public EventClass()
	{
		var num = 0;
		try
		{
			try
			{
				num = 1;
				World.EventTop.Clear();
				num = 2;
				World.TLC_ToaDo_CamPK.Clear();
			}
			catch
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi EventClass tại vị trí cấm PK vượt quá phạm vi nên bỏ qua [" + num + "] !!");
				World.EventTop.Clear();
			}
			num = 3;
			kssj = DateTime.Now.AddMinutes(World.ThoiGianChuanBi_ChoTheLucChien);
			num = 4;
			World.TheLucChien_Progress = 1;
			num = 5;
			World.TheLucChien_ChinhPhai_DiemSo = 0;
			num = 6;
			World.TheLucChien_TaPhai_DiemSo = 0;
			num = 7;
			ThoiGian1 = new(5000.0);
			num = 8;
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			num = 9;
			ThoiGian1.Enabled = true;
			num = 10;
			ThoiGian1.AutoReset = true;
			num = 11;
			ThoiGianKetThucSuKien1(null, null);
			num = 12;
			World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến EventClass Progress == 1 - lỗi tại num： [" + num + "] - " + ex);
			World.EventTop.Clear();
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				World.TheLucChien_Progress = 2;
				World.TheLucChien_ChinhPhai_DiemSo = 0;
				World.TheLucChien_TaPhai_DiemSo = 0;
				World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
				num = 0;
			}
			kssjint = num;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.Client.TreoMay)
				{
					continue;
				}
				if (value.NhanVatToaDo_BanDo == 801)
				{
					value.GuiDi_TheLucChien_TinTuc_TranChienSapBatDau(kssjint);
					value.GuiDi_TheLucChien_DemNguoc(kssjint);
					value.Tao_GietNguoi_Trong_TLC.Clear();
				}
				else if (value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
				{
					value.GuiDi_TheLucChien_LoiMoi_New2();
					if (World.TheLucChien_Progress == 1)
					{
						value.Send_LoiMoi_TLC_Packet(1, 1, 2);
					}
				}
			}
			if (kssjint <= 0)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
				World.TheLucChien_Progress = 3;
				World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
				kssjgj = DateTime.Now.AddMinutes(World.TheLucChien_TongThoiGian);
				ThoiGian2 = new(10000.0);
				ThoiGian2.Elapsed += ThoiGianKetThucSuKien2;
				ThoiGian2.Enabled = true;
				ThoiGian2.AutoReset = true;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGianKetThucSuKien1 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien2(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (World.TheLucChien_MidTime = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds);
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.Client.TreoMay)
				{
					continue;
				}
				if (value.NhanVatToaDo_BanDo == 801)
				{
					value.GuiDi_TheLucChien_TinTuc2(value);
					value.GuiDi_TheLucChien_CapNhat_DiemSo(value);
					value.GuiDi_TheLucChien_DemNguoc(num);
				}
				else if (value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
				{
					value.GuiDi_TheLucChien_LoiMoi_New2();
					if (World.TheLucChien_Progress == 1)
					{
						value.Send_LoiMoi_TLC_Packet(1, 1, 2);
					}
				}
			}
			if (num <= 0)
			{
				ThoiGian2.Enabled = false;
				ThoiGian2.Close();
				ThoiGian2.Dispose();
				World.TheLucChien_Progress = 4;
				World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
				ThoiGian3 = new(10000.0);
				ThoiGian3.Elapsed += ThoiGianKetThucSuKien3;
				ThoiGian3.Enabled = true;
				ThoiGian3.AutoReset = false;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGianKetThucSuKien2 error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien3(object sender, ElapsedEventArgs e)
	{
		try
		{
			DBA.ExeSqlCommand("DELETE FROM EventTop");
			foreach (var value in World.EventTop.Values)
			{
				var tenNhanVat = value.TenNhanVat;
				var dBToDataTable = DBA.GetDBToDataTable($"SELECT TenNhanVat from [EventTop] where TenNhanVat ='{tenNhanVat}'");
				DBA.ExeSqlCommand($"INSERT INTO EventTop (TenNhanVat,BangPhai,TheLuc,DangCap,GietNguoiSoLuong,TuVongSoLuong,Diem_ChinhPhai,Diem_TaPhai)values('{tenNhanVat}','{value.BangPhai}','{value.TheLuc}',{value.DangCap},{value.GietNguoiSoLuong},{value.TuVongSoLuong},{World.TheLucChien_ChinhPhai_DiemSo},{World.TheLucChien_TaPhai_DiemSo})");
			}
			if (World.TheLucChien_ChinhPhai_DiemSo > World.TheLucChien_TaPhai_DiemSo)
			{
				TheLucChiensj = 1;
				jlsqlzj = "CHINH_PHAI";
				World.GuiThongBao("Thế Lực Chiến kết thúc. Chính Phái dành chiến thắng");
			}
			else if (World.TheLucChien_ChinhPhai_DiemSo == World.TheLucChien_TaPhai_DiemSo)
			{
				TheLucChiensj = 3;
				jlsqlzj = string.Empty;
				World.GuiThongBao("Thế Lực Chiến kết thúc 2 bên hòa nhau");
			}
			else
			{
				TheLucChiensj = 2;
				jlsqlzj = "TA_PHAI";
				World.GuiThongBao("Thế Lực Chiến kết thúc, Tà Phái dành chiến thắng");
			}
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (value2.NhanVatToaDo_BanDo != 801)
				{
					continue;
				}
				value2.PhanThuong_HopThanKhi_Top_10_TLC();
				value2.Top1_den_15_PhanThuong_HuyChuongDanhDu();
				value2.GuiDi_TheLucChien_KetThuc_TinTuc(TheLucChiensj);
				if (value2.TheLucChien_PhePhai == jlsqlzj)
				{
					value2.Player_WuXun += World.Win_TLC_Phan_Thuong_VoHuan;
					value2.HeThongNhacNho("Đại hiệp nhận được [" + World.Win_TLC_Phan_Thuong_VoHuan + "] điểm võ huân, chiến công lừng lẫy giang hồ!", 10, "Truyền Âm Các");
					var parcelVacancy = value2.GetParcelVacancy(value2);
					if (parcelVacancy != -1)
					{
						value2.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000388), parcelVacancy, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 7);
						if (World.PhanThuong_ThemVao_TLC == 1)
						{
							value2.AddItem_ThuocTinh_int(World.PhanThuongBenThangTLC, value2.GetParcelVacancy(value2), 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5);
							value2.HeThongNhacNho("Đại hiệp được ban thêm bảo vật từ Thế Lực Chiến, quả là phúc lớn!", 7, "Truyền Âm Các");
						}
					}
					else
					{
						value2.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					}
				}
				else
				{
					value2.Player_WuXun += World.Lose_TLC_Phan_Thuong_VoHuan;
					value2.HeThongNhacNho("Đại hiệp nhận được [" + World.Lose_TLC_Phan_Thuong_VoHuan + "] điểm võ huân, dù bại vẫn hiển vinh!", 10, "Truyền Âm Các");
					var parcelVacancy2 = value2.GetParcelVacancy(value2);
					if (parcelVacancy2 != -1)
					{
						value2.AddItems_Lock(BitConverter.GetBytes(RxjhClass.CreateItemSeries()), BitConverter.GetBytes(1008000389), parcelVacancy2, BitConverter.GetBytes(1), new byte[56], khoaLai: true, 7);
						if (World.PhanThuong_ThemVao_TLC == 1)
						{
							value2.AddItem_ThuocTinh_int(World.PhanThuongBenThuaTLC, value2.GetParcelVacancy(value2), 1, 0, 0, 0, 0, 0, 0, 0, 0, 1, 5);
							value2.HeThongNhacNho("Đại hiệp được ban thêm bảo vật từ Thế Lực Chiến, quả là phúc lớn!", 7, "Truyền Âm Các");
						}
					}
					else
					{
						value2.HeThongNhacNho("Hành trang không còn chỗ trống!", 10, "Thiên cơ các");
					}
				}
				value2.SaveCharacterData();
				value2.UpdateMartialArtsAndStatus();
				value2.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
				value2.TinhToan_NhanVatCoBan_DuLieu();
				value2.CapNhat_HP_MP_SP();
			}
			World.EventTop.Clear();
			World.TheLucChien_Progress = 5;
			World.tmc_flag = false;
			World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
			kssjgj = DateTime.Now.AddMinutes(3.0);
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			ThoiGian4 = new(3000.0);
			ThoiGian4.Elapsed += ThoiGianKetThucSuKien4;
			ThoiGian4.Enabled = true;
			ThoiGian4.AutoReset = true;
			ThoiGian5 = new(10000.0);
			ThoiGian5.Elapsed += ThoiGianKetThucSuKien5;
			ThoiGian5.Enabled = true;
			ThoiGian5.AutoReset = true;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGian KetThuc SuKien3 error： - " + ex);
		}
	}

	public void ThoiGianKetThucSuKien4(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (World.TheLucChien_MidTime = (int)kssjgj.Subtract(DateTime.Now).TotalSeconds);
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 801)
				{
					value.GuiDi_TheLucChien_CapNhat_DiemSo(value);
					value.GuiDi_TheLucChien_DemNguoc(num);
					if (value.TheLucChien_PhePhai != jlsqlzj)
					{
						value.TheLucChien_PhePhai = string.Empty;
						value.TheLucChien_SatNhan_SoLuong = 0;
						value.TheLucChien_TuVong_SoLuong = 0;
						value.Send_BieuTuong(0);
						value.Mobile(420f + RNG.Next(-50, 50), 1740f + RNG.Next(-50, 50), 15f, 101, 1);
					}
				}
			}
			if (num > 0)
			{
				return;
			}
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
			World.TheLucChien_Progress = 6;
			World.conn.Transmit("FACTION_WAR_PROGRESS|" + World.TheLucChien_Progress);
			foreach (var value2 in World.allConnectedChars.Values)
			{
				if (value2.NhanVatToaDo_BanDo == 801)
				{
					value2.TheLucChien_PhePhai = string.Empty;
					value2.TheLucChien_SatNhan_SoLuong = 0;
					value2.TheLucChien_TuVong_SoLuong = 0;
					value2.Mobile(420f + RNG.Next(-50, 50), 1740f + RNG.Next(-50, 50), 15f, 101, 1);
				}
			}
			Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGian KetThuc SuKien4 error： " + ex);
		}
	}

	public void ThoiGianKetThucSuKien5(object sender, ElapsedEventArgs e)
	{
		try
		{
			ThoiGian5.Enabled = false;
			ThoiGian5.Close();
			ThoiGian5.Dispose();
			World.AddNpc_Boss(15137, 0f + RNG.Next(-10, 10), 0f + RNG.Next(-10, 10), 801);
			World.AddNpc_Boss(15136, 0f + RNG.Next(-10, 10), 0f + RNG.Next(-10, 10), 801);
			World.AddNpc_Boss(15137, 0f + RNG.Next(-10, 10), 0f + RNG.Next(-10, 10), 801);
			World.AddNpc_Boss(15136, 0f + RNG.Next(-10, 10), 0f + RNG.Next(-10, 10), 801);
			World.AddNpc_Boss(15137, 0f + RNG.Next(-10, 10), 0f + RNG.Next(-10, 10), 801);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Thế lực chiến ThoiGianKetThuc Event 5 error：" + ex);
		}
	}

	public void Dispose()
	{
		World.TheLucChien_Progress = 0;
		World.TheLucChien_MidTime = 0;
		World.tmc_flag = false;
		if (World.TheLucChien_Giam_NguoiChoi.Count > 0)
		{
			World.TheLucChien_Giam_NguoiChoi.Clear();
		}
		if (ThoiGian1 != null)
		{
			ThoiGian1.Enabled = false;
			ThoiGian1.Close();
			ThoiGian1.Dispose();
			ThoiGian1 = null;
		}
		if (ThoiGian2 != null)
		{
			ThoiGian2.Enabled = false;
			ThoiGian2.Close();
			ThoiGian2.Dispose();
			ThoiGian2 = null;
		}
		if (ThoiGian3 != null)
		{
			ThoiGian3.Enabled = false;
			ThoiGian3.Close();
			ThoiGian3.Dispose();
			ThoiGian3 = null;
		}
		if (ThoiGian4 != null)
		{
			ThoiGian4.Enabled = false;
			ThoiGian4.Close();
			ThoiGian4.Dispose();
			ThoiGian4 = null;
		}
		if (ThoiGian5 != null)
		{
			ThoiGian5.Enabled = false;
			ThoiGian5.Close();
			ThoiGian5.Dispose();
			ThoiGian5 = null;
		}
		foreach (var value in World.allConnectedChars.Values)
		{
			if (value.NhanVatToaDo_BanDo == 801)
			{
				value.Mobile(420f + RNG.Next(-50, 50), 1740f + RNG.Next(-50, 50), 15f, 101, 1);
			}
			value.GetUpdatedCharacterData(value);
			value.TheLucChien_PhePhai = string.Empty;
			value.TimesTang1Lan = 0;
		}
		World.TheLucChien_ChinhPhai_SoNguoi = 0;
		World.TheLucChien_TaPhai_SoNguoi = 0;
		World.eve = null;
		World.delNpc(801, 15136);
		World.delNpc(801, 15137);
	}

	public static void GUI_DI_THE_LUC_CHIEN_THAM_DU_FEED_MANG_BI_KICK_RA_TIN_TUC(Players player)
	{
		var string_ = "AA5536000F2713223000020001000A000000010000000E000000010000000100000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
	}

	public static void KET_THUC_THE_LUC_CHIEN_NHAC_NHO(Players player)
	{
		var string_ = "AA5536000F2713223000040001000A000000010000000E000000010000000100000000000000000000000000000000000000000000000000000055AA";
		var array = Converter.HexStringToByte(string_);
		if (player.Client != null)
		{
			player.Client.Send_Map_Data(array, array.Length);
		}
	}
}
