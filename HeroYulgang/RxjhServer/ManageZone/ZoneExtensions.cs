
namespace RxjhServer.ManageZone
{
    // Class hỗ trợ cập nhật các đối tượng theo Zone
    public static class ZoneExtensions
    {
 
        // Phương thức hook để sử dụng trong game
        public static void RefreshZoneVisibility(this Players player)
        {
            // <PERSON>hi người chơi di chuyển, g<PERSON><PERSON> phương thức này để cập nhật tầm nhìn
            player.GetTheReviewRangePlayers();
            player.GetReviewScopeNpc();
            player.ThuThap_VatPham_Drop_PhamVi();
        }
    }
} 