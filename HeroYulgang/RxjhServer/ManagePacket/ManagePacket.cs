using RxjhServer.HelperTools;
using System;
using HeroYulgang.Helpers;
using HeroYulgang.Services;

namespace RxjhServer;

public partial class Players
{
    	public void ManagePacket(byte[] data, int length)
	{
		int num = BitConverter.ToInt16(data, 6);
		try
		{
			if (!World.allConnectedChars.TryGetValue(SessionID, out var _))
			{
				switch (num)
				{
				case 20:
					<PERSON><PERSON><PERSON><PERSON><PERSON>(data, length);
					break;
				case 16:
					GetAListOfPeople(data, length);
					break;
				case 1:
					KetNoi_DangNhap(data, length);
					break;
				case 3:
					DangXuat(data, length);
					break;
				case 5:
					CharacterLogin(data, length);
					break;
				case 143:
					Display();
					break;
				case 56:
					KiemTraNhanVat_CoTonTaiHayKhong(data, length);
					break;
				case 30:
					XoaBoNhanVat(data, length);
					break;
				case 836:
					XacMinhThongTinDangNhapId(data, length);
					break;
				case 218:
					ChangeLineVerification(data, length);
					break;
				case 211:
					ChangeLineVerification(data, length);
					break;
				case 16666:
					IsAttackConfirmation(data, length);
					break;
				case 5638:
				case 8212:
					VersionVerification(data, length);
					break;
				}
				return;
			}
			if (num == 176)
			{
				Phat_Hien_Nhip_Tim(data, length);
				return;
			}
			if (!_connectionSucceeded)
			{
             	throw new Exception("Connection not succeeded");
			}
			switch (num)
			{
			case 14:
				ThrowItems(data, length);
				break;
			case 3:
				DangXuat(data, length);
				break;
			case 7:
				CharacterMove(data, length);
				break;
			case 8:
				Speak(data, length);
				break;
			case 9:
			{
				var num4 = (ThoiGianXacNhanTanCong = (int)DateTime.Now.Subtract(ThoiGianTanCong).TotalMilliseconds);
				if (World.ON_OFF_case_16666 != 0)
				{
					TanCongPacketXacNhan = data;
				}
				Attack(data, length);
				break;
			}
			case 11:
				PickUpItems(data, length);
				break;
			case 26:
				ChangeEquipment(data, length);
				break;
			case 22:
				UpdateConfiguration(data, length);
				break;
			case 23:
				ThanThuTamPhap_MauTim_VoHuan(data, length);
				break;
			case 16:
				GetAListOfPeople(data, length);
				break;
			case 40:
				CaptainManagement(data, length);
				break;
			case 36:
				ActionExpression(data, length);
				break;
			case 67:
				KhiCongPoint(data, length);
				break;
			case 48:
				SendTeam(data, length);
				break;
			case 50:
				CancelTheTeam(data, length);
				break;
			case 52:
				TheOpponentCancelsTheTeam(data, length);
				break;
			case 54:
				LeaveTheTeam(data, length);
				break;
			case 56:
				KiemTraNhanVat_CoTonTaiHayKhong(data, length);
				break;
			case 58:
				OpenItem(data, length);
				break;
			case 60:
				KhinhCong(data, length);
				break;
			case 42:
				ToDoi_DaoCuQuyTac_PhanPhoi(data, length);
				break;
			case 72:
				VeThanhDuongSuc(data, length);
				break;
			case 69:
				Goi_KyNang_HocTap(data, length);
				break;
			case 131:
				MissionSystem(data, length);
				break;
			case 86:
				isholdlogin = true;
				QuayLaiChonNhanVat(data, length);
				break;
			case 80:
				SynthesisSystem(data, length);
                    break;
			case 176:
                    HeartbeatDetection(data, length);
                break;
			case 177:
			case 178:
				ViewBiography(data, length);
				break;
			case 179:
				GuiDiTruyenThu(data, length);
				break;
			case 181:
				ReadingBiography(data, length);
				break;
			case 143:
				Display();
				break;
			case 144:
				OpenStore(data, length);
				break;
			case 146:
				BuyAndSellThings(data, length);
				break;
			case 148:
				WarehouseAccess(data, length);
				break;
			case 151:
				GiaoDich_LoaiLon(data, length);
				break;
			case 153:
				GiaoDich_Bo_VatPham(data, length);
				break;
			case 203:
				Shop(data, length);
				break;
			case 194:
				KyNangLienHoan2(data, length);
				break;
			case 196:
				InsufficientMagicTips();
				break;
			case 197:
				KhinhCong(data, length);
				break;
			case 199:
				KyNangLienHoan(data, length);
				break;
			case 189:
				WarehousePharmacy(data, length);
				break;
			case 209:
				NpcTransmission(data, length);
				break;
			case 206:
				IntoTheStore(data, length);
				break;
			case 321:
				RegisterNgocLienHoan(data, length);
				break;
			case 325:
				MarketPlace(data);
				break;
			case 660:
				BachBaoNew(data, length);
				break;
			case 641:
			case 645:
				BachBaoNew_ShopData(data, length);
				break;
			case 643:
				BachBaoNew_ItemDetail(data, length);
				break;
			case 655:
				BachBaonew_History_Grid(data);
				break;
			case 670:
				BachBaoNew_History(data);
				break;
			case 647:
				BachBaonew_BuyItem(data, length);
				break;
			case 668:
				PAYMENT_SHOPPING_CART(data, length);
				break;
			case 222:
				EquipmentPlusUnlock(data, length);
				break;
			case 225:
				Threading(data, length);
				break;
			case 227:
				TaoBangPhaiXacNhan(data, length);
				break;
			case 229:
				TaoBangPhai(data, length);
				break;
			case 231:
				GiaNhapBangPhai(data, length);
				break;
			case 232:
				ChaGang();
				break;
			case 234:
				GetTheDoorBadge(data, length);
				break;
			case 236:
				ApplyForDoorBadge(data, length);
				break;
			case 238:
				AssignAPosition(data, length);
				break;
			case 212:
				BachBao(data, length);
				break;
			case 345:
				OpenTheHallOfHonor(data, length);
				break;
			case 342:
				CollectMartialArtsProperty(data, length);
				break;

			case 774:
				OpenTheRoseRanking(data, length);
				break;
			case 772:
				GiftRoses(data, length);
				break;
			case 901:
				XoaBo_BaLo_VatPham(data, length);
				break;
			case 401:
				UpdateHonor(data, length);
				break;
			case 789:
				TopThreeHonors(data, length);
				break;
			case 884:
				 RearrangeItem(data);
				break;
			case 916:
				LogHelper.WriteLine(LogLevel.Error, "Case này là Khôi Phục Bang Phái đã giải tán, nhưng chưa được thêm vào !!");
				break;
			case 950:
				Click_Chuot_Phai_Xoa_Buff_HoTro(data, length);
				break;
			case 1268:
				Hoc_Skill_ThanNu(data, length);
				break;
			case 1247:
				LogoEmoji(data, length);
				break;
			case 1249:
				break;
			case 1211:
				DangKy_CongThanh(data, this);
				break;
			case 1213:
				KiemTra_CongThanh_DongMinh(data, this);
				break;
			case 1215:
				LienMinh_XinPhep(data, this);
				break;
			case 1217:
				ThienMaThanCungBangXepHang(data, this);
				break;
			case 1219:
				ChapNhan_MonPhaiKhac_LienMinh(data, this);
				break;
			case 1221:
				LienMinh_QuanLy(data, this);
				break;
			case 1223:
				KiemTra_LienMinh_XinMonPhai(data, this);
				break;
			case 1225:
				ThienMaThanCungThongTin(data, this);
				break;
			case 1237:
				Xin_Cancel_CongThanh(data, this);
				break;
			case 1242:
				CongThanh_CuongHoa_XacNhan(data, this);
				break;
			case 1240:
				KiemTra_CongThanh_Cuong_Hoa(data, this);
				break;
			case 1280:
				Event_Dragon_ToDoi_Loai.Event_HoaLong(this, data, length);
				break;
			case 1284:
				ThuocTay_Debuff_ThanNu(data, length);
				break;
			case 1293:
				DragonMove(data, length);
				break;
			case 4097:
				TruongBachDan(data, length);
				break;
			case 4099:
				Item_TaySkill(data, length);
				break;
			case 4101:
				SuDung_ThoLinhPhu(data, length);
				break;
			case 4108:
				SaveEarthTalisman(data, length);
				break;
			case 4110:
				XoaBoThoLinhPhu(data, length);
				break;
			case 4112:
				DyeHair(data, length);
				break;
			case 4115:
				OpenSymbol(data, length);
				break;
			case 4117:
				PkSwitch(data, length);
				break;
			case 4119:
				PutInTheShortcutBar(data, length);
				break;
			case 4147:
				MobileTrainingPlace(data, length);
				break;
			case 4145:
				ModifyGangAnnouncement(data, length);
				break;
			case 4156:
				ApplyForHelp(data, length);
				break;
			case 4154:
				CancelTheGangWar(data, length);
				break;
			case 4180:
				PetActionPack(data, length);
				break;
			case 4176:
				SummonPets(data, length);
				break;
			case 4160:
				MentoringSystem(data, length);
				break;
			case 4162:
				MasterApprenticeSystemRequest(data, length);
				break;
			case 4164:
				TheMentoringSystemCanceled(data, length);
				break;
			case 4166:
				DismissalOfTheMentoringSystem(data, length);
				break;
			case 4168:
				SuDo_TeachMartialArts(data, length);
				break;
			case 4186:
				SpiritBeastTransformation(data, length);
				break;
			case 4182:
				PetNamePack(data, length);
				break;
			case 5441:
				ChangeDoorService(data, length);
				break;
			case 4232:
				Click_Check_Info_NPC_Monster(data, length);
				break;
			case 4192:
				SuDo_VoCong_KiemTra(data, length);
				break;
			case 5648:
				GroupTeleport(data, length);
				break;
			case 5639:
				HairDressing(data, length);
				break;
			case 5724:
				ChuyenDoiThietBiPhuTro(data, length);
				break;
			case 5680:
				GangTeleport(data, length);
				break;
			case 5654:
				ChangeTenNhanVat(data, length);
				break;
			case 5920:
				NguyenBao_HopThanh(data, length);
				break;
			case 5922:
				NguyenBao_HopThanh2(data, length);
				break;
			case 5924:
				VeThanhDuongSuc(data, length);
				break;
			case 5914:
				HeThong_DuocPham(data, length);
				break;
			case 5954:
				XoaBo_CheTac_KyThuat(data, length);
				break;
			case 5952:
				MakeSystemActions(data, length);
				break;
			case 5936:
				MakeADecompositionSystem(data, length);
				break;
			case 5938:
				ProductionDecompositionInspection(data, length);
				break;
			case 5941:
				ProductionSystemProduction(data, length);
				break;
			case 5943:
				ProductionSystemCheck(data, length);
				break;
			case 5944:
				LearnProductionSkills(data, length);
				break;
			case 5946:
				PhanPhoi_BangPhaiVoHuan(data, length);
				break;
			case 5971:
				HaiThuoc_BanDo_NamLam(data, length);
				break;
			case 5968:
				TangHinh(data, length);
				break;
			case 6009:
				CoupleSystem(data, length);
				break;
			case 6003:
				Keo_Bua_Bao_OanTuTi(data, length);
				break;
			case 6000:
				BoiCanh_TrangTri(data, length);
				break;
			case 6150:
				ThemThangThienVoCongPoint(data, length);
				break;
			case 6144:
				TimKiem_ToDoi(data, length);
				break;
			case 6148:
				KiemTraToDoi(data, length);
				break;
			case 6433:
				ThietLap_PhoBan_DoKho(data, length);
				break;
			case 6418:
				ViewEquipment(data, length);
				break;
			case 6402:
				HopThanh_KyNgocThach(data, length);
				break;
			case 6424:
				Hop_Auto(data, length);
				break;
			case 7179:
				YuanbaoPersonalStore(data, length);
				break;
			case 6435:
				XemKhiCong(data, length);
				break;
			case 7195:
				YuanbaoPersonalStoreInquiryAgreementOpened(data, length);
				break;
			case 7194:
				YuanbaoPersonalStoreInquiryAgreement(data, length);
				break;
			case 7181:
				EnterYuanbaoPersonalStore(data, length);
				break;
			 case 8480:
                RequestMailList(data);
                break;
            case 8482:
                SendMailCod(data);
                break;
            // case 8484:
            //     ClaimMailCodItem(array);
            //    break;
            case 8486:
                AcceptMailCod(data);
                break;
            case 8488:
                RejectMailCod(data);
                break;
			case 12580:
				EquipmentRepair(data, length);
				break;
			case 12403:
				OpenChangeCharacter(data, length);
				break;
			case 8724:
				TheLucChien_HeThong(data, length);
				break;
			case 20760:
				ThienMaVeThanh(data, length);
				break;
			case 20742:
				ThienMaThanCung_MoiThamGia(data, length);
				break;
			case 20758:
				GuiDiXungQuanhPlayer_CongThanhChien_BenTrongEvent();
				break;
			case 20740:
				DaiChienHon_HeThong(data, length);
				break;
			case 20774:
				DaiChienHon_XepHang(data, length);
				break;
			case 20778:
				DaiChienHon_LuaChon_HoiSinh(data, length);
				break;
			}
			if (num != 7)
			{
				NhanVatDangDiChuyen = false;
			}
			if (num != 16666 && length != 36 && num == 9 && ThoiGianXacNhanTanCong != 0)
			{
				AttackConfirmation_Apply++;
			}
		}
		catch (Exception ex)
		{
			var array2 = new string[8]
			{
				"Manage Packet() Lỗi tại case: [",
				num.ToString(),
				"]-[",
                Client.SessionID.ToString(),
				"]-[",
                Client.ToString(),
				"] [",
				null
			};
			array2[7] = ex.ToString();
			LogHelper.WriteLine(LogLevel.Error, string.Concat(array2));
			Console.WriteLine(ex);
            Client.Dispose();
			LogHelper.WriteLine(LogLevel.Error,"Disconnected![" + AccountID + "]-[" + CharacterName + "][Mã dis 10]");
		}
	}

}