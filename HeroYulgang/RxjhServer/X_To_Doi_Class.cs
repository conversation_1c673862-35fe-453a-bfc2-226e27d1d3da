using System;
using System.Collections.Generic;
using System.Linq;
using System.Timers;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class X_To_Doi_Class : IDisposable
{
	public List<Players> tem = new();

	public ThreadSafeDictionary<int, Players> ToDoi_NguoiChoi;

	private ThreadSafeDictionary<string, X_Nguoi_Choi_Ngoai_Tuyen> ToDoi_NguoiChoi_Da_OffLine;

	public string DoiTruongTen;

	public int TeamID;

	public Players Moi_NguoiChoi;

	public Players DoiTruong;

	public int DoiNguDangCap;

	public bool RedPackage;

	public int RedPackageThoiGian;

	public int DaoCuQuyTac_PhanPhoi;

	public int PhanBoHienTai;

	public System.Timers.Timer AutomaticDisplay;

	private int GuiLoiMoi_ToDoi_DemSoLuong;

	public Players Leader => World.allConnectedChars.Values.Where((Players K) => K.CharacterName == DoiTruongTen).FirstOrDefault();

	public X_To_Doi_Class(Players Doi_Truong)
	{
		AutomaticDisplay = new(5000.0);
		AutomaticDisplay.Elapsed += AutomaticDisplayEvent;
		AutomaticDisplay.AutoReset = true;
		DoiTruongTen = Doi_Truong.CharacterName;
		DoiTruong = Doi_Truong;
		ToDoi_NguoiChoi = new();
		ToDoi_NguoiChoi.Add(Doi_Truong.SessionID, Doi_Truong);
		PhanBoHienTai = 0;
		DaoCuQuyTac_PhanPhoi = 1;
		RedPackage = false;
		RedPackageThoiGian = 0;
		DoiNguDangCap = Doi_Truong.Player_Level;
		ToDoi_NguoiChoi_Da_OffLine = new();
	}

	~X_To_Doi_Class()
	{
	}

	public Players ThanhVien_DatDuoc_TuongUng(int key)
	{
		try
		{
			var num = 0;
			foreach (var value in ToDoi_NguoiChoi.Values)
			{
				if (key == num)
				{
					return value;
				}
				num++;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Nhận tương ứng party NguoiChoi error!" + ex.Message);
		}
		return null;
	}

	public void Dispose()
	{
		try
		{
			if (ToDoi_NguoiChoi_Da_OffLine != null)
			{
				ToDoi_NguoiChoi_Da_OffLine.Clear();
			}
			if (World.WToDoi.TryGetValue(TeamID, out var _))
			{
				World.WToDoi.Remove(TeamID);
			}
			if (ToDoi_NguoiChoi != null)
			{
				foreach (var value2 in ToDoi_NguoiChoi.Values)
				{
					value2.GiaiTan_ToDoi_NhacNho();
					value2.TeamID = 0;
					value2.TeamingStage = 0;
					value2.CoupleInTeam = false;
				}
			}
			if (ToDoi_NguoiChoi != null)
			{
				ToDoi_NguoiChoi.Clear();
			}
			if (AutomaticDisplay != null)
			{
				AutomaticDisplay.Enabled = false;
				AutomaticDisplay.Close();
				AutomaticDisplay.Dispose();
				AutomaticDisplay = null;
			}
			Moi_NguoiChoi = null;
			tem = null;
			DoiNguDangCap = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng Dispose error!" + ex.Message);
		}
		finally
		{
			if (tem != null)
			{
				tem.Clear();
				tem = null;
			}
			if (ToDoi_NguoiChoi != null)
			{
				ToDoi_NguoiChoi.Dispose();
				ToDoi_NguoiChoi = null;
			}
			if (ToDoi_NguoiChoi_Da_OffLine != null)
			{
				ToDoi_NguoiChoi_Da_OffLine.Dispose();
				ToDoi_NguoiChoi_Da_OffLine = null;
			}
			if (World.WToDoi.ContainsKey(TeamID))
			{
				World.WToDoi.Remove(TeamID);
			}
			if (AutomaticDisplay != null)
			{
				AutomaticDisplay.Enabled = false;
				AutomaticDisplay.Close();
				AutomaticDisplay.Dispose();
				AutomaticDisplay = null;
			}
			Moi_NguoiChoi = null;
			DoiNguDangCap = 0;
			RedPackage = false;
			RedPackageThoiGian = 0;
		}
	}

	private void AutomaticDisplayEvent(object sender, ElapsedEventArgs e)
	{
		var num = 0;
		try
		{
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count <= 1)
			{
				Dispose();
				return;
			}
			GuiLoiMoi_ToDoi_DemSoLuong++;
			if (GuiLoiMoi_ToDoi_DemSoLuong >= 15)
			{
				GuiLoiMoi_ToDoi_DemSoLuong = 0;
				if (ToDoi_NguoiChoi_Da_OffLine != null && ToDoi_NguoiChoi_Da_OffLine.Count > 0)
				{
					List<string> list = new();
					foreach (var value2 in ToDoi_NguoiChoi_Da_OffLine.Values)
					{
						var players = World.KiemTra_Ten_NguoiChoi(value2.UserName);
						if (players == null)
						{
							continue;
						}
						if (value2.TeamID == TeamID)
						{
							if (ToDoi_NguoiChoi.Count >= World.Gioi_han_so_nguoi_vao_party)
							{
								continue;
							}
							if (DoiTruong.FindPlayers(1000, players))
							{
								if (players.TeamingStage == 0 && ToDoi_NguoiChoi.Count < World.Gioi_han_so_nguoi_vao_party && players.TeamID == 0)
								{
									var array = Converter.HexStringToByte("AA5528002C0130000600010001002D010000000000000000000000000000000000000000000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(players.SessionID), 0, array, 14, 2);
									DoiTruong.SendTeam(array, array.Length);
								}
								else if (players.TeamingStage == 1)
								{
									var array2 = Converter.HexStringToByte("AA5512002C013200040001002C01000000000000000055AA");
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array2, 4, 2);
									System.Buffer.BlockCopy(BitConverter.GetBytes(DoiTruong.SessionID), 0, array2, 12, 2);
									DoiTruong.CancelTheTeam(array2, array2.Length);
								}
								else if (players.TeamingStage == 2 && !list.Contains(players.AccountID))
								{
									list.Add(players.AccountID);
								}
							}
							else
							{
								players.HeThongNhacNho("Hiệp khách lạc bước quá xa tổ đội, đồng môn của ngươi đang ở [" + X_Toa_Do_Class.getmapname(DoiTruong.NhanVatToaDo_BanDo) + "] - tọa độ: [" + DoiTruong.NhanVatToaDo_X + "," + DoiTruong.NhanVatToaDo_Y + "]!", 10, "Thiên cơ các");
							}
						}
						else if (!list.Contains(players.AccountID))
						{
							list.Add(players.AccountID);
						}
					}
					if (list.Count > 0)
					{
						foreach (var item in list)
						{
							if (ToDoi_NguoiChoi_Da_OffLine.ContainsKey(item))
							{
								ToDoi_NguoiChoi_Da_OffLine.Remove(item);
							}
						}
					}
					list.Clear();
				}
			}
			if (RedPackage)
			{
				RedPackageThoiGian -= 3000;
				if (RedPackageThoiGian <= 0)
				{
					RedPackage = false;
					RedPackageThoiGian = 0;
				}
			}
			else
			{
				RedPackage = false;
				RedPackageThoiGian = 0;
			}
			Players value;
			if (ToDoi_NguoiChoi != null)
			{
				foreach (var value3 in ToDoi_NguoiChoi.Values)
				{
					if (World.allConnectedChars.TryGetValue(value3.SessionID, out value))
					{
						value3.ShowPlayers();
						if (RedPackage && RedPackageThoiGian > 0)
						{
							if (value3.AppendStatusList != null && !value3.GetAddState(**********))
							{
								X_Them_Vao_Trang_Thai_Loai x_Them_Vao_Trang_Thai_Loai = new(value3, RedPackageThoiGian, **********, 0);
								value3.AppendStatusList.Add(x_Them_Vao_Trang_Thai_Loai.FLD_PID, x_Them_Vao_Trang_Thai_Loai);
								value3.StatusEffect(BitConverter.GetBytes(**********), 1, RedPackageThoiGian);
							}
						}
						else if (value3.AppendStatusList != null && value3.GetAddState(**********))
						{
							value3.AppendStatusList[**********].ThoiGianKetThucSuKien();
						}
					}
					else if (tem != null && !tem.Contains(value3))
					{
						tem.Add(value3);
					}
				}
			}
			if (tem != null)
			{
				foreach (var item2 in tem)
				{
					if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.TryGetValue(item2.SessionID, out value))
					{
						ToDoi_NguoiChoi.Remove(item2.SessionID);
						item2.TeamID = 0;
						item2.TeamingStage = 0;
					}
				}
			}
			if (tem.Count > 0)
			{
				tem.Clear();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Auto Party tạo nhóm Tổ Đội bị lỗi - [" + num + "] - [" + ex.Message);
		}
	}

	public void UyQuyen_DoiTruong(Players Old_DoiTruong, Players New_DoiTruong)
	{
		try
		{
			DoiTruongTen = New_DoiTruong.CharacterName;
			DoiTruong = New_DoiTruong;
			foreach (var value in ToDoi_NguoiChoi.Values)
			{
				value.UyQuyen_DoiTruong_NhacNho(Old_DoiTruong, New_DoiTruong);
				value.ShowPlayers();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Chuyển quyền Key đội trưởng Lỗi !! - " + ex.Message);
		}
	}

	public void ThamGiaThanhVienNhom_NhacNho(Players NguoiChoi)
	{
		try
		{
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				foreach (var value2 in ToDoi_NguoiChoi.Values)
				{
					if (value2.CharacterName == NguoiChoi.FLD_Couple)
					{
						NguoiChoi.CoupleInTeam = true;
						value2.CoupleInTeam = true;
						break;
					}
				}
			}
			foreach (var value3 in ToDoi_NguoiChoi.Values)
			{
				if (NguoiChoi != value3)
				{
					value3.GiaNhap_ToDoi_NhacNho(NguoiChoi);
					NguoiChoi.GiaNhap_ToDoi_NhacNho(value3);
				}
				value3.ShowPlayers();
			}
			if (ToDoi_NguoiChoi.Count >= 2)
			{
				AutomaticDisplay.Enabled = true;
			}
			if (ToDoi_NguoiChoi_Da_OffLine.TryGetValue(NguoiChoi.AccountID, out var _))
			{
				ToDoi_NguoiChoi_Da_OffLine.Remove(NguoiChoi.AccountID);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "ToDoi tốt bụng ThamGiaThanhVienNhom_NhacNho error!" + ex.Message);
		}
	}

	public void DangXuat(Players NguoiChoi, int Exit_ID)
	{
		var num = 0;
		try
		{
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.ContainsKey(NguoiChoi.SessionID))
			{
				ToDoi_NguoiChoi.Remove(NguoiChoi.SessionID);
				if (Exit_ID == 1 && ToDoi_NguoiChoi.Count >= 2 && !ToDoi_NguoiChoi_Da_OffLine.ContainsKey(NguoiChoi.AccountID))
				{
					ToDoi_NguoiChoi_Da_OffLine.Add(NguoiChoi.AccountID, new()
					{
						TeamID = NguoiChoi.TeamID,
						UserName = NguoiChoi.CharacterName
					});
				}
			}
			if (NguoiChoi.GetAddState(**********))
			{
				NguoiChoi.AppendStatusList[**********].ThoiGianKetThucSuKien();
			}
			num = 1;
			if (NguoiChoi.FLD_Couple.Length != 0)
			{
				NguoiChoi.CoupleInTeam = false;
				num = 3;
				if (ToDoi_NguoiChoi != null)
				{
					foreach (var value in ToDoi_NguoiChoi.Values)
					{
						num = 4;
						if (value.CharacterName == NguoiChoi.FLD_Couple)
						{
							num = 5;
							value.CoupleInTeam = false;
							num = 6;
							break;
						}
					}
				}
			}
			num = 7;
			if (ToDoi_NguoiChoi != null && ToDoi_NguoiChoi.Count >= 2)
			{
				num = 8;
				if (DoiTruongTen!= NguoiChoi.CharacterName)
				{
					foreach (var value2 in ToDoi_NguoiChoi.Values)
					{
						num = 9;
						value2.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						num = 10;
						value2.ShowPlayers();
						num = 11;
					}
				}
				else
				{
					var flag = true;
					foreach (var value3 in ToDoi_NguoiChoi.Values)
					{
						if (flag)
						{
							num = 12;
							UyQuyen_DoiTruong(NguoiChoi, value3);
							flag = false;
						}
						num = 15;
						value3.HeThongNhacNho("Ấn chương đội trưởng đã được truyền cho hiệp sĩ " + DoiTruongTen + "!", 8, "Thiên Cơ Lệnh");
						value3.RoiKhoi_ToDoi_NhacNho(NguoiChoi);
						value3.ShowPlayers();
						num = 16;
					}
				}
			}
			else
			{
				num = 17;
				Dispose();
			}
			num = 18;
			NguoiChoi.this_RoiKhoi_ToDoi_NhacNho();
			num = 19;
			NguoiChoi.TeamID = 0;
			num = 20;
			NguoiChoi.TeamingStage = 0;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tổ Đội Party Thoát bị error! |" + num + "|" + ex.Message);
		}
		finally
		{
			NguoiChoi.TeamID = 0;
			NguoiChoi.TeamingStage = 0;
		}
	}
}
