using System;
using System.Net.Sockets;
using System.Text;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class WGHandler : WGSockClient
{
	private string string_0;

	public string ServerId
	{
		get
		{
			return string_0;
		}
		set
		{
			string_0 = value;
		}
	}

	public void Logout()
	{
		LogHelper.WriteLine(LogLevel.Info, "<PERSON><PERSON><PERSON> chủ ngắt kết nối ID：" + ServerId);
	}

	public WGHandler(Socket socket_0, RemoveWGClientDelegate removeWGClientDelegate_0)
		: base(socket_0, removeWGClientDelegate_0)
	{
	}

	public override byte[] ProcessDataReceived(byte[] data, int length)
	{
		try
		{
			var num = 0;
			if (170 == data[0] && 102 == data[1])
			{
				var array = new byte[4];
				System.Buffer.BlockCopy(data, 2, array, 0, 4);
				var num2 = BitConverter.ToInt32(array, 0);
				if (length < num2 + 6)
				{
					return null;
				}
				while (true)
				{
					var array2 = new byte[num2];
					System.Buffer.BlockCopy(data, num + 6, array2, 0, num2);
					num += num2 + 6;
					DataReceived(array2, num2);
					if (num >= length || data[num] != 170 || data[num + 1] != 102)
					{
						break;
					}
					System.Buffer.BlockCopy(data, num + 2, array, 0, 4);
					num2 = BitConverter.ToInt16(array, 0);
				}
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Error, "Nhầm lẫn Package： " + data[0] + " " + data[1]);
			}
			return null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "error： " + ex.Message);
			Console.WriteLine(ex.Message);
			Console.WriteLine(ex.Source);
			Console.WriteLine(ex.StackTrace);
			return null;
		}
	}

	public byte[] DataReceived(byte[] byte_0, int int_0)
	{
		try
		{
			var @string = Encoding.Default.GetString(byte_0);
			LogHelper.WriteLine(LogLevel.Info, "Account verification received： " + @string);
			var array = @string.Split('|');
			var text = array[0];
			if (text != null && text == "服务器连接Login")
			{
				ServerId = array[1];
				World.PortReplacementNotice(ServerId);
				LogHelper.WriteLine(LogLevel.Info, "Người phục vụ Connection Succeeded ID： " + array[1]);
			}
			return null;
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "error 112233： " + ex.Message);
			Console.WriteLine(ex.Message);
			Console.WriteLine(ex.Source);
			Console.WriteLine(ex.StackTrace);
			return null;
		}
	}
}
