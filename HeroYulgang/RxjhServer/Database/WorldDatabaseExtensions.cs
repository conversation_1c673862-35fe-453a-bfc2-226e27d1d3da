using System;
using System.Threading.Tasks;

namespace RxjhServer.Database
{
    public static class WorldDatabaseExtensions
    {
        private static DatabaseService _databaseService;

        public static void InitializeDatabaseService()
        {
            try
            {
                // Khởi tạo cơ sở dữ liệu
                var initializer = new DatabaseInitializer();
                initializer.Initialize();

                // Khởi tạo dịch vụ cơ sở dữ liệu
                _databaseService = new DatabaseService();

                Console.WriteLine("Khởi tạo dịch vụ cơ sở dữ liệu thành công");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi khởi tạo dịch vụ cơ sở dữ liệu: {ex.Message}");
                throw;
            }
        }

        public static async Task InitializeDatabaseServiceAsync()
        {
            try
            {
                // Khởi tạo cơ sở dữ liệu
                var initializer = new DatabaseInitializer();
                await initializer.InitializeAsync();

                // Khởi tạo dịch vụ cơ sở dữ liệu
                _databaseService = new DatabaseService();

                Console.WriteLine("Khởi tạo dịch vụ cơ sở dữ liệu thành công");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi khởi tạo dịch vụ cơ sở dữ liệu: {ex.Message}");
                throw;
            }
        }

        public static DatabaseService GetDatabaseService()
        {
            if (_databaseService == null)
            {
                InitializeDatabaseService();
            }
            return _databaseService;
        }
    }
}
