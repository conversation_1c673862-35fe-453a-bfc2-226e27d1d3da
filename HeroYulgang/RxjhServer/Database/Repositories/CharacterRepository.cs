using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Game;

namespace RxjhServer.Database.Repositories
{
    public class CharacterRepository
    {
        private readonly GameDbContext _context;

        public CharacterRepository(GameDbContext context)
        {
            _context = context;
        }

        public async Task<TblXwwlChar> GetCharacterAsync(int charId)
        {
            return await _context.TblXwwlChars.FindAsync(charId);
        }

        public async Task<List<TblXwwlChar>> GetCharactersByAccountAsync(string accountId)
        {
            return await _context.TblXwwlChars
                .Where(c => c.FldId == accountId)
                .ToListAsync();
        }

        public async Task<TblXwwlChar> GetCharacterByNameAsync(string name)
        {
            return await _context.TblXwwlChars
                .FirstOrDefaultAsync(c => c.FldName == name);
        }

        public async Task<bool> CreateCharacterAsync(TblXwwlChar character)
        {
            try
            {
                await _context.TblXwwlChars.AddAsync(character);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateCharacterAsync(TblXwwlChar character)
        {
            try
            {
                _context.TblXwwlChars.Update(character);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteCharacterAsync(int charId)
        {
            try
            {
                var character = await _context.TblXwwlChars.FindAsync(charId);
                if (character != null)
                {
                    _context.TblXwwlChars.Remove(character);
                    await _context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> IsNameExistsAsync(string name)
        {
            return await _context.TblXwwlChars.AnyAsync(c => c.FldName == name);
        }

        public async Task<List<TblXwwlChar>> GetAllCharactersAsync()
        {
            return await _context.TblXwwlChars.ToListAsync();
        }
    }
}
