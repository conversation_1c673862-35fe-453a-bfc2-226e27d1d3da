using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Account;

namespace RxjhServer.Database.Repositories
{
    public class AccountRepository
    {
        private readonly AccountDbContext _context;

        public AccountRepository(AccountDbContext context)
        {
            _context = context;
        }

        public async Task<TblAccount?> GetAccountAsync(string username)
        {
            return await _context.TblAccounts.FindAsync(username);
        }

        public async Task<bool> ValidateAccountAsync(string username, string password)
        {
            var account = await _context.TblAccounts.FindAsync(username);
            return account != null && account.FldPassword == password;
        }

        public async Task<bool> CreateAccountAsync(TblAccount account)
        {
            try
            {
                await _context.TblAccounts.AddAsync(account);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> UpdateAccountAsync(TblAccount account)
        {
            try
            {
                _context.TblAccounts.Update(account);
                await _context.SaveChangesAsync();
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<bool> DeleteAccountAsync(string username)
        {
            try
            {
                var account = await _context.TblAccounts.FindAsync(username);
                if (account != null)
                {
                    _context.TblAccounts.Remove(account);
                    await _context.SaveChangesAsync();
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        public async Task<List<TblAccount>> GetAllAccountsAsync()
        {
            return await _context.TblAccounts.ToListAsync();
        }
    }
}
