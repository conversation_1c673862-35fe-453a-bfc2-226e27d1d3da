using Microsoft.EntityFrameworkCore;
using System;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using HeroYulgang.Database.Entities.Public;

namespace RxjhServer.Database
{
    public class DatabaseInitializer
    {
        private readonly AccountDbContext _accountDbContext;
        private readonly GameDbContext _gameDbContext;
        private readonly PublicDbContext _publicDbContext;

        public DatabaseInitializer()
        {
            _accountDbContext = new AccountDbContext();
            _gameDbContext = new GameDbContext();
            _publicDbContext = new PublicDbContext();
        }

        public async Task InitializeAsync()
        {
            try
            {
                // Đảm bảo cơ sở dữ liệu được tạo
                await _accountDbContext.Database.EnsureCreatedAsync();
                await _gameDbContext.Database.EnsureCreatedAsync();
                await _publicDbContext.Database.EnsureCreatedAsync();

                Console.WriteLine("Khởi tạo cơ sở dữ liệu thành công.");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi khởi tạo cơ sở dữ liệu: {ex.Message}");
                throw;
            }
        }

        public void Initialize()
        {
            InitializeAsync().Wait();
        }

        public void Dispose()
        {
            _accountDbContext?.Dispose();
            _gameDbContext?.Dispose();
            _publicDbContext?.Dispose();
        }
    }
}
