using System.Data;
using System.Data.SqlClient;
using Microsoft.Data.SqlClient;

namespace RxjhServer.Database
{
    /// <summary>
    /// Lớp tương thích với SqlDBA cũ, sử dụng Entity Framework Core
    /// </summary>
    public static class SqlDBA
    {
        /// <summary>
        /// Tạo tham số đầu vào cho SQL
        /// </summary>
        public static SqlParameter MakeInParam(string paramName, SqlDbType dbType, int size, object value)
        {
            return new SqlParameter
            {
                ParameterName = paramName,
                SqlDbType = dbType,
                Size = size,
                Direction = ParameterDirection.Input,
                Value = value
            };
        }

        /// <summary>
        /// Tạo tham số đầu ra cho SQL
        /// </summary>
        public static SqlParameter MakeOutParam(string paramName, SqlDbType dbType, int size)
        {
            return new SqlParameter
            {
                ParameterName = paramName,
                SqlDbType = dbType,
                Size = size,
                Direction = ParameterDirection.Output
            };
        }

        /// <summary>
        /// Tạo tham số đầu vào/đầu ra cho SQL
        /// </summary>
        public static SqlParameter MakeInOutParam(string paramName, SqlDbType dbType, int size, object value)
        {
            return new SqlParameter
            {
                ParameterName = paramName,
                SqlDbType = dbType,
                Size = size,
                Direction = ParameterDirection.InputOutput,
                Value = value
            };
        }

        /// <summary>
        /// Tạo tham số trả về cho SQL
        /// </summary>
        public static SqlParameter MakeReturnParam(string paramName, SqlDbType dbType, int size)
        {
            return new SqlParameter
            {
                ParameterName = paramName,
                SqlDbType = dbType,
                Size = size,
                Direction = ParameterDirection.ReturnValue
            };
        }
    }
}
