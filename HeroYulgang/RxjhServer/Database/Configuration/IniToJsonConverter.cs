using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Text.Json;

namespace RxjhServer.Database.Configuration
{
    public static class IniToJsonConverter
    {
        public static void ConvertIniToJson(string iniFilePath, string jsonFilePath)
        {
            try
            {
                // Đọc file INI
                var iniContent = File.ReadAllText(iniFilePath);
                var iniLines = iniContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                // Phân tích file INI
                var jsonObject = new Dictionary<string, Dictionary<string, string>>();
                string currentSection = "";

                foreach (var line in iniLines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                    {
                        continue; // Bỏ qua dòng trống hoặc comment
                    }

                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        // Đây là một section mới
                        currentSection = trimmedLine.Substring(1, trimmedLine.Length - 2);
                        if (!jsonObject.ContainsKey(currentSection))
                        {
                            jsonObject[currentSection] = new Dictionary<string, string>();
                        }
                    }
                    else
                    {
                        // Đây là một cặp key-value
                        var parts = trimmedLine.Split(new[] { '=' }, 2);
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();

                            if (string.IsNullOrEmpty(currentSection))
                            {
                                // Nếu không có section, tạo một section mặc định
                                if (!jsonObject.ContainsKey("Default"))
                                {
                                    jsonObject["Default"] = new Dictionary<string, string>();
                                }
                                jsonObject["Default"][key] = value;
                            }
                            else
                            {
                                jsonObject[currentSection][key] = value;
                            }
                        }
                    }
                }

                // Chuyển đổi sang JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true
                };
                var jsonString = JsonSerializer.Serialize(jsonObject, jsonOptions);

                // Ghi file JSON
                File.WriteAllText(jsonFilePath, jsonString, Encoding.UTF8);

                Console.WriteLine($"Đã chuyển đổi thành công từ {iniFilePath} sang {jsonFilePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi chuyển đổi từ INI sang JSON: {ex.Message}");
                throw;
            }
        }

        public static void ConvertGSConfigToAppSettings(string iniFilePath, string jsonFilePath)
        {
            try
            {
                // Đọc file INI
                var iniContent = File.ReadAllText(iniFilePath);
                var iniLines = iniContent.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

                // Tạo cấu trúc JSON
                var jsonObject = new Dictionary<string, object>();
                var connectionStrings = new Dictionary<string, string>();
                var serverSettings = new Dictionary<string, object>();

                // Phân tích file INI
                string currentSection = "";

                foreach (var line in iniLines)
                {
                    var trimmedLine = line.Trim();
                    if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                    {
                        continue; // Bỏ qua dòng trống hoặc comment
                    }

                    if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                    {
                        // Đây là một section mới
                        currentSection = trimmedLine.Substring(1, trimmedLine.Length - 2);
                    }
                    else
                    {
                        // Đây là một cặp key-value
                        var parts = trimmedLine.Split(new[] { '=' }, 2);
                        if (parts.Length == 2)
                        {
                            var key = parts[0].Trim();
                            var value = parts[1].Trim();

                            if (currentSection == "Database")
                            {
                                // Xử lý cấu hình cơ sở dữ liệu
                                if (key == "Server")
                                {
                                    var server = value;
                                    var userId = GetValueFromIni(iniLines, "Database", "UserId", "sa");
                                    var password = GetValueFromIni(iniLines, "Database", "Password", "123123");
                                    
                                    // Tạo connection string cho từng cơ sở dữ liệu
                                    connectionStrings["AccountDb"] = $"Server={server};Database=heroAccount;User Id={userId};Password={password};TrustServerCertificate=True;";
                                    connectionStrings["GameDb"] = $"Server={server};Database=heroGame;User Id={userId};Password={password};TrustServerCertificate=True;";
                                    connectionStrings["PublicDb"] = $"Server={server};Database=heroPublic;User Id={userId};Password={password};TrustServerCertificate=True;";
                                }
                            }
                            else if (currentSection == "Server")
                            {
                                // Xử lý cấu hình server
                                if (key == "ServerName")
                                {
                                    serverSettings["ServerName"] = value;
                                }
                                else if (key == "ServerID")
                                {
                                    if (int.TryParse(value, out int serverId))
                                    {
                                        serverSettings["ServerID"] = serverId;
                                    }
                                    else
                                    {
                                        serverSettings["ServerID"] = 1;
                                    }
                                }
                                else if (key == "GameServerPort")
                                {
                                    if (int.TryParse(value, out int port))
                                    {
                                        serverSettings["GameServerPort"] = port;
                                    }
                                    else
                                    {
                                        serverSettings["GameServerPort"] = 7000;
                                    }
                                }
                                else if (key == "MaximumOnline")
                                {
                                    if (int.TryParse(value, out int maxOnline))
                                    {
                                        serverSettings["MaximumOnline"] = maxOnline;
                                    }
                                    else
                                    {
                                        serverSettings["MaximumOnline"] = 1000;
                                    }
                                }
                                else if (key == "AutomaticConnectionTime")
                                {
                                    if (int.TryParse(value, out int time))
                                    {
                                        serverSettings["AutomaticConnectionTime"] = time;
                                    }
                                    else
                                    {
                                        serverSettings["AutomaticConnectionTime"] = 60;
                                    }
                                }
                                else
                                {
                                    // Các cấu hình khác
                                    serverSettings[key] = value;
                                }
                            }
                        }
                    }
                }

                // Thêm vào JSON object
                jsonObject["ConnectionStrings"] = connectionStrings;
                jsonObject["ServerSettings"] = serverSettings;

                // Chuyển đổi sang JSON
                var jsonOptions = new JsonSerializerOptions
                {
                    WriteIndented = true
                };
                var jsonString = JsonSerializer.Serialize(jsonObject, jsonOptions);

                // Ghi file JSON
                File.WriteAllText(jsonFilePath, jsonString, Encoding.UTF8);

                Console.WriteLine($"Đã chuyển đổi thành công từ {iniFilePath} sang {jsonFilePath}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi chuyển đổi từ INI sang JSON: {ex.Message}");
                throw;
            }
        }

        private static string GetValueFromIni(string[] iniLines, string section, string key, string defaultValue)
        {
            bool inSection = false;
            foreach (var line in iniLines)
            {
                var trimmedLine = line.Trim();
                if (string.IsNullOrEmpty(trimmedLine) || trimmedLine.StartsWith(";") || trimmedLine.StartsWith("#"))
                {
                    continue; // Bỏ qua dòng trống hoặc comment
                }

                if (trimmedLine.StartsWith("[") && trimmedLine.EndsWith("]"))
                {
                    // Đây là một section mới
                    var currentSection = trimmedLine.Substring(1, trimmedLine.Length - 2);
                    inSection = (currentSection == section);
                }
                else if (inSection)
                {
                    // Đây là một cặp key-value trong section cần tìm
                    var parts = trimmedLine.Split(new[] { '=' }, 2);
                    if (parts.Length == 2)
                    {
                        var currentKey = parts[0].Trim();
                        var value = parts[1].Trim();
                        if (currentKey == key)
                        {
                            return value;
                        }
                    }
                }
            }
            return defaultValue;
        }
    }
}
