namespace RxjhServer.Database.Configuration
{
    public class DatabaseConfig
    {
        public static string AccountConnectionString => ConfigurationManager.GetConnectionString("AccountDb");
        public static string GameConnectionString => ConfigurationManager.GetConnectionString("GameDb");
        public static string PublicConnectionString => ConfigurationManager.GetConnectionString("PublicDb");

        public static string ServerName => ConfigurationManager.GetSetting("ServerSettings", "ServerName");
        public static int ServerId => ConfigurationManager.GetSettingInt("ServerSettings", "ServerID", 1);
        public static int GameServerPort => ConfigurationManager.GetSettingInt("ServerSettings", "GameServerPort", 7000);
        public static int MaximumOnline => ConfigurationManager.GetSettingInt("ServerSettings", "MaximumOnline", 1000);
        public static int AutomaticConnectionTime => ConfigurationManager.GetSettingInt("ServerSettings", "AutomaticConnectionTime", 60);
    }
}
