using System;
using System.Data;
using HeroYulgang.Database.Entities.Game;

namespace RxjhServer.Database.Utilities
{
    public static class DataConverter
    {
        /// <summary>
        /// <PERSON><PERSON><PERSON><PERSON> đ<PERSON><PERSON> từ DataRow sang Character
        /// </summary>
        // public static TblXwwl<PERSON>har ToCharacter(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new TblXwwlChar()
        //     {
        //         Id = Convert.ToInt32(row["ID"]),
        //         FldId = row["FLD_ID"].ToString(),
        //         FldName = row["FLD_Name"].ToString(),
        //         FldLevel = Convert.ToInt32(row["FLD_Level"]),
        //         FldJob = Convert.ToInt32(row["FLD_Job"]),
        //         FldSe = Convert.ToInt32(row["FLD_Sex"]),
        //         F = Convert.ToInt32(row["FLD_MapID"]),
        //         PosX = Convert.ToSingle(row["FLD_X"]),
        //         PosY = Convert.ToSingle(row["FLD_Y"]),
        //         GuildId = Convert.ToInt32(row["FLD_FactionID"]),
        //         Gold = Convert.ToInt64(row["FLD_Money"]),
        //         Exp = Convert.ToInt64(row["FLD_EXP"]),
        //         Hp = Convert.ToInt32(row["FLD_HP"]),
        //         Mp = Convert.ToInt32(row["FLD_MP"]),
        //         Sp = Convert.ToInt32(row["FLD_SP"]),
        //         Data = row["FLD_DATA"] as byte[]
        //     };
        // }
        //
        // /// <summary>
        // /// Chuyển đổi từ DataRow sang Item
        // /// </summary>
        // public static Item ToItem(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new Item
        //     {
        //         ItemId = Convert.ToInt32(row["FLD_ID"]),
        //         CharId = Convert.ToInt32(row["FLD_CharID"]),
        //         ItemType = Convert.ToInt32(row["FLD_Type"]),
        //         Position = Convert.ToInt32(row["FLD_Position"]),
        //         ItemName = row["FLD_Name"].ToString(),
        //         Count = Convert.ToInt32(row["FLD_Count"]),
        //         Quality = Convert.ToInt32(row["FLD_Quality"]),
        //         Durability = Convert.ToInt32(row["FLD_Durability"]),
        //         Bind = Convert.ToInt32(row["FLD_Bind"]),
        //         Data = row["FLD_DATA"] as byte[]
        //     };
        // }
        //
        // /// <summary>
        // /// Chuyển đổi từ DataRow sang Guild
        // /// </summary>
        // public static Guild ToGuild(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new Guild
        //     {
        //         GuildId = Convert.ToInt32(row["FLD_ID"]),
        //         Name = row["FLD_Name"].ToString(),
        //         LeaderId = Convert.ToInt32(row["FLD_MasterID"]),
        //         Level = Convert.ToInt32(row["FLD_Level"]),
        //         Money = Convert.ToInt64(row["FLD_Money"]),
        //         CreateTime = Convert.ToDateTime(row["FLD_CreateTime"]),
        //         Data = row["FLD_DATA"] as byte[]
        //     };
        // }
        //
        // /// <summary>
        // /// Chuyển đổi từ DataRow sang GuildMember
        // /// </summary>
        // public static GuildMember ToGuildMember(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new GuildMember
        //     {
        //         GuildId = Convert.ToInt32(row["FLD_FactionID"]),
        //         CharId = Convert.ToInt32(row["FLD_CharID"]),
        //         Name = row["FLD_Name"].ToString(),
        //         Level = Convert.ToInt32(row["FLD_Level"]),
        //         Job = Convert.ToInt32(row["FLD_Job"]),
        //         Rank = Convert.ToInt32(row["FLD_Rank"]),
        //         JoinTime = Convert.ToDateTime(row["FLD_JoinTime"])
        //     };
        // }
        //
        // /// <summary>
        // /// Chuyển đổi từ DataRow sang Account
        // /// </summary>
        // public static Account ToAccount(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new Account
        //     {
        //         Username = row["Username"].ToString(),
        //         Password = row["Password"].ToString(),
        //         Question = row["Question"].ToString(),
        //         Answer = row["Answer"].ToString(),
        //         TruePass = row["TruePass"].ToString(),
        //         Admin = Convert.ToInt32(row["Admin"]),
        //         State = Convert.ToInt32(row["State"]),
        //         EndDate = row["EndDate"] as DateTime?,
        //         BanEndDate = row["BanEndDate"] as DateTime?
        //     };
        // }
        //
        // /// <summary>
        // /// Chuyển đổi từ DataRow sang ServerInfo
        // /// </summary>
        // public static ServerInfo ToServerInfo(DataRow row)
        // {
        //     if (row == null)
        //     {
        //         return null;
        //     }
        //
        //     return new ServerInfo
        //     {
        //         ServerId = Convert.ToInt32(row["FLD_ID"]),
        //         ServerName = row["FLD_Name"].ToString(),
        //         ServerIp = row["FLD_IP"].ToString(),
        //         ServerPort = Convert.ToInt32(row["FLD_Port"]),
        //         ServerState = Convert.ToInt32(row["FLD_State"])
        //     };
        // }
    }
}
