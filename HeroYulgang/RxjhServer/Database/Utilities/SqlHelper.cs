using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Threading.Tasks;
using Microsoft.Data.SqlClient;

namespace RxjhServer.Database.Utilities
{
    public static class SqlHelper
    {
        /// <summary>
        /// Thực thi truy vấn SQL và trả về DataTable
        /// </summary>
        public static async Task<DataTable> ExecuteQueryAsync(DbContext context, string sql, params SqlParameter[] parameters)
        {
            var dataTable = new DataTable();
            
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = sql;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        dataTable.Load(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện truy vấn ExecuteQueryAsync: {ex.Message} {sql}");
                throw;
            }
            
            return dataTable;
        }

        /// <summary>
        /// Thực thi truy vấn SQL và trả về giá trị đơn
        /// </summary>
        public static async Task<object> ExecuteScalarAsync(DbContext context, string sql, params SqlParameter[] parameters)
        {
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = sql;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    return await command.ExecuteScalarAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện truy vấn ExecuteScalarAsync: {ex.Message} {sql}");
                throw;
            }
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu
        /// </summary>
        public static async Task<int> ExecuteNonQueryAsync(DbContext context, string sql, params SqlParameter[] parameters)
        {
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = sql;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    return await command.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện truy vấn ExecuteNonQueryAsync: {ex.Message} {sql}");
                throw;
            }
        }

        /// <summary>
        /// Thực thi stored procedure và trả về DataTable
        /// </summary>
        public static async Task<DataTable> ExecuteStoredProcedureAsync(DbContext context, string procedureName, params SqlParameter[] parameters)
        {
            var dataTable = new DataTable();
            
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = procedureName;
                    command.CommandType = CommandType.StoredProcedure;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        dataTable.Load(reader);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện stored procedure: {ex.Message}");
                throw;
            }
            
            return dataTable;
        }

        /// <summary>
        /// Thực thi stored procedure và trả về giá trị đơn
        /// </summary>
        public static async Task<object> ExecuteStoredProcedureScalarAsync(DbContext context, string procedureName, params SqlParameter[] parameters)
        {
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = procedureName;
                    command.CommandType = CommandType.StoredProcedure;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    return await command.ExecuteScalarAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện stored procedure: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Thực thi stored procedure không trả về dữ liệu
        /// </summary>
        public static async Task<int> ExecuteStoredProcedureNonQueryAsync(DbContext context, string procedureName, params SqlParameter[] parameters)
        {
            try
            {
                var connection = context.Database.GetDbConnection();
                using (var command = connection.CreateCommand())
                {
                    command.CommandText = procedureName;
                    command.CommandType = CommandType.StoredProcedure;
                    if (parameters != null)
                    {
                        foreach (var parameter in parameters)
                        {
                            command.Parameters.Add(parameter);
                        }
                    }
                    
                    if (connection.State != ConnectionState.Open)
                    {
                        await connection.OpenAsync();
                    }
                    
                    return await command.ExecuteNonQueryAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi thực hiện stored procedure: {ex.Message}");
                throw;
            }
        }
    }
}
