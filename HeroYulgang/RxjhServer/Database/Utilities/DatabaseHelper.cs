using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Account;
using HeroYulgang.Database.Entities.Game;
using HeroYulgang.Database.Entities.Public;

namespace RxjhServer.Database.Utilities
{
    public static class DatabaseHelper
    {
        /// <summary>
        /// Lấy danh sách nhân vật của tài khoản
        /// </summary>
        public static async Task<List<TblXwwlChar>> GetCharactersByAccountAsync(string accountId)
        {
            using (var context = new GameDbContext())
            {
                return await context.TblXwwlChars
                    .Where(c => c.FldId == accountId)
                    .ToListAsync();
            }
        }


        /// <summary>
        /// Lấy thông tin bang phái
        /// </summary>
        public static async Task<TblXwwlGuild> GetGuildAsync(int guildId)
        {
            using (var context = new GameDbContext())
            {
                return await context.TblXwwlGuilds
                    .FindAsync(guildId);
            }
        }

        /// <summary>
        /// L<PERSON>y danh sách thành viên bang phái
        /// </summary>
        public static async Task<List<TblXwwlGuild>> GetGuildMembersAsync(string guildName)
        {
            using (var context = new GameDbContext())
            {
                return await context.TblXwwlGuilds
                    .Where(m => m.GName == guildName)
                    .ToListAsync();
            }
        }

        /// <summary>
        /// Lấy thông tin tài khoản
        /// </summary>
        public static async Task<TblAccount> GetAccountAsync(string username)
        {
            using (var context = new AccountDbContext())
            {
                return await context.TblAccounts
                    .FindAsync(username);
            }
        }


        /// <summary>
        /// Thực thi truy vấn SQL và trả về DataTable
        /// </summary>
        public static async Task<DataTable> ExecuteQueryAsync(string sql, string databaseName = "GameDb")
        {
            switch (databaseName.ToLower())
            {
                case "accountdb":
                case "account":
                    using (var context = new AccountDbContext())
                    {
                        return await SqlHelper.ExecuteQueryAsync(context, sql);
                    }
                
                case "gamedb":
                case "game":
                case "gameserver":
                    using (var context = new GameDbContext())
                    {
                        return await SqlHelper.ExecuteQueryAsync(context, sql);
                    }
                
                case "publicdb":
                case "public":
                    using (var context = new PublicDbContext())
                    {
                        return await SqlHelper.ExecuteQueryAsync(context, sql);
                    }
                
                default:
                    throw new ArgumentException($"Không hỗ trợ cơ sở dữ liệu: {databaseName}");
            }
        }

        /// <summary>
        /// Thực thi lệnh SQL không trả về dữ liệu
        /// </summary>
        public static async Task<int> ExecuteNonQueryAsync(string sql, string databaseName = "GameDb")
        {
            switch (databaseName.ToLower())
            {
                case "accountdb":
                case "account":
                    using (var context = new AccountDbContext())
                    {
                        return await SqlHelper.ExecuteNonQueryAsync(context, sql);
                    }
                
                case "gamedb":
                case "game":
                case "gameserver":
                    using (var context = new GameDbContext())
                    {
                        return await SqlHelper.ExecuteNonQueryAsync(context, sql);
                    }
                
                case "publicdb":
                case "public":
                    using (var context = new PublicDbContext())
                    {
                        return await SqlHelper.ExecuteNonQueryAsync(context, sql);
                    }
                
                default:
                    throw new ArgumentException($"Không hỗ trợ cơ sở dữ liệu: {databaseName}");
            }
        }

        /// <summary>
        /// Thực thi truy vấn SQL và trả về giá trị đơn
        /// </summary>
        public static async Task<object> ExecuteScalarAsync(string sql, string databaseName = "GameDb")
        {
            switch (databaseName.ToLower())
            {
                case "accountdb":
                case "account":
                    using (var context = new AccountDbContext())
                    {
                        return await SqlHelper.ExecuteScalarAsync(context, sql);
                    }
                
                case "gamedb":
                case "game":
                case "gameserver":
                    using (var context = new GameDbContext())
                    {
                        return await SqlHelper.ExecuteScalarAsync(context, sql);
                    }
                
                case "publicdb":
                case "public":
                    using (var context = new PublicDbContext())
                    {
                        return await SqlHelper.ExecuteScalarAsync(context, sql);
                    }
                
                default:
                    throw new ArgumentException($"Không hỗ trợ cơ sở dữ liệu: {databaseName}");
            }
        }
    }
}
