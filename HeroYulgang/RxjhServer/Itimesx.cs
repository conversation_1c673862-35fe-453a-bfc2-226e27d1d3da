using System;

namespace RxjhServer;

public class Itimesx
{
	private int int_0;

	private int int_1;

	private int int_2;

	private int int_3;

	public int SoLuong
	{
		get
		{
			return int_0;
		}
		set
		{
			int_0 = value;
		}
	}

	public int ThuocTinhLoaiHinh
	{
		get
		{
			return int_1;
		}
		set
		{
			int_1 = value;
		}
	}

	public int KhiCongThuocTinhLoaiHinh
	{
		get
		{
			return int_2;
		}
		set
		{
			int_2 = value;
		}
	}

	public int ThuocTinhSoLuong
	{
		get
		{
			return int_3;
		}
		set
		{
			int_3 = value;
		}
	}

	public Itimesx(byte[] byte_0)
	{
		ThuocTinhGiaiDoan(byte_0);
	}

	public void ThuocTinhGiaiDoan(byte[] byte_0)
	{
		var text = BitConverter.ToInt32(byte_0, 0).ToString();
		switch (text.Length)
		{
		case 8:
			SoLuong = 1;
			ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 1));
			if (ThuocTinhLoaiHinh == 8)
			{
				KhiCongThuocTinhLoaiHinh = int.Parse(text.Substring(4, 2));
			}
			if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
			{
				ThuocTinhSoLuong = int.Parse(text.Substring(6, 2));
			}
			else
			{
				ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 1)) * 10000000;
			}
			break;
		case 9:
			SoLuong = 1;
			ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 2));
			if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
			{
				ThuocTinhSoLuong = int.Parse(text.Substring(7, 2));
			}
			else
			{
				ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 2)) * 10000000;
			}
			break;
		case 10:
			SoLuong = 1;
			ThuocTinhLoaiHinh = int.Parse(text.Substring(0, 2));
			if (World.PhaiChang_HoTroMoRong_CacVatPham_ChuSo == 0)
			{
				ThuocTinhSoLuong = int.Parse(text.Substring(7, 2));
			}
			else
			{
				ThuocTinhSoLuong = int.Parse(text) - int.Parse(text.Substring(0, 3)) * 10000000;
			}
			break;
		}
	}
}
