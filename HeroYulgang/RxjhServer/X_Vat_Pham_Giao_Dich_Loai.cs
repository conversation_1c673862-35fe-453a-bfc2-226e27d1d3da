using System;

namespace RxjhServer;

public class X_Vat_Pham_Giao_Dich_Loai : IDisposable
{
	private int _VatPhamSoLuong;

	private X_Vat_Pham_Loai _VatPham;

	public int VatPhamSoLuong
	{
		get
		{
			return _VatPhamSoLuong;
		}
		set
		{
			_VatPhamSoLuong = value;
		}
	}

	public X_Vat_Pham_Loai VatPham
	{
		get
		{
			return _VatPham;
		}
		set
		{
			_VatPham = value;
		}
	}

	public void Dispose()
	{
		VatPham = null;
	}
}
