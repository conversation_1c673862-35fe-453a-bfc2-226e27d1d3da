using System;
using System.Runtime.InteropServices;
using System.Text;

namespace RxjhServer;

public class AtapiDevicea
{
	private const uint DFP_GET_VERSION = 475264u;

	private const uint DFP_SEND_DRIVE_COMMAND = 508036u;

	private const uint DFP_RECEIVE_DRIVE_DATA = 508040u;

	private const uint GENERIC_READ = 2147483648u;

	private const uint GENERIC_WRITE = 1073741824u;

	private const uint FILE_SHARE_READ = 1u;

	private const uint FILE_SHARE_WRITE = 2u;

	private const uint CREATE_NEW = 1u;

	private const uint OPEN_EXISTING = 3u;

	[DllImport("kernel32.dll", SetLastError = true)]
	private static extern int CloseHandle(IntPtr intptr_0);

	[DllImport("kernel32.dll", SetLastError = true)]
	private static extern IntPtr CreateFile(string string_0, uint uint_0, uint uint_1, IntPtr intptr_0, uint uint_2, uint uint_3, IntPtr intptr_1);

	[DllImport("kernel32.dll")]
	private static extern int DeviceIoControl(IntPtr intptr_0, uint uint_0, IntPtr intptr_1, uint uint_1, ref GetVersionOutParams getVersionOutParams_0, uint uint_2, ref uint uint_3, [Out] IntPtr intptr_2);

	[DllImport("kernel32.dll")]
	private static extern int DeviceIoControl(IntPtr intptr_0, uint uint_0, ref SendCmdInParams sendCmdInParams_0, uint uint_1, ref SendCmdOutParams sendCmdOutParams_0, uint uint_2, ref uint uint_3, [Out] IntPtr intptr_1);

	public static HardDiskInfo GetHddInfo(byte byte_0)
	{
		return Environment.OSVersion.Platform switch
		{
			PlatformID.Win32S => throw new NotSupportedException("Win32s   is   not   supported."), 
			PlatformID.Win32Windows => smethod_0(byte_0), 
			PlatformID.WinCE => throw new NotSupportedException("WinCE   is   not   supported."), 
			PlatformID.Win32NT => smethod_1(byte_0), 
			_ => throw new NotSupportedException("Unknown   Platform."), 
		};
	}

	private static HardDiskInfo smethod_0(byte byte_0)
	{
		var getVersionOutParams_ = default(GetVersionOutParams);
		var sendCmdInParams_ = default(SendCmdInParams);
		var sendCmdOutParams_ = default(SendCmdOutParams);
		var uint_ = 0u;
		var intPtr = CreateFile("\\\\.\\Smartvsd", 0u, 0u, IntPtr.Zero, 1u, 0u, IntPtr.Zero);
		if (intPtr == IntPtr.Zero)
		{
			throw new("Open   smartvsd.vxd   failed.");
		}
		if (DeviceIoControl(intPtr, 475264u, IntPtr.Zero, 0u, ref getVersionOutParams_, (uint)Marshal.SizeOf(getVersionOutParams_), ref uint_, IntPtr.Zero) == 0)
		{
			CloseHandle(intPtr);
			throw new("DeviceIoControl   failed:DFP_GET_VERSION");
		}
		if ((getVersionOutParams_.uint_0 & 1) == 0)
		{
			CloseHandle(intPtr);
			throw new("Error:   IDE   identify   command   not   supported.");
		}
		sendCmdInParams_.ideRegs_0.byte_5 = (byte)(((byte_0 & 1) == 0) ? 160u : 176u);
		if ((getVersionOutParams_.uint_0 & (16 >> byte_0)) != 0)
		{
			CloseHandle(intPtr);
			throw new($"Drive   {byte_0 + 1}   is   a   ATAPI   device,   we   don't   detect   it");
		}
		sendCmdInParams_.ideRegs_0.byte_6 = 236;
		sendCmdInParams_.byte_0 = byte_0;
		sendCmdInParams_.ideRegs_0.byte_1 = 1;
		sendCmdInParams_.ideRegs_0.byte_2 = 1;
		sendCmdInParams_.uint_0 = 512u;
		if (DeviceIoControl(intPtr, 508040u, ref sendCmdInParams_, (uint)Marshal.SizeOf(sendCmdInParams_), ref sendCmdOutParams_, (uint)Marshal.SizeOf(sendCmdOutParams_), ref uint_, IntPtr.Zero) == 0)
		{
			CloseHandle(intPtr);
			throw new("DeviceIoControl   failed:   DFP_RECEIVE_DRIVE_DATA");
		}
		CloseHandle(intPtr);
		return smethod_2(sendCmdOutParams_.idSector_0);
	}

	private static HardDiskInfo smethod_1(byte byte_0)
	{
		var getVersionOutParams_ = default(GetVersionOutParams);
		var sendCmdInParams_ = default(SendCmdInParams);
		var sendCmdOutParams_ = default(SendCmdOutParams);
		var uint_ = 0u;
		var intPtr = CreateFile($"\\\\.\\PhysicalDrive{byte_0}", 3221225472u, 3u, IntPtr.Zero, 3u, 0u, IntPtr.Zero);
		if (intPtr == IntPtr.Zero)
		{
			throw new("CreateFile   faild.");
		}
		if (DeviceIoControl(intPtr, 475264u, IntPtr.Zero, 0u, ref getVersionOutParams_, (uint)Marshal.SizeOf(getVersionOutParams_), ref uint_, IntPtr.Zero) == 0)
		{
			CloseHandle(intPtr);
			throw new($"Drive   {byte_0 + 1}   may   not   exists.");
		}
		if ((getVersionOutParams_.uint_0 & 1) == 0)
		{
			CloseHandle(intPtr);
			throw new("Error:   IDE   identify   command   not   supported.");
		}
		sendCmdInParams_.ideRegs_0.byte_5 = (byte)(((byte_0 & 1) == 0) ? 160u : 176u);
		if ((getVersionOutParams_.uint_0 & (16 >> byte_0)) != 0)
		{
			CloseHandle(intPtr);
			throw new($"Drive   {byte_0 + 1}   is   a   ATAPI   device,   we   don't   detect   it.");
		}
		sendCmdInParams_.ideRegs_0.byte_6 = 236;
		sendCmdInParams_.byte_0 = byte_0;
		sendCmdInParams_.ideRegs_0.byte_1 = 1;
		sendCmdInParams_.ideRegs_0.byte_2 = 1;
		sendCmdInParams_.uint_0 = 512u;
		if (DeviceIoControl(intPtr, 508040u, ref sendCmdInParams_, (uint)Marshal.SizeOf(sendCmdInParams_), ref sendCmdOutParams_, (uint)Marshal.SizeOf(sendCmdOutParams_), ref uint_, IntPtr.Zero) == 0)
		{
			CloseHandle(intPtr);
			throw new("DeviceIoControl   failed:   DFP_RECEIVE_DRIVE_DATA");
		}
		CloseHandle(intPtr);
		return smethod_2(sendCmdOutParams_.idSector_0);
	}

	private static HardDiskInfo smethod_2(IdSector idSector_0)
	{
		var result = default(HardDiskInfo);
		smethod_3(idSector_0.sModelNumber);
		result.ModuleNumber = Encoding.ASCII.GetString(idSector_0.sModelNumber).Trim();
		smethod_3(idSector_0.sFirmwareRev);
		result.Firmware = Encoding.ASCII.GetString(idSector_0.sFirmwareRev).Trim();
		smethod_3(idSector_0.sSerialNumber);
		result.SerialNumber = Encoding.ASCII.GetString(idSector_0.sSerialNumber).Trim();
		result.Capacity = idSector_0.uint_1 / 2 / 1024;
		return result;
	}

	private static void smethod_3(byte[] byte_0)
	{
		for (var i = 0; i < byte_0.Length; i += 2)
		{
			var b = byte_0[i];
			byte_0[i] = byte_0[i + 1];
			byte_0[i + 1] = b;
		}
	}

	public static string getHad()
	{
		try
		{
			return GetHddInfo(0).SerialNumber;
		}
		catch (Exception)
		{
			return "0";
		}
	}
}
