using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.Text;

namespace RxjhServer.HelperTools;

public class Converter
{
	public static Dictionary<string, byte[]> Hexstring = new();

	public static string Convert_String(byte[] byte1)
	{
		return Encoding.Default.GetString(byte1).Trim();
	}

	public static byte ToByte(byte[] byte_0, ref int int_0)
	{
		return byte_0[int_0++];
	}

	public static void ToBytes(byte byte_0, byte[] byte_1, ref int int_0)
	{
		byte_1[int_0++] = byte_0;
	}

	public static void ToBytes(BitArray bitArray_0, byte[] byte_0, ref int int_0)
	{
		bitArray_0.CopyTo(byte_0, int_0);
		int_0 += bitArray_0.Length / 8;
	}

	public static void ToBytes(double double_0, byte[] byte_0, ref int int_0)
	{
		var bytes = BitConverter.GetBytes(double_0);
		System.Buffer.BlockCopy(bytes, 0, byte_0, int_0, bytes.Length);
		int_0 += bytes.Length;
	}

	public static void ToBytes(short short_0, byte[] byte_0, ref int int_0)
	{
		byte_0[int_0++] = (byte)((uint)short_0 & 0xFFu);
		byte_0[int_0++] = (byte)((uint)(short_0 >> 8) & 0xFFu);
	}

	public static void ToBytes(int int_0, byte[] byte_0, ref int int_1)
	{
		byte_0[int_1++] = (byte)((uint)int_0 & 0xFFu);
		byte_0[int_1++] = (byte)((uint)(int_0 >> 8) & 0xFFu);
		byte_0[int_1++] = (byte)((uint)(int_0 >> 16) & 0xFFu);
		byte_0[int_1++] = (byte)((uint)(int_0 >> 24) & 0xFFu);
	}

	public static void ToBytes(long long_0, byte[] byte_0, ref int int_0)
	{
		ToBytes((ulong)long_0, byte_0, ref int_0);
	}

	public static void ToBytes(object object_0, byte[] byte_0, ref int int_0)
	{
		if (object_0 is int)
		{
			ToBytes((int)object_0, byte_0, ref int_0);
		}
		if (object_0 is uint)
		{
			ToBytes((uint)object_0, byte_0, ref int_0);
		}
		else if (object_0 is ulong)
		{
			ToBytes((ulong)object_0, byte_0, ref int_0);
		}
		else if (object_0 is long)
		{
			ToBytes((long)object_0, byte_0, ref int_0);
		}
		else if (object_0 is ushort)
		{
			ToBytes((ushort)object_0, byte_0, ref int_0);
		}
		else if (object_0 is short)
		{
			ToBytes((short)object_0, byte_0, ref int_0);
		}
		else if (object_0 is byte)
		{
			ToBytes((byte)object_0, byte_0, ref int_0);
		}
		else if (object_0 is string)
		{
			ToBytes((string)object_0, byte_0, ref int_0);
		}
	}

	public static void ToBytes(float float_0, byte[] byte_0, ref int int_0)
	{
		var bytes = BitConverter.GetBytes(float_0);
		System.Buffer.BlockCopy(bytes, 0, byte_0, int_0, bytes.Length);
		int_0 += bytes.Length;
	}

	public static void ToBytes(string string_0, byte[] byte_0, ref int int_0)
	{
		var array = string_0.ToCharArray();
		var array2 = array;
		foreach (var c in array2)
		{
			byte_0[int_0++] = (byte)c;
		}
	}

	public static void ToBytes(ushort ushort_0, byte[] byte_0, ref int int_0)
	{
		byte_0[int_0++] = (byte)(ushort_0 & 0xFFu);
		byte_0[int_0++] = (byte)((uint)(ushort_0 >> 8) & 0xFFu);
	}

	public static void ToBytes(uint uint_0, byte[] byte_0, ref int int_0)
	{
		byte_0[int_0++] = (byte)(uint_0 & 0xFFu);
		byte_0[int_0++] = (byte)((uint_0 >> 8) & 0xFFu);
		byte_0[int_0++] = (byte)((uint_0 >> 16) & 0xFFu);
		byte_0[int_0++] = (byte)((uint_0 >> 24) & 0xFFu);
	}

	public static void ToBytes(ulong ulong_0, byte[] byte_0, ref int int_0)
	{
		byte_0[int_0++] = (byte)(ulong_0 & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 8) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 16) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 24) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 32) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 40) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 48) & 0xFF);
		byte_0[int_0++] = (byte)((ulong_0 >> 56) & 0xFF);
	}

	public static void ToBytes(BitArray bitArray_0, byte[] byte_0, ref int int_0, int int_1)
	{
		bitArray_0.CopyTo(byte_0, int_0);
		int_0 += int_1;
	}

	public static double ToDouble(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToDouble(byte_0, int_0);
		int_0 += 8;
		return result;
	}

	public static float ToFloat(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToSingle(byte_0, int_0);
		int_0 += 4;
		return result;
	}

	public static short ToInt16(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToInt16(byte_0, int_0);
		int_0 += 2;
		return result;
	}

	public static int ToInt32(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToInt32(byte_0, int_0);
		int_0 += 4;
		return result;
	}

	public static long ToInt64(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToInt64(byte_0, int_0);
		int_0 += 8;
		return result;
	}

	public static ushort ToUInt16(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToUInt16(byte_0, int_0);
		int_0 += 2;
		return result;
	}

	public static uint ToUInt32(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToUInt32(byte_0, int_0);
		int_0 += 4;
		return result;
	}

	public static ulong ToUInt64(byte[] byte_0, ref int int_0)
	{
		var result = BitConverter.ToUInt64(byte_0, int_0);
		int_0 += 8;
		return result;
	}

	public static void ToGuidMark(ulong ulong_0, byte[] byte_0, ref int int_0)
	{
		byte b = 0;
		var array = new byte[8];
		var num = 0;
		for (var i = 0; i < 8; i++)
		{
			if (((ulong_0 >> 8 * i) & 0xFF) != 0)
			{
				b += (byte)Math.Pow(2.0, i);
				array[num] = (byte)((ulong_0 >> 8 * i) & 0xFF);
				num++;
			}
		}
		byte_0[int_0++] = b;
		System.Buffer.BlockCopy(array, 0, byte_0, int_0, num);
		int_0 += num;
	}

	public static ulong ReadGuidToUlong(byte[] byte_0, int int_0)
	{
		var b = byte_0[int_0++];
		var array = new byte[8];
		for (var i = 0; i < 8; i++)
		{
			if ((byte)((uint)(b >> i) & 1u) != 0)
			{
				array[i] = byte_0[int_0++];
			}
			else
			{
				array[i] = 0;
			}
		}
		return BitConverter.ToUInt64(array, 0);
	}

	public static string ToString(byte[] byte_0)
	{
		StringBuilder stringBuilder = new(byte_0.Length * 2);
		foreach (var b in byte_0)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return stringBuilder.ToString();
	}

	public static string ToString1(byte[] byte_0)
	{
		StringBuilder stringBuilder = new(byte_0.Length * 2);
		foreach (var b in byte_0)
		{
			stringBuilder.Append(b.ToString("X2"));
		}
		return "0x" + stringBuilder;
	}

	public static string ToString2(byte[] byte_0)
	{
		StringBuilder stringBuilder = new();
		foreach (var b in byte_0)
		{
			stringBuilder.Append(Convert.ToString((short)b, 16).PadLeft(2, '0').ToUpper());
		}
		return stringBuilder.ToString();
	}

	public static byte[] HexStringToByte(string string_0)
	{
		var key = ((string_0.Length > 40) ? string_0.Remove(40, string_0.Length - 40) : string_0);
		if (!Hexstring.TryGetValue(key, out var value))
		{
			value = hexStringToByte2(string_0);
			Hexstring.Add(key, value);
		}
		var array = new byte[value.Length];
		value.CopyTo(array, 0);
		return array;
	}

	public static byte[] hexStringToByte2(string string_0)
	{
		try
		{
			var num = string_0.Length / 2;
			var array = new byte[num];
			for (var i = 0; i < num; i++)
			{
				array[i] = Convert.ToByte(string_0.Substring(i * 2, 2), 16);
			}
			return array;
		}
		catch (Exception)
		{
			return new byte[string_0.Length];
		}
	}

	private static byte smethod_0(char char_0)
	{
		return (byte)"0123456789ABCDEF".IndexOf(char_0);
	}

	public static int getitmeid(string string_0)
	{
		var text = string_0.Substring(4, 2);
		var text2 = string_0.Substring(2, 2);
		var text3 = string_0.Substring(0, 2);
		return int.Parse(text + text2 + text3, NumberStyles.HexNumber);
	}

	public static int getItmeid(string string_0)
	{
		var text = string_0.Substring(6, 2);
		var text2 = string_0.Substring(4, 2);
		var text3 = string_0.Substring(2, 2);
		var text4 = string_0.Substring(0, 2);
		return int.Parse(text + text2 + text3 + text4, NumberStyles.HexNumber);
	}

	public static string smethod_8(byte[] byte1)
	{
		return Encoding.Default.GetString(byte1).Trim();
	}

	public static byte[] TangHoaKetHon(string str)
	{
		return Encoding.Default.GetBytes(str);
	}

	public static string DateTimeToString(DateTime dt)
	{
		return dt.ToString("yyyyMMdd");
	}
}
