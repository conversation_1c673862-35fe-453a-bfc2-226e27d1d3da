using System;
using System.Linq;
using System.Security;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;

namespace RxjhServer;

public class MissionClass : IDisposable
{
	private Players Play;

	public PromotionConfig[] promotions;

	public MissionClass(Players Playr)
	{
		Play = Playr;
	}

	~MissionClass()
	{
	}

	public void Dispose()
	{
		Play = null;
	}

	public void NhiemVu(byte[] data, int length)
	{
		try
		{
			Play.PacketModification(data, length);
			int questId = BitConverter.ToUInt16(data, 10);
			int step = BitConverter.ToUInt16(data, 12);
			int option = BitConverter.ToInt16(data, 14);
			switch (questId)
			{
				case 1:
					JobChange1(questId,step,option);
				return;
			}
			
			if (step == 1)
			{
				TaskReminder(questId, 11, option);
			}
			if (step == 3)
			{
				TaskReminder(questId, 31, option);
			}
			if (step == 5)
			{
				TaskReminder(questId, 51, option);
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi trả nhiệm vụ !!!");
		}
	}


	public void TaskReminder(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Error, $"Nhiệm vụ {NhiemVuID} - {ThaoTacD} - {NhiemVuGiaiDoanID}");
			var array = Converter.HexStringToByte("AA551400000084000600120033000000000000000000000055AA");
			System.Buffer.BlockCopy(BitConverter.GetBytes(NhiemVuID), 0, array, 10, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(ThaoTacD), 0, array, 12, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(NhiemVuGiaiDoanID), 0, array, 14, 2);
			System.Buffer.BlockCopy(BitConverter.GetBytes(Play.SessionID), 0, array, 4, 2);
			if (Play.Client != null)
			{
				Play.Client.Send_Map_Data(array, array.Length);
			}
		}
		catch
		{
		}
	}

	public void JobChange1(int NhiemVuID, int ThaoTacD, int NhiemVuGiaiDoanID)
	{
		LogHelper.WriteLine(LogLevel.Error, $"Nhiệm vụ Change job {NhiemVuID} - {ThaoTacD} - {NhiemVuGiaiDoanID}");
		try
		{
			switch (ThaoTacD)
			{
				case 1:
					if (Play.Player_Level >= 10 && Play.Player_Job_level < 1)
					{
						TaskReminder(NhiemVuID, 11, NhiemVuGiaiDoanID);
					}
					else
					{
						TaskReminder(NhiemVuID, 12, NhiemVuGiaiDoanID);
					}
					break;
				case 2:

					Play.CharacterToProfession(0, 1);
					TaskReminder(NhiemVuID, 21, 1);
					ThietLap_NhiemVu(NhiemVuID, 1);
					Play.NewLearningQigong(5, 0);
					Play.UpdateCharacterData(Play);
					Play.UpdateMartialArtsAndStatus();
					break;
				case 3:
					TaskReminder(NhiemVuID, 31, NhiemVuGiaiDoanID);
					break;
				case 4:
					if (Play.QuestList.ContainsKey(NhiemVuID))
					{
						Play.QuestList.Remove(NhiemVuID);
					}
					TaskReminder(NhiemVuID, 41, NhiemVuGiaiDoanID);
					break;
				case 5:
					TaskReminder(NhiemVuID, 51, NhiemVuGiaiDoanID);
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi Thăng Chức 1 - " + ex.Message);
		}
	}

	public void ThietLap_NhiemVu(int NhiemVuID, int NhiemVuGiaiDoanID)
	{
		try
		{
			if (Play.QuestList.TryGetValue(NhiemVuID, out var value))
			{
				value.NhiemVuGiaiDoanID = NhiemVuGiaiDoanID;
				return;
			}
			X_Nhiem_Vu_Loai x_Nhiem_Vu_Loai = new();
			x_Nhiem_Vu_Loai.NhiemVuID = NhiemVuID;
			x_Nhiem_Vu_Loai.NhiemVuGiaiDoanID = NhiemVuGiaiDoanID;
			if (!Play.QuestList.ContainsKey(NhiemVuID))
			{
				Play.QuestList.Add(NhiemVuID, x_Nhiem_Vu_Loai);
			}
		}
		catch
		{
		}
	}
		/// <summary>
	/// Lấy cấp độ thăng chức
	/// </summary>
	public int GetLevelThangChuc(int thangchuc)
	{
		return thangchuc switch
		{
			1 => 10,
			2 => 35,
			3 => 60,
			4 => 80,
			5 => 100,
			6 => 110,
			7 => 120,
			8 => 130,
			9 => 140,
			10 => 150,
			11 => 160,
			12 => 170,
			_ => 190,
		};
	}
	public class PromotionConfig
	{
		public int Level { get; set; }

		public int JobLevel { get; set; }

		public int ProfessionLevel { get; set; }

		public int QigongLevel { get; set; }

		public long Money { get; set; }

		public int MinUpgradeLevel { get; set; }

		public int? Exp { get; set; }

		public int? SkillPoints { get; set; }

		public double? RequiredVersion { get; set; }
	}

	public void THANG_CHUC_AUTO(Players player)
	{
		var promotions = new PromotionConfig[]
		{
		  new() {
			  Level = 10,
			  JobLevel = 0,
			  ProfessionLevel = 1,
			  QigongLevel = 5,
			  Money = 10000L,
			  MinUpgradeLevel = 6
		  },
		  new() {
			  Level = 60,
			  JobLevel = 2,
			  ProfessionLevel = 3,
			  QigongLevel = 7,
			  Money = 20000L,
			  MinUpgradeLevel = 7
		  },
		  new() {
			  Level = 80,
			  JobLevel = 3,
			  ProfessionLevel = 4,
			  QigongLevel = 8,
			  Money = 30000L,
			  MinUpgradeLevel = 8
		  },
		  new() {
			  Level = 100,
			  JobLevel = 4,
			  ProfessionLevel = 5,
			  QigongLevel = 10,
			  Money = 40000L,
			  MinUpgradeLevel = 9,
			  Exp = 9999999,
			  SkillPoints = 100
		  },
		  new() {
			  Level = 110,
			  JobLevel = 5,
			  ProfessionLevel = 6,
			  QigongLevel = 0,
			  Money = 50000L,
			  MinUpgradeLevel = 10
		  },
		  new() {
			  Level = 120,
			  JobLevel = 6,
			  ProfessionLevel = 7,
			  QigongLevel = 0,
			  Money = 60000L,
			  MinUpgradeLevel = 11
		  },
		  new() {
			  Level = 130,
			  JobLevel = 7,
			  ProfessionLevel = 8,
			  QigongLevel = 0,
			  Money = 70000L,
			  MinUpgradeLevel = 12
		  },
		  new() {
			  Level = 140,
			  JobLevel = 8,
			  ProfessionLevel = 9,
			  QigongLevel = 0,
			  Money = 80000L,
			  MinUpgradeLevel = 13
		  },
		  new() {
			  Level = 150,
			  JobLevel = 9,
			  ProfessionLevel = 10,
			  QigongLevel = 0,
			  Money = 100000L,
			  MinUpgradeLevel = 13
		  },
		  new() {
			  Level = 160,
			  JobLevel = 10,
			  ProfessionLevel = 11,
			  QigongLevel = 0,
			  Money = 100000L,
			  MinUpgradeLevel = 13,
			  Exp = 9999999,
			  SkillPoints = 100
		  },
		  new() {
			  Level = 170,
			  JobLevel = 11,
			  ProfessionLevel = 12,
			  QigongLevel = 0,
			  Money = 100000L,
			  MinUpgradeLevel = 13,
			  Exp = 9999999,
			  SkillPoints = 100
		  } };

		bool isHeroUpgradeEnabled = false;
		bool validJob = player.Player_Job != 8 && player.Player_Job != 9 && player.Player_Job != 11 && player.Player_Job != 12 && player.Player_Job !=
			13;

		bool isEligibleForUpgrade = promotions.Any(promotion =>
		{
			if (player.Player_Job_level == promotion.JobLevel && player.Player_Level >= promotion.Level)
			{
				if ((promotion.RequiredVersion == null) && (!validJob || !isHeroUpgradeEnabled))
				{
					AwardPlayer(player,promotion.ProfessionLevel, promotion.QigongLevel, promotion.Money, promotion.SkillPoints ??
						5, promotion.Exp ?? 9999999);
				}
				else
				{
					//   ProcessHeroUpgrade(promotion.ProfessionLevel, promotion.QigongLevel, promotion.Money, promotion.MinUpgradeLevel);
				}
				return true;
			}
			return false;
		});

		if (!isEligibleForUpgrade)
		{
			player.HeThongNhacNho("Hiện tại chưa đủ thăng chức tiếp!", 20, "Thiên cơ các");
		}
	}
	void AwardPlayer(Players player,int professionLevel, int qigongLevel, long money, int points = 5, int exp = 9999999, int skillPoints = 100)
	{
		player.CharacterToProfession(player.Player_Zx, professionLevel);
		player.NewLearningQigong(qigongLevel, 0);
		player.KiemSoatGold_SoLuong(money, 1);
		player.ThanNuVoCongDiemSo += points;
		player.Player_ExpErience += exp;
		player.ThangThienVoCong_DiemSo += skillPoints;
		player.UpdateCharacterData(player);
		player.UpdateMoneyAndWeight();
		player.UpdateMartialArtsAndStatus();
		player.HeThongNhacNho($"Chúc mừng bạn đã thăng chức lần {professionLevel}!", 13, "Thiên cơ các");
	}

}
