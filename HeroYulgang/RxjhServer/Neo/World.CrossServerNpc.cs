using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Helpers;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Phần mở rộng của lớp World để xử lý NPC trong Zone liên server
    /// </summary>
    public partial class World
    {
        // Dictionary lưu trữ ánh xạ giữa NPC từ server khác và NPC local
        public static Dictionary<string, Dictionary<int, int>> CrossServerNpcMapping = new Dictionary<string, Dictionary<int, int>>();


        // public static int GetNextNpcSessionId()
        // {
        //     return _nextNpcSessionId++;
        // }

        /// <summary>
        /// Thêm ánh xạ giữa NPC từ server khác và NPC local
        /// </summary>
        /// <param name="worldId">ID của server chứa NPC gốc</param>
        /// <param name="remoteNpcSessionId">SessionId của NPC trên server gốc</param>
        /// <param name="localNpcSessionId">SessionId của NPC trên server hiện tại</param>
        public static void AddCrossServerNpcMapping(string worldId, int remoteNpcSessionId, int localNpcSessionId)
        {
            try
            {
                // Kiểm tra xem đã có dictionary cho worldId chưa
                if (!CrossServerNpcMapping.TryGetValue(worldId, out var mapping))
                {
                    // Nếu chưa có, tạo mới
                    mapping = new Dictionary<int, int>();
                    CrossServerNpcMapping[worldId] = mapping;
                }

                // Thêm ánh xạ
                mapping[remoteNpcSessionId] = localNpcSessionId;

                LogHelper.WriteLine(LogLevel.Info, $"Đã thêm ánh xạ NPC: {worldId}:{remoteNpcSessionId} -> {localNpcSessionId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi thêm ánh xạ NPC: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy NPC local từ NPC remote
        /// </summary>
        /// <param name="worldId">ID của server chứa NPC gốc</param>
        /// <param name="remoteNpcSessionId">SessionId của NPC trên server gốc</param>
        /// <returns>NPC trên server hiện tại hoặc null nếu không tìm thấy</returns>
        public static NpcClass GetLocalNpcFromRemote(string worldId, int remoteNpcSessionId)
        {
            try
            {
                // Kiểm tra xem có ánh xạ cho worldId không
                if (!CrossServerNpcMapping.TryGetValue(worldId, out var mapping))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy ánh xạ NPC cho server {worldId}");
                    return null;
                }

                // Kiểm tra xem có ánh xạ cho remoteNpcSessionId không
                if (!mapping.TryGetValue(remoteNpcSessionId, out var localNpcSessionId))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy ánh xạ NPC cho {worldId}:{remoteNpcSessionId}");
                    return null;
                }

                // Tìm NPC trong server hiện tại
                foreach (var map in MapList.Values)
                {
                    if (map.npcTemplate.TryGetValue(localNpcSessionId, out var npc))
                    {
                        return npc;
                    }
                }

                LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy NPC local có session {localNpcSessionId}");
                return null;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi lấy NPC local từ remote: {ex.Message}");
                return null;
            }
        }





        /// <summary>
        /// Xóa một NPC khỏi bản đồ
        /// </summary>
        /// <param name="mapId">ID của bản đồ</param>
        /// <param name="npcId">ID của NPC</param>
        public static void delNpc(int mapId, int npcId)
        {
            try
            {
                if (MapList.TryGetValue(mapId, out var map))
                {
                    foreach (var npc in map.npcTemplate.Values.ToList())
                    {
                        if (npc.FLD_PID == npcId)
                        {
                            map.RemoveNpcFromMapClass(npc.NPC_SessionID);
                            LogHelper.WriteLine(LogLevel.Info, $"Đã xóa NPC {npc.Name} (ID: {npc.FLD_PID}) khỏi bản đồ {mapId}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi xóa NPC: {ex.Message}");
            }
        }

        internal static void SendMailCodNotificationByAdmin(int WorldId, int sessionID)
        {
            if (WorldId == ServerID)
            {
                var player = FindPlayerBySession(sessionID);
                if (player != null)
                {
                    SendingClass sendingClass = new();
                    sendingClass.Write4(1);
                    sendingClass.Write2(0);
                    player.Client?.SendPak(sendingClass, 12065, sessionID, true);
                }
            }
            else
            {
                conn.Transmit($"MAILCOD_ADMIN_NOTI|{WorldId}|{sessionID}");
            }

        }

        
        public static NpcClass AddNpcNeo(int int_0, float float_0, float float_1, int int_1, string thanmadaichien = "",
         float face1 = 0f, float face2 = 0f, int TMDC_Team = 0, bool worldBoss = false, int marked = 0, double disposeTime = 0)
        {
            try
            {
                if (MonsterTemplateList.TryGetValue(int_0, out var value))
                {
                    NpcClass npcClass = new()
                    {
                        FLD_PID = value.FLD_PID,
                        Name = value.Name,
                        Level = value.Level,
                        Rxjh_Exp = value.Rxjh_Exp,
                        Rxjh_X = float_0,
                        Rxjh_Y = float_1,
                        Rxjh_Z = 15f,
                        Rxjh_cs_X = float_0,
                        Rxjh_cs_Y = float_1,
                        Rxjh_cs_Z = 15f,
                        Rxjh_Map = int_1,
                        IsNpc = 0,
                        FLD_FACE1 = face1,
                        FLD_FACE2 = face2,
                        //Max_Rxjh_HP = value.Rxjh_HP,
                        //Rxjh_HP = value.Rxjh_HP,
                        FLD_AT = value.FLD_AT,
                        FLD_DF = 0,
                        FLD_AUTO = value.FLD_AUTO,
                        FLD_BOSS = value.FLD_BOSS,
                        FLD_FreeDrop = value.FLD_FreeDrop,
                        Rxjh_Evasion = value.FLD_Evasion,
                        Rxjh_Accuracy = value.FLD_Accuracy,
                        FLD_NEWTIME = 10,
                        QuaiXuatHien_DuyNhatMotLan = true,
                        timeNpcDie = DateTime.Now,
                        timeNpcRevival = DateTime.MinValue,
                        IsWorldBoss = worldBoss,
                        MarkedType = marked,
                        DisPoseTime = disposeTime
                    };
                    npcClass.SetMaxHP(value.Rxjh_HP);
                    npcClass.SetHp(value.Rxjh_HP);

                    if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
                    {
                        value2.AddNpcToMapClass(npcClass);
                    }
                    else
                    {
                        MapClass mapClass = new()
                        {
                            MapID = npcClass.Rxjh_Map
                        };
                        mapClass.AddNpcToMapClass(npcClass);
                        MapList.Add(mapClass.MapID, mapClass);
                    }

                    npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
                    npcClass.NPC_Attack();
                    if (disposeTime > 0)
                    {
                        npcClass.InitiateDisposeTime();
                    }

                    return npcClass;
                }
                else
                {
                    LogHelper.WriteLine(LogLevel.Error, "Không tìm thấy Npc cần add " + int_0);
                    return null;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, "Tăng cường đổ lỗi  [" + int_0 + "]error：" + ex);
                return null;
            }
        }

    }
}
