using System;
using HeroYulgang.Helpers;
using RxjhServer.ManageZone;

namespace RxjhServer
{
    /// <summary>
    /// Class chứa các hook kết nối với sự kiện di chuyển của người chơi để hỗ trợ Thiên cơ các Zone
    /// </summary>
    public static class Hooks_PlayerMove
    {
        /// <summary>
        /// Gọi phương thức này sau khi người chơi di chuyển để cập nhật Zone
        /// </summary>
        /// <param name="player">Người chơi vừa di chuyển</param>
        public static void AfterPlayerMove(Players player)
        {
            try
            {
                if (player == null)
                    return;
                
                // Kiểm tra và cập nhật zone của người chơi dựa theo vị trí
                ZoneManager.Instance.CheckPlayerZonePosition(player);
                
                // Cập nhật danh sách các đối tượng người chơi có thể nhìn thấy
                ZoneExtensions.RefreshZoneVisibility(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"AfterPlayerMove error: {ex.Message}");
            }
        }
        
        
        /// <summary>
        /// Gọi phương thức này khi người chơi đăng nhập vào game để thiết lập Zone ban đầu
        /// </summary>
        /// <param name="player">Người chơi vừa đăng nhập</param>
        public static void OnPlayerLogin(Players player)
        {
            try
            {
                if (player == null)
                    return;
                
                // Mặc định, đặt người chơi vào Zone 0 (Zone mặc định)
                ZoneManager.Instance.MovePlayerToZone(player, 0);
                
                // Kiểm tra xem người chơi có nằm trong vùng Zone đặc biệt nào không
                ZoneManager.Instance.CheckPlayerZonePosition(player);
                
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"OnPlayerLogin error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Gọi phương thức này trước khi người chơi đăng xuất để xử lý Zone
        /// </summary>
        /// <param name="player">Người chơi sắp đăng xuất</param>
        public static void OnPlayerLogout(Players player)
        {
            try
            {
                if (player == null)
                    return;
                
                // Nếu người chơi đang ở zone nào đó, loại bỏ khỏi zone đó
                if (player.CurrentZone != null)
                {
                    player.CurrentZone.RemovePlayer(player);
                    player.CurrentZone = null;
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"OnPlayerLogout error: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Gọi phương thức này khi người chơi tấn công NPC để kiểm tra có thể tấn công không
        /// </summary>
        /// <param name="player">Người chơi tấn công</param>
        /// <param name="npc">NPC bị tấn công</param>
        /// <returns>true nếu có thể tấn công, false nếu không thể</returns>
        public static bool CanAttackNpc(Players player, NpcClass npc)
        {
            try
            {
                if (player == null || npc == null)
                    return false;
                
                // Sử dụng extension method để kiểm tra có thể tấn công NPC không
                return player.CanAttackNpc(npc);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CanAttackNpc error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Gọi phương thức này khi người chơi tấn công người chơi khác để kiểm tra có thể tấn công không
        /// </summary>
        /// <param name="attacker">Người chơi tấn công</param>
        /// <param name="target">Người chơi bị tấn công</param>
        /// <returns>true nếu có thể tấn công, false nếu không thể</returns>
        public static bool CanAttackPlayer(Players attacker, Players target)
        {
            try
            {
                if (attacker == null || target == null)
                    return false;
                
                // Sử dụng extension method để kiểm tra có thể tấn công người chơi không
                return attacker.CanAttackPlayer(target);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CanAttackPlayer error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Gọi phương thức này khi người chơi nhặt vật phẩm để kiểm tra có thể nhặt không
        /// </summary>
        /// <param name="player">Người chơi nhặt vật phẩm</param>
        /// <param name="item">Vật phẩm cần nhặt</param>
        /// <returns>true nếu có thể nhặt, false nếu không thể</returns>
        public static bool CanPickupItem(Players player, X_Mat_Dat_Vat_Pham_Loai item)
        {
            try
            {
                if (player == null || item == null)
                    return false;
                
                // Sử dụng phương thức trực tiếp của vật phẩm để kiểm tra
                return item.CanPlayerPickupItem(player);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"CanPickupItem error: {ex.Message}");
                return false;
            }
        }
    }
} 