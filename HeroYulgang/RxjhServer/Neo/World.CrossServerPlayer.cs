
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using HeroYulgang.Helpers;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Phần mở rộng của lớp World để xử lý người chơi trong Zone liên server
    /// </summary>
    public partial class World
    {
        // Dictionary lưu trữ ánh xạ giữa người chơi từ server khác và người chơi local
        public static Dictionary<string, Dictionary<int, int>> CrossServerPlayerMapping = new Dictionary<string, Dictionary<int, int>>();
        
        /// <summary>
        /// Lấy một sessionId mới cho người chơi
        /// </summary>
        /// <returns>SessionId mới</returns>
        /// public static int GetNextPlayerSessionId()
        /// {
        ///     return _nextPlayerSessionId++;
        /// }

        /// <summary>
        /// Thêm ánh xạ giữa người chơi từ server khác và người chơi local
        /// </summary>
        /// <param name="worldId">ID của server chứa người chơi gốc</param>
        /// <param name="remotePlayerSessionId">SessionId của người chơi trên server gốc</param>
        /// <param name="localPlayerSessionId">SessionId của người chơi trên server hiện tại</param>
        public static void AddCrossServerPlayerMapping(string worldId, int remotePlayerSessionId, int localPlayerSessionId)
        {
            try
            {
                // Kiểm tra xem đã có dictionary cho worldId chưa
                if (!CrossServerPlayerMapping.TryGetValue(worldId, out var mapping))
                {
                    // Nếu chưa có, tạo mới
                    mapping = new Dictionary<int, int>();
                    CrossServerPlayerMapping[worldId] = mapping;
                }

                // Thêm ánh xạ
                mapping[remotePlayerSessionId] = localPlayerSessionId;

                LogHelper.WriteLine(LogLevel.Info, $"Đã thêm ánh xạ người chơi: {worldId}:{remotePlayerSessionId} -> {localPlayerSessionId}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi thêm ánh xạ người chơi: {ex.Message}");
            }
        }

        /// <summary>
        /// Lấy người chơi local từ người chơi remote
        /// </summary>
        /// <param name="worldId">ID của server chứa người chơi gốc</param>
        /// <param name="remotePlayerSessionId">SessionId của người chơi trên server gốc</param>
        /// <returns>Người chơi trên server hiện tại hoặc null nếu không tìm thấy</returns>
        public static Players GetLocalPlayerFromRemote(string worldId, int remotePlayerSessionId)
        {
            try
            {
                // Kiểm tra xem có ánh xạ cho worldId không
                if (!CrossServerPlayerMapping.TryGetValue(worldId, out var mapping))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy ánh xạ người chơi cho server {worldId}");
                    return null;
                }

                // Kiểm tra xem có ánh xạ cho remotePlayerSessionId không
                if (!mapping.TryGetValue(remotePlayerSessionId, out var localPlayerSessionId))
                {
                    LogHelper.WriteLine(LogLevel.Error, $"Không tìm thấy ánh xạ người chơi cho {worldId}:{remotePlayerSessionId}");
                    return null;
                }

                // Tìm người chơi trong server hiện tại
                return FindPlayerBySession(localPlayerSessionId);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi lấy người chơi local từ remote: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Lấy người chơi remote từ người chơi local
        /// </summary>
        /// <param name="localPlayerSessionId">SessionId của người chơi trên server hiện tại</param>
        /// <returns>Thông tin người chơi remote hoặc null nếu không tìm thấy</returns>
        public static (string worldId, int remotePlayerSessionId) GetRemotePlayerFromLocal(int localPlayerSessionId)
        {
            try
            {
                // Duyệt qua tất cả ánh xạ
                foreach (var worldMapping in CrossServerPlayerMapping)
                {
                    foreach (var mapping in worldMapping.Value)
                    {
                        if (mapping.Value == localPlayerSessionId)
                        {
                            return (worldMapping.Key, mapping.Key);
                        }
                    }
                }

                return (null, 0);
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi lấy người chơi remote từ local: {ex.Message}");
                return (null, 0);
            }
        }
        public static void DiChuyen_RaKhoi_BanDo(Players target, int removedSessionID, int removedBeastID)
        {
            try
            {
                var array = Converter.HexStringToByte("AA5513000300630008000100000003000000000000000000000055AA");
                System.Buffer.BlockCopy(BitConverter.GetBytes(removedSessionID), 0, array, 14, 2);
                System.Buffer.BlockCopy(BitConverter.GetBytes(target.SessionID), 0, array, 4, 2);
                target.Client?.SendMultiplePackage(array, array.Length);
                if (removedBeastID > 0)
                {
                    var array2 = Converter.HexStringToByte("AA551600549C6300080001000000549C0001000000000000A37B55AA");
                    System.Buffer.BlockCopy(BitConverter.GetBytes(removedBeastID), 0, array2, 14, 2);
                    System.Buffer.BlockCopy(BitConverter.GetBytes(target.SessionID), 0, array2, 4, 2);
                    target.Client?.SendMultiplePackage(array2, array2.Length);
                }
            }
            catch (Exception Ex)
            {
                LogHelper.WriteLine(LogLevel.Error, "DiChuyen_RaKhoi_BanDo error "+Ex.Message);
            }
        }
    }
}










