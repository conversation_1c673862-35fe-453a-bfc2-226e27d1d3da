using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using HeroYulgang.Helpers;
using Microsoft.Data.SqlClient;
using RxjhServer.Database;
using RxjhServer.HelperTools;
using RxjhServer.Network;

namespace RxjhServer
{
    /// <summary>
    /// Hệ thống quản lý chợ giao dịch vật phẩm
    /// </summary>
    public class MarketPlace
    {
        private readonly Players _player;

        private readonly long[] _buaChu =
        {
            800000003, 800000004, 800000005, 800000007, 800000008, 800000009, 800000010, 800000029, 800000050, 800000051,
            800000052, 800000053, 800000054, 800000055, 800000055, 800000057, 1000000330, 1000000365, 1000000367,
            1000000654, 1000000652
        };

        private readonly long[] _petEns =
        {
            800000030, 800000032, 800000031, 800000033, 800000034, 800000035, 800000036, 800000037, 800000080, 800000081,
            800000082, 800000083
        };

        private readonly long[] _petIds =
            { 1008000557, 1008000558, 1008000559, 1008000560, 1008000563, 1008000564, 1008000565 };

        // private readonly long[] _petEggBears = { 1008002690};
        private readonly Dictionary<long, int[]> _stoneDict = new()
        {
            { 800000001, new[] { 4, 1 } }, // Kim cương thạch
            { 800000023, new[] { 4, 2 } }, // Kim cương thạch cao cấp
            { 800000061, new[] { 4, 3 } }, // Kim cương thạch siêu cấp
            { 1000001650, new[] { 4, 4 } }, // Kim cương thạch hỗn nguyên
            { 800000002, new[] { 5, 1 } }, // Hàn ngọc thạch
            { 800000024, new[] { 5, 2 } }, // Hàn ngọc thạch cao cấp
            { 800000062, new[] { 5, 3 } }, // Hàn ngọc thạch siêu cấp
            { 1000001651, new[] { 5, 4 } }, // Hàn ngọc thạch cao cấp
            { 800000028, new[] { 11, 0 } } // Nguyên liệu thường
        };

        public MarketPlace(Players player)
        {
            _player = player;
        }

        /// <summary>
        /// Xử lý các yêu cầu từ chợ
        /// </summary>
        public void MarketPlaceRequest(byte[] data)
        {
            try
            {
                var offset = 2;
                int type = BitConverter.ToInt16(data, 0xC - offset);
                switch (type)
                {
                    case 1:
                        // Register New Item
                        MarketPlace_Register_Item(data, offset);
                        break;
                    case 2:
                        // Buy Item
                        MarKetPlace_Buy_Item(data, offset);
                        break;
                    case 3:
                        // Get List Item
                        MarketPlace_List_Item(data, offset);
                        break;
                    case 4:
                        // Log
                        MarketPlace_History(_player, data, offset);
                        break;
                    case 5:
                        // Retrieve Item from market
                        MarketPlace_Take_Profit(data, offset);
                        break;
                    case 6:
                        MarketPlace_Cancel_Selling(data, offset);
                        break;
                    case 7:
                        // My Selling Item
                        MarketPlace_List_Items_Sold(_player, offset);
                        break;
                }
            }
            catch (Exception ex)
            {
                _player.HeThongNhacNho("MarketPlace Error ", 10, "Market");
                LogHelper.WriteLine(LogLevel.Error, "MarketPlace Error " + ex.Message);
            }
        }

       public void MarketPlace_Take_Profit(byte[] data, int offset)
    {
        // int type = BitConverter.ToInt16(data, 0xC - offset);
        // int amount = BitConverter.ToInt16(data, 0x1A - offset);
        var productId = BitConverter.ToInt32(data, 0x12 - offset);
        // var itemId = BitConverter.ToInt64(data, 0x1c - offset);
        // var option = BitConverter.ToInt32(data, 0x24 - offset);
        // var magic1 = BitConverter.ToInt32(data, 0x28 - offset);
        // var magic2 = BitConverter.ToInt32(data, 0x2C - offset);
        // var magic3 = BitConverter.ToInt32(data, 0x30 - offset);
        // var magic4 = BitConverter.ToInt32(data, 0x34 - offset);
        // var price = BitConverter.ToInt64(data, 0x38 - offset);
        // LogHelper.WriteLine(LogLevel.Debug,
        //     $" type {type} a {amount} price {price} productid {productId} itemId {itemId} op {option} m1 {magic1} m2 {magic2} m3 {magic3} m4 {magic4}");
        try
        {
            const string query = @"
                SELECT 
                    M.*,
                    OD.ID AS OrderDetail_ID,
                    OD.STATUS AS OrderDetail_STATUS,
                    OD.MESSAGE AS OrderDetail_MESSAGE,
                    OD.BUYYER AS OrderDetail_BUYYER,
                    OD.AMOUNT AS OrderDetail_AMOUNT,
                    OD.PRICE AS OrderDetail_PRICE,
                    OD.UPDATED_AT AS OrderDetail_UPDATED_AT,
                    OD.CREATED_AT AS OrderDetail_CREATED_AT
                FROM 
                    MARKETPLACE M
                INNER JOIN 
                    ORDER_DETAIL OD ON M.ID = OD.MARKETPLACE_ID
                WHERE 
                    M.ID = @MarketplaceID 
                    AND M.SELLER_NAME = @SellerName 
                    AND M.STATUS = 'SOLD'
            ";
            var sqlParameters = new List<SqlParameter>
            {
                new("@MarketplaceID", productId),
                new("@SellerName", _player.CharacterName)
            };
            var itemTable = DBA.GetDBToDataTable(query, sqlParameters.ToArray(), "BBG");
            if (itemTable.Rows.Count > 0)
            {
                var priceDb = long.Parse(itemTable.Rows[0]["FLD_PRICE"].ToString());
                MarketPlace_Update_Status((int)itemTable.Rows[0]["ID"], "COMPLETED");
                // Nhận Cash
                MarketPlace_Currency_Update(priceDb, CurrencyOperation.Increase);
                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Lưu thông tin nhân vật phòng chống mất đồ
                var array = Converter.HexStringToByte(
                    "aa5500000f27460154000500010000000000000000000000000000000000000000000600000001000000b9823f0200000000189354000000004700000000000000000000000000000000000000000000000000bcadb1000000000000000055aa");
                var buyer = itemTable.Rows[0]["OrderDetail_BUYYER"].ToString();
                System.Buffer.BlockCopy(Encoding.GetEncoding(1252).GetBytes(buyer), 0, array, 0x12,
                    Encoding.GetEncoding(1252).GetBytes(buyer).Length);
                _player.Client?.Send_Map_Data(array, array.Length);
            }
            else
            {
                _player.HeThongNhacNho("Không tìm thấy vật phẩm!!! Vui lòng liên hệ admin", 10, "Market");
                LogHelper.WriteLine(LogLevel.Debug, "xxx");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "MarKetPlace_Cancel_Selling " + ex.Message);
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public void MarketPlace_Cancel_Selling(byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        int amount = BitConverter.ToInt16(data, 0x1A - offset);
        var productId = BitConverter.ToInt32(data, 0x12 - offset);
        var itemId = BitConverter.ToInt64(data, 0x1c - offset);
        var option = BitConverter.ToInt32(data, 0x24 - offset);
        var magic1 = BitConverter.ToInt32(data, 0x28 - offset);
        var magic2 = BitConverter.ToInt32(data, 0x2C - offset);
        var magic3 = BitConverter.ToInt32(data, 0x30 - offset);
        var magic4 = BitConverter.ToInt32(data, 0x34 - offset);
        // var price = BitConverter.ToInt64(data, 0x38 - offset);
        // LogHelper.WriteLine(LogLevel.Debug,
        //     $" type {type} a {amount} price {price} productid {productId} itemId {itemId} op {option} m1 {magic1} m2 {magic2} m3 {magic3} m4 {magic4}");
        try
        {
            var query = string.Format(
                @"SELECT * FROM MARKETPLACE WHERE ID='{0}' AND SELLER_NAME='{1}' AND STATUS='SELLING'"
                , productId, _player.CharacterName);
            var itemTable = DBA.GetDBToDataTable(query, "BBG");
            if (itemTable.Rows.Count > 0)
            {
                var emptySlot = _player.GetParcelVacancy(_player);
                if (emptySlot == -1)
                {
                    _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống", 10, "Market");
                    throw new("Thùng đồ không đủ chỗ trống");
                }

                var marketItem = new X_Vat_Pham_Loai();
                var marketItemByte = (byte[])itemTable.Rows[0]["ITEM"];

                if (amount > 1)
                {
                    var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                    System.Buffer.BlockCopy(newId, 0, marketItemByte, 0, 8);
                }

                marketItem.VatPham_byte = marketItemByte;
                // var priceDb = Convert.ToInt64(itemTable.Rows[0]["BASE_PRICE"]);
                if (amount != (int)itemTable.Rows[0]["CURRENT_AMOUNT"] || itemId != marketItem.GetVatPham_ID ||
                    option != marketItem.FLD_MAGIC0 || magic1 != marketItem.FLD_MAGIC1 ||
                    magic2 != marketItem.FLD_MAGIC2 || magic3 != marketItem.FLD_MAGIC3 ||
                    magic4 != marketItem.FLD_MAGIC4)
                {
                    // Check if user using cheat
                    _player.HeThongNhacNho("Có lỗi xảy ra khi kiểm tra vật phẩm", 10, "Market");
                    throw new("Vật phẩm lỗi");
                }
                // Update Status of marketplace item to CANCELLED

                if (!MarketPlace_Update_Status((int)itemTable.Rows[0]["ID"], "CANCELLED"))
                    throw new($"Không thể cập nhật cancel vật phẩm : {_player.CharacterName}");

                // Check and give item back to User
                marketItem.VatPhamSoLuong = BitConverter.GetBytes(amount);
                _player.Item_In_Bag[emptySlot] = marketItem;
                if (_player.CharacterBeast != null)
                {
                    _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                    _player.UpdateTheWeightOfTheBeast();
                }

                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Lưu thông tin nhân vật phòng chống mất đồ
                MarketPlace_Response(type, 1); // true = success 2 = buy
            }
            else
            {
                // No item Send Error message
                _player.HeThongNhacNho("Item này đã được bán hoặc hết hạn!!!", 10, "Market");
                //MarketPlace_Response(type, false); // true = success
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "MarKetPlace_Cancel_Selling " + ex.Message);
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public void MarKetPlace_Buy_Item(byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        int amount = BitConverter.ToInt16(data, 0x1A - offset);
        var productId = BitConverter.ToInt32(data, 0x12 - offset);
        var itemId = BitConverter.ToInt64(data, 0x1c - offset);
        var option = BitConverter.ToInt32(data, 0x24 - offset);
        var magic1 = BitConverter.ToInt32(data, 0x28 - offset);
        var magic2 = BitConverter.ToInt32(data, 0x2C - offset);
        var magic3 = BitConverter.ToInt32(data, 0x30 - offset);
        var magic4 = BitConverter.ToInt32(data, 0x34 - offset);
        var price = BitConverter.ToInt64(data, 0x38 - offset);
        // LogHelper.WriteLine(LogLevel.Debug, $" type {type} amount {amount} productid {productId} itemId {itemId} price {price}");
        if (_player.Player_Money < price)
        {
            _player.HeThongNhacNho("Bạn không đủ tiền mua vật phẩm này ", 10, "Market");
            return;
        }

        try
        {
            var query =
                @"SELECT * FROM MARKETPLACE WHERE ID=@ProductID AND STATUS='SELLING' AND  EXPIRED_AT >= @ExpiredAt";
            var sqlParameters = new List<SqlParameter>
            {
                new("@ProductID", productId),
                new("@ExpiredAt", DateTime.Now)
            };

            var itemTable = DBA.GetDBToDataTable(query, sqlParameters.ToArray(), "BBG");
            if (itemTable.Rows.Count > 0)
            {
                var emptySlot = _player.GetParcelVacancy(_player);
                if (emptySlot == -1)
                {
                    _player.HeThongNhacNho("Thùng đồ không đủ chỗ trống", 10, "Market");
                    throw new("Thùng đồ không đủ chỗ trống");
                }

                var marketItem = new X_Vat_Pham_Loai();
                var marketItemByte = (byte[])itemTable.Rows[0]["ITEM"];

                if (amount > 1)
                {
                    var newId = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
                    System.Buffer.BlockCopy(newId, 0, marketItemByte, 0, 8);
                }

                marketItem.VatPham_byte = marketItemByte;
                var priceDb = Convert.ToInt64(itemTable.Rows[0]["BASE_PRICE"]);
                if (price != priceDb || amount != (int)itemTable.Rows[0]["CURRENT_AMOUNT"] ||
                    itemId != marketItem.GetVatPham_ID || option != marketItem.FLD_MAGIC0 ||
                    magic1 != marketItem.FLD_MAGIC1 || magic2 != marketItem.FLD_MAGIC2 ||
                    magic3 != marketItem.FLD_MAGIC3 || magic4 != marketItem.FLD_MAGIC4)
                {
                    // Check if user using cheat
                    _player.HeThongNhacNho("Có lỗi xảy ra khi kiểm tra vật phẩm", 10, "Market");
                    throw new("Vật phẩm lỗi");
                }

                // Tạo order đồng thời cập nhật Status thành đã bán
                var orderId = MarketPlace_CreateOrderDetail(_player, (int)itemTable.Rows[0]["ID"], amount, price);
                if (orderId == -1) throw new("Không tạo được order vật phẩm");

                //Kiem_Soat_Money(price, 0);
                // Trừ cash
                MarketPlace_Currency_Update(price, CurrencyOperation.Decrease);

                // Tiến hành chuyển vật phẩm cho nhân vật
                marketItem.VatPhamSoLuong = BitConverter.GetBytes(amount);
                _player.Item_In_Bag[emptySlot] = marketItem;
                if (_player.CharacterBeast != null)
                {
                    _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                    _player.UpdateTheWeightOfTheBeast();
                }

                _player.Init_Item_In_Bag();
                _player.UpdateCharacterData(_player);
                _player.SaveCharacterData(); // Lưu thông tin nhân vật phòng chống mất đồ
                // Cập nhật Order thành công sau khi user đã nhận item

                MarketPlace_Response(type, 1); // true = success 2 = buy
                // Cập nhật order thành đã hoàn thành
                MarketPlace_Update_Order(orderId, "SUCCESSED");
                MarketPlace_Update_Status((int)itemTable.Rows[0]["ID"], "SOLD");
            }
            else
            {
                _player.HeThongNhacNho("Item này đã được bán hoặc hết hạn!!!", 10, "Market");
            }
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "MarKetPlace_Buy_Item " + ex.Message);
            _player.HeThongNhacNho("Có lỗi xảy ra với Thiên cơ các! Vui lòng thử lại sau!!!", 10, "Market");
        }
    }

    public int MarketPlace_CreateOrderDetail(Players player, int id, int amount, long price)
    {
        try
        {
            const string insertQuery = @"
        INSERT INTO ORDER_DETAIL (STATUS, MESSAGE, BUYYER, AMOUNT, PRICE, MARKETPLACE_ID, CREATED_AT, UPDATED_AT)
        OUTPUT INSERTED.ID
        VALUES (@Status, @Message, @Buyer, @Amount, @Price,@MarketID, @CreatedAt, @UpdatedAt)";

            var sqlParameters = new List<SqlParameter>
            {
                new("@Status", "PENDING"),
                new("@Message", DBNull.Value),
                new("@Buyer", player.CharacterName),
                new("@Amount", amount),
                new("@Price", price),
                new("@marketID", id),
                new("@CreatedAt", DateTime.Now),
                new("@UpdatedAt", DateTime.Now)
            };

            var orderDetailId = DBA.GetDBValue_3(insertQuery, sqlParameters.ToArray(), "BBG");
            if (orderDetailId is not int detailId) return -1;

            return detailId;
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "MarketPlace_CreateOrderDetail Error " + ex.Message);
            return -1;
        }
    }

    public bool MarketPlace_Update_Order(int orderId, string status)
    {
        const string updateQuery = @"UPDATE ORDER_DETAIL SET STATUS=@Status WHERE ID = @OrderID";
        var sqlParameter = new List<SqlParameter>
        {
            new("@Status", status),
            new("@OrderID", orderId)
        };
        var rowsAffected = DBA.ExeSqlCommand(updateQuery, sqlParameter.ToArray(), "BBG").GetAwaiter().GetResult();
        return rowsAffected > 0;
    }

    public void MarketPlace_Response(int type, int success)
    {
        var array = Converter.HexStringToByte("aa5500000f274601060001000100000055aa");
        System.Buffer.BlockCopy(BitConverter.GetBytes(type), 0, array, 0xC, 2);
        System.Buffer.BlockCopy(BitConverter.GetBytes(success), 0, array, 0XE, 2);
        _player.Client?.Send_Map_Data(array, array.Length);
    }

    public void MarketPlace_List_Items_Sold(Players player, int offset)
    {
        SendingClass packetDataClass = new();
        packetDataClass.Write2(7); //
        packetDataClass.Write4(1);
        packetDataClass.Write4(int.MaxValue);
        //packetDataClass.Write4(0);
        var countQuery = $"""
                          SELECT COUNT(*) AS TotalItems from MARKETPLACE WHERE
                                      STATUS = 'SOLD' AND SELLER_NAME = '{player.CharacterName}'
                          """;
        var itemsQuery = string.Format("""
                                       
                                                  WITH FilteredItems AS (
                                                                   SELECT
                                                                       ID, PRODUCT_CODE, SELLER_NAME, BASE_AMOUNT, BASE_PRICE, FLD_PRICE, CURRENT_AMOUNT,
                                                                       FILTER_1, FILTER_2, FILTER_3, FILTER_4, FILTER_5, ITEM, CREATED_AT, EXPIRED_AT,
                                                                       ROW_NUMBER() OVER (ORDER BY CREATED_AT DESC) AS RowNum
                                                                   FROM
                                                                       MARKETPLACE
                                                                   WHERE
                                                                       STATUS = 'SOLD'
                                                                       AND SELLER_NAME = '{0}'
                                                               )
                                                               SELECT *
                                                               FROM FilteredItems
                                                               WHERE RowNum BETWEEN ({1} - 1) * {2} + 1 AND {1} * {2};
                                               
                                       """, player.CharacterName, 1, 30);
        var itemsTable = DBA.GetDBToDataTable(itemsQuery, "BBG");
        foreach (DataRow row in itemsTable.Rows)
            try
            {
                MarketPlace_Write_One_Item(packetDataClass, row, "FLD_PRICE");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Debug, "Error " + ex.Message);
                itemsTable.Dispose();
            }

        _player.Client?.SendPak(packetDataClass, 17921, 0, true);
        itemsTable.Dispose();
    }

    public void MarketPlace_Write_One_Item(SendingClass packetDataClass, DataRow row, string showPrice, int currentPage = 0)
    {
        packetDataClass.Write4(currentPage);
        packetDataClass.Write4((int)row["ID"]);
        packetDataClass.Write4(0);
        packetDataClass.Write4(_player.SessionID);
        packetDataClass.WriteString(_player.AccountID.Trim(), 20);
        packetDataClass.Write4(_player.SessionID);
        packetDataClass.Write4(0);
        packetDataClass.Write(0);
        packetDataClass.WriteName(row["SELLER_NAME"].ToString().Trim());
        packetDataClass.Write4(0);
        packetDataClass.Write4(0);
        var originalItem = (byte[])row["ITEM"];
        X_Vat_Pham_Loai item = new()
        {
            VatPham_byte = originalItem
        };
        packetDataClass.Write(item.GetByte());

        packetDataClass.Write8((long)row[showPrice]);
        packetDataClass.Write2((int)row["CURRENT_AMOUNT"]);
        packetDataClass.Write4(MarketPlace_Get_Expired_Seconds(Convert.ToDateTime(row["EXPIRED_AT"])));
       // packetDataClass.Write4(0);
    }

    public void MarketPlace_List_Item(byte[] data, int offset)
    {
       
        int page = BitConverter.ToInt16(data, 0x1c - offset);
        int filter0 = BitConverter.ToInt16(data, 0xe - offset);
        int filter1 = BitConverter.ToInt16(data, 0x12 - offset);
        int filter2 = BitConverter.ToInt16(data, 0x14 - offset);
        int filter3 = BitConverter.ToInt16(data, 0x16 - offset);
        int filter4 = BitConverter.ToInt16(data, 0x18 - offset);
        int filter5 = BitConverter.ToInt16(data, 0x1A - offset);

        var searchByte = new byte[0x1A];
        Array.Copy(data, 0x30 - offset, searchByte, 0, 0x1A);
        var searchWord = Encoding.Default.GetString(searchByte).Replace("\0", "").ToLowerInvariant();
        if (!string.IsNullOrEmpty(searchWord)) searchWord = "%" + searchWord + "%";

        // LogHelper.WriteLine(LogLevel.Debug,
        //     $"Page = {page} | filter0 ={filter0} | filter1 = {filter1} | filter2 = {filter2} | filter 3 = {filter3} | filter 4 = {filter4} | filter 5 = {filter5} | search = {searchWord}");

        const string countQuery = """
                                  
                                          SELECT COUNT(*) AS TotalItems 
                                          FROM MARKETPLACE 
                                          WHERE STATUS = 'SELLING' 
                                          AND (@Filter1 = 0 OR FILTER_1 = @Filter1)
                                          AND (@Filter2 = 0 OR FILTER_2 = @Filter2)
                                          AND (@Filter3 = 0 OR FILTER_3 = @Filter3)
                                          AND (@Filter4 = 0 OR FILTER_4 = @Filter4)
                                          AND (@Filter5 = 0 OR FILTER_5 = @Filter5)
                                          AND (@searchWord = '' OR ITEM_NAME LIKE @searchWord)
                                          AND EXPIRED_AT >= @DateNow
                                          
                                  """;
        var sqlParameters = new List<SqlParameter>
        {
            new("@Filter1", filter1),
            new("@Filter2", filter2),
            new("@Filter3", filter3),
            new("@Filter4", filter4),
            new("@Filter5", filter5),
            new("@DateNow", DateTime.Now),
            new("@searchWord", searchWord)
        };

        var orderByClause = filter0 switch
        {
            1 => "ORDER BY FILTER_1 ASC",
            2 => "ORDER BY FILTER_1 DESC",
            3 => "ORDER BY BASE_PRICE ASC",
            4 => "ORDER BY BASE_PRICE DESC",
            _ => ""
        };

        var itemsQuery = $"""
                          
                                    WITH FilteredItems AS (
                                        SELECT
                                            ID, PRODUCT_CODE, ITEM_NAME, SELLER_NAME, BASE_AMOUNT, BASE_PRICE, FLD_PRICE, CURRENT_AMOUNT,
                                            FILTER_1, FILTER_2, FILTER_3, FILTER_4, FILTER_5, ITEM, CREATED_AT, EXPIRED_AT,
                                            ROW_NUMBER() OVER (ORDER BY CREATED_AT DESC) AS RowNum
                                        FROM
                                            MARKETPLACE
                                        WHERE
                                            STATUS = 'SELLING'
                                            AND (@Filter1 = 0 OR FILTER_1 = @Filter1)
                                            AND (@Filter2 = 0 OR FILTER_2 = @Filter2)
                                            AND (@Filter3 = 0 OR FILTER_3 = @Filter3)
                                            AND (@Filter4 = 0 OR FILTER_4 = @Filter4)
                                            AND (@Filter5 = 0 OR FILTER_5 = @Filter5)
                                            AND (@searchWord = '' OR ITEM_NAME LIKE @searchWord)
                                            AND EXPIRED_AT >= @DateNow
                                    )
                                    SELECT *
                                    FROM FilteredItems
                                    WHERE RowNum BETWEEN (@Page - 1) * @Pagesize + 1 AND @Page * @Pagesize
                                    {orderByClause}
                                      
                          """;
        var param2 = new List<SqlParameter>
        {
            new("@Filter1", filter1),
            new("@Filter2", filter2),
            new("@Filter3", filter3),
            new("@Filter4", filter4),
            new("@Filter5", filter5),
            new("@DateNow", DateTime.Now),
            new("@searchWord", searchWord),
            new("@Page", page + 1),
            new("@Pagesize", 10)
        };
        // Execute the count query to get the total number of items
        var totalItems = (int)DBA.GetDBValue_3(countQuery, sqlParameters.ToArray(), "BBG");

        // Execute the items query to get the paginated list of items
        var itemsTable = DBA.GetDBToDataTable(itemsQuery, param2.ToArray(), "BBG");
        var packetData = new SendingClass();
        packetData.Write2(3); // 3- Item List
        packetData.Write4(totalItems != 0 ? 1:0);
        var totalPages = (totalItems + 9) / 10; // This rounds up without adding 1 unnecessarily
        packetData.Write4(totalPages);
        LogHelper.WriteLine(LogLevel.Error,"Total "+ totalItems + " page " + totalPages);
         // Unknow header
        foreach (DataRow row in itemsTable.Rows)
        try
        {
            MarketPlace_Write_One_Item(packetData, row, "BASE_PRICE");
        }
        catch (Exception ex)
        {
            LogHelper.WriteLine(LogLevel.Debug, "Error " + ex.Message);
            itemsTable.Dispose();
        }
        //Client?.SendPacketX(packetData, 326, 0);
        _player.Client?.SendPak(packetData, 17921, _player.SessionID,true);
        itemsTable.Dispose();
    }

    public void MarketPlace_Register_Item(byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        int itemPos = BitConverter.ToInt16(data, 0xE - offset);
        var price = BitConverter.ToInt64(data, 0x14 - offset);
        int amount = BitConverter.ToInt16(data, 0x1c - offset);
        _player.Item_In_Bag[itemPos].Lock_Move = true;
        if (amount > _player.Item_In_Bag[itemPos].GetVatPhamSoLuong)
            throw new($"{_player.CharacterName} - Số lượng vật phẩm đăng bán nhiều hơn số lượng đang có ");

        var itemByte = new byte[World.Item_Db_Byte_Length]; //
        System.Buffer.BlockCopy(data, 0x1E - offset, itemByte, 8, World.Item_Db_Byte_Length - 8); //  - Series
        //LogHelper.WriteLine(LogLevel.Debug, $"ItemPost {itemPos} itemPrice {price} amount {amount}");
        if (_player.Player_Money <= 0 || _player.Player_Money < price * 1 / 1000)
        {
            _player.HeThongNhacNho($"Cần {price * 1 / 1000} Lượng để có thể bán vật phẩm", 10, "Market");
            throw new("Nhân vật không đủ tiền để đăng ký bán vật phẩm");
        }

        if (price < 1)
        {
            _player.HeThongNhacNho("Giá của vật phẩm không thể nhỏ hơn 1", 10, "Market");
            throw new("Giá của vật phẩm không thể nhỏ hơn 1");
        }

        // Check 20 item sell cung luc
        MarketPlace_ValidateMaxItemsOnSale(type);
        var itemId = _player.Item_In_Bag[itemPos].GetVatPham_ID;
        var newProductCode = "MP" + DateTime.Now.ToString("yyMMddHHmmss");
        var rowsAffected = MarketPlace_RegisterItemInMarket(itemPos, itemId, newProductCode, amount, price);
        if (rowsAffected > 0)
        {
            //Success
            X_Vat_Pham_Loai item = new()
            {
                VatPham_byte = itemByte
            };
            item = _player.Item_In_Bag[itemPos];
            _player.Item_In_Bag[itemPos].Lock_Move = false;
            //LogHelper.WriteLine(LogLevel.Error, $"Success id {rowsAffected} amount {amount} old {Item_In_Bag[itemPos].VatPhamSoLuong}");
            var amountLeft = _player.Item_In_Bag[itemPos].GetVatPhamSoLuong - amount;

            if (amountLeft <= 0)
                _player.Item_In_Bag[itemPos].VatPham_byte = new byte[World.Item_Db_Byte_Length];
            else
                _player.Item_In_Bag[itemPos].VatPhamSoLuong = BitConverter.GetBytes(amountLeft);

            MarketPlace_Currency_Update(price * 1 / 1000, CurrencyOperation.Decrease);
            _player.SaveCharacterData();
            if (!MarketPlace_Update_Status(newProductCode, "SELLING"))
            {
                _player.Item_In_Bag[itemPos] = item;
                _player.SaveCharacterData();
                throw new("Không thể cập nhật vật phẩm. Vui lòng thử lại sau");
            }

            if (_player.CharacterBeast != null)
            {
                _player.UpdateTheEquipmentBasketPackageOfTheSpiritBeastSInitialStory();
                _player.UpdateTheWeightOfTheBeast();
            }

            _player.Init_Item_In_Bag();
            MarketPlace_Response(type, 1);
        }
        else
        {
            _player.HeThongNhacNho("Có lỗi xảy ra với máy chủ! Vui lòng thử lại sau!", 10, "Market");
            MarketPlace_Update_Status(newProductCode, "FAILED");
            // Failed, Revert the change, add back item to Bag
            MarketPlace_Response(type, int.MaxValue - 4); // true = success
        }
    }

    public void MarketPlace_Currency_Update(long price, CurrencyOperation operation)
    {
        switch (operation)
        {
            case CurrencyOperation.Decrease:
                _player.KiemSoatGold_SoLuong(price, 0);
                break;
            case CurrencyOperation.Increase:
                _player.KiemSoatGold_SoLuong(price, 1);
                break;
            default:
                LogHelper.WriteLine(LogLevel.Error,"Default");
                break;
        }

        _player.UpdateMoneyAndWeight();
        // var type = 0;
        // // position 0 = -gold 1 = +gold
        // switch (type)
        // {
        //     case 1:
        //         CheckTheNumberOfIngotsInBaibaoge();
        //         if (operation == CurrencyOperation.Decrease)
        //         {
        //             KiemSoatCash_SoLuong((int)price, 0);
        //             HeThongNhacNho($"Đã trừ {price} Cash. Còn lại {FLD_RXPIONT} CASH", 10, "Market");
        //         }
        //         else if (operation == CurrencyOperation.Increase)
        //         {
        //             KiemSoatCash_SoLuong((int)price, 1);
        //             HeThongNhacNho($"Đã cộng {price} Cash. Còn lại {FLD_RXPIONT} CASH", 10, "Market");
        //         }
        //
        //         Save_CashData();
        //         break;
        //     default:
        //         if (operation == CurrencyOperation.Decrease)
        //             KiemSoatGold_SoLuong(price, 0);
        //         else if (operation == CurrencyOperation.Increase) KiemSoatGold_SoLuong(price, 1);
        //
        //         UpdateMoneyAndWeight();
        //         break;
        // }
    }

    private int MarketPlace_RegisterItemInMarket(int itemPos, long itemId, string newProductCode, int amount,
        long price)
    {
        var reside1 = _player.Item_In_Bag[itemPos].FLD_RESIDE1; // Job
        var reside2 = _player.Item_In_Bag[itemPos].FLD_RESIDE2; // Loai Item
        var level = _player.Item_In_Bag[itemPos].FLD_LEVEL;
        var enhanced = _player.Item_In_Bag[itemPos].FLD_CuongHoaSoLuong;
        var attribute = _player.Item_In_Bag[itemPos].FLDThuocTinhSoLuong;
        var option = _player.Item_In_Bag[itemPos].FLD_MAGIC0;
        int filter1, filter2 = 0, filter3 = 0, filter4 = 0, filter5 = 0;
        switch (reside2)
        {
            case 4:
                filter1 = 1;
                filter2 = reside1;
                filter3 = level % 10 + 1;
                filter4 = enhanced;
                filter5 = attribute;
                break;
            case 1:
                filter1 = 2;
                filter2 = reside2;
                filter3 = reside1;
                filter4 = level % 10 + 1;
                filter5 = enhanced;
                break;
            case 2:
            case 5:
            case 6:
                filter1 = 2;
                filter2 = reside2;
                filter3 = level % 10 + 1;
                filter4 = enhanced;
                filter5 = 0;
                break;
            case 7:
            case 8:
            case 10:
                filter1 = 3;
                filter2 = level % 10 + 1;
                filter3 = 0;
                filter4 = 0;
                filter5 = 0;
                break;
            case 16:
                filter3 = option / 100000;
                filter4 = 0;
                filter5 = 0;
                if (_stoneDict.TryGetValue(itemId, out var val))
                {
                    //Các loại cường hóa hợp thành thạch
                    filter1 = val[0];
                    filter2 = val[1];
                }
                else if (_petIds.Contains(itemId))
                {
                    filter1 = 7;
                    filter2 = 1;
                }
                else if (_petEns.Contains(itemId))
                {
                    filter1 = 7;
                    filter2 = 5;
                }
                else if (_buaChu.Contains(itemId))
                {
                    filter1 = 8;
                    filter2 = 3;
                }
                else if (itemId == 800000013)
                {
                    // Handle Khi cong
                    filter1 = 6;
                }
                else
                {
                    filter1 = 10;
                }

                break;
            case 18:
                filter1 = 8;
                filter2 = 3;
                break;
            case 19:
                filter1 = 8;
                filter2 = 4;
                break;
            case 23:
                // Trang bị pet
                filter1 = 7;
                filter2 = 5;
                break;
            case 25:
                // Trang sức pet
                filter1 = 7;
                filter2 = 3;
                break;
            default:
                filter1 = 10;
                filter2 = 0;
                filter3 = 0;
                filter4 = 0;
                filter5 = 0;
                break;
        }

        //LogHelper.WriteLine(LogLevel.Debug, $"filter1 {filter1} filter2 {filter2} filter3 {filter3} filter4 {filter4} filter5 {filter5}");
        // TODO : Check 1 nhân vật chỉ được gắn 30 item bán
        const string queryName = @"SELECT FLD_NAME FROM TBL_XWWL_ITEM WHERE FLD_PID = @ItemId";
        var queryParam = new List<SqlParameter>
        {
            new("@ItemID", _player.Item_In_Bag[itemPos].GetVatPham_ID)
        };
        var nameTable = DBA.GetDBToDataTable(queryName, queryParam.ToArray(), "PublicDb");
        var itemName = "";
        if (nameTable.Rows.Count > 0) itemName = nameTable.Rows[0]["FLD_NAME"].ToString();


        const string query = """
                             
                                         INSERT INTO MARKETPLACE (SELLER_ID,PRODUCT_CODE,ITEM_NAME, SELLER_NAME, BASE_AMOUNT, BASE_PRICE, FLD_PRICE,CURRENT_AMOUNT,FILTER_1,FILTER_2,FILTER_3,FILTER_4,FILTER_5,ITEM,CREATED_AT,EXPIRED_AT)
                                         VALUES (@SellerId,@ProductCode,@ItemName,@SellerName,@BaseAmount,@BasePrice,@FldPrice,@CurrentAmount,@Filter1,@Filter2,@Filter3,@Filter4,@Filter5,@Item,@CreatedAt,@ExpiredAt)
                             """;
        var sqlParameters = new List<SqlParameter>
        {
            new("@SellerId", _player.AccountID),
            new("@ProductCode", newProductCode),
            new("@ItemName", itemName),
            new("@SellerName", _player.CharacterName.Replace(" ", "").Trim()),
            new("@BaseAmount", amount),
            new("@BasePrice", price),
            new("@FldPrice", price * 97 / 100), // Phí 3%
            new("@CurrentAmount", amount),
            new("@Filter1", filter1),
            new("@Filter2", filter2),
            new("@Filter3", filter3),
            new("@Filter4", filter4),
            new("@Filter5", filter5),
            new("@Item", _player.Item_In_Bag[itemPos].VatPham_byte),
            new("@CreatedAt", DateTime.Now),
            new("@ExpiredAt", DateTime.Now.AddDays(7))
        };
        var rowsAffected = DBA.ExeSqlCommand(query, sqlParameters.ToArray(), "BBG").GetAwaiter().GetResult();
        return rowsAffected;
    }

    private void MarketPlace_ValidateMaxItemsOnSale(int type)
    {
        const string query = @"SELECT COUNT(*) FROM MARKETPLACE WHERE STATUS='SELLING' AND SELLER_NAME=@Player";
        var param = new SqlParameter("@Player", _player.CharacterName);
        var totalSelling = (int)DBA.GetDBValue_3(query, new[] { param }, "BBG");

        if (totalSelling < 20) return;
        _player.HeThongNhacNho("Không thể đăng ký !! Mỗi nhân vật chỉ được bán tối đa 20 vật phẩm cùng lúc", 10,
            "Market");
        MarketPlace_Response(type, int.MaxValue - 4);
        throw new("Cannot register item: Max 20 items can be sold at once.");
    }

    public bool MarketPlace_Update_Status(int productId, string status)
    {
        const string updateQuery = "UPDATE MARKETPLACE SET STATUS = @Status WHERE ID = @productId";

        var updateParameters = new List<SqlParameter>
        {
            new("@Status", status),
            new("@productId", productId)
        };

        var rowsAffected = DBA.ExeSqlCommand(updateQuery, updateParameters.ToArray(), "BBG").GetAwaiter().GetResult();
        return rowsAffected > 0;
    }

    public bool MarketPlace_Update_Status(string productCode, string status)
    {
        const string updateQuery = "UPDATE MARKETPLACE SET STATUS = @Status WHERE PRODUCT_CODE = @ProductCode";

        var updateParameters = new List<SqlParameter>
        {
            new("@Status", status),
            new("@ProductCode", productCode)
        };
        var rowsAffected = DBA.ExeSqlCommand(updateQuery, updateParameters.ToArray(), "BBG").GetAwaiter().GetResult();
        return rowsAffected > 0;
    }

    public void MarketPlace_History(Players player, byte[] data, int offset)
    {
        int type = BitConverter.ToInt16(data, 0xC - offset);
        //int page = BitConverter.ToInt16(data, 0xE - offset);

        var countQuery = $"""
                          SELECT COUNT(*) AS ToTalItems from MARKETPLACE WHERE
                                  STATUS = 'SELLING' AND SELLER_NAME = '{player.CharacterName}'
                          """;
        var totalItems = (int)DBA.GetDBValue_3(countQuery, "BBG");
        var itemsQuery = $"""
                          
                                          SELECT
                                             *
                                          FROM
                                              MARKETPLACE
                                          WHERE
                                              STATUS = 'SELLING'
                                              AND SELLER_NAME = '{player.CharacterName}'
                                                  ;
                                  
                          """;

        var totalPages = (totalItems + 9) / 10;
        // start 
        var itemsTable = DBA.GetDBToDataTable(itemsQuery, "BBG");
        const int packetSize = 10; // Define max items per packet

        LogHelper.WriteLine(LogLevel.Debug, $"Total {totalItems} page {totalPages} {itemsTable.Rows.Count}");
        var currentPage = 0;
        for (var i = 0; i < totalItems; i += packetSize)
        {
            var packetData = new SendingClass();
            packetData.Write2(type);
            packetData.Write4(1);
            packetData.Write4(int.MaxValue);
            //packetData.Write4(currentPage); // Set current page for each packet

            var itemsInThisPacket = Math.Min(packetSize, totalItems - i);
            for (var j = i; j < i + itemsInThisPacket; j++)
            {
                var row = itemsTable.Rows[j];
                try
                {
                    MarketPlace_Write_One_Item(packetData, row, "BASE_PRICE", currentPage);
                }
                catch (Exception ex)
                {
                    LogHelper.WriteLine(LogLevel.Debug, $"Error {ex.Message}");
                }
            }

            _player.Client?.SendPak(packetData, 17921, 0,true);
            currentPage++; // Increment page counter after sending a packet
        }

        itemsTable.Dispose();
    }

        public int MarketPlace_Get_Expired_Seconds(DateTime expiredAt)
        {
            var minutesLeft = (int)(expiredAt - DateTime.Now).TotalSeconds;
            return minutesLeft;
        }
    }

    /// <summary>
    /// Loại thao tác tiền tệ
    /// </summary>
    public enum CurrencyOperation
    {
        Decrease,
        Increase
    }
}