# Hướng dẫn cài đặt và sử dụng Thiên cơ các Boss Đặc Biệt

## Giới thiệu

Thiên cơ các Boss Đặc Biệt được thiết kế để cải thiện cách quản lý trạng thái và logic xử lý boss. <PERSON>hi<PERSON><PERSON> cơ các mới đưa toàn bộ logic xử lý boss v<PERSON><PERSON>p<PERSON>, gi<PERSON><PERSON> tách biệt rõ ràng hơn giữa quản lý boss và xử lý trạng thái boss.

### Các loại boss được hỗ trợ
- **Boss T<PERSON><PERSON> (WorldBoss)**: Xu<PERSON>t hiện theo lịch trình cố định, chắc chắn rơi vật phẩm hiếm
- **Boss <PERSON> (GuildBoss)**: <PERSON><PERSON><PERSON> hiện theo lịch trình hoặc triệu hồi, c<PERSON> tỉ lệ rơi vật phẩm hiếm (15%)
- **Boss Tri<PERSON> (SummonBoss)**: <PERSON><PERSON><PERSON><PERSON> chơi triệ<PERSON> hồ<PERSON>, có tỉ lệ rơi vật phẩm hiếm thấp (5%)

### Lợi ích của Thiên cơ các mới:
- Quản lý trạng thái boss cục bộ thay vì global
- Xử lý các sự kiện boss một cách độc lập với mỗi boss
- Lịch trình boss được quản lý tập trung
- Dễ dàng mở rộng và thêm các loại boss mới
- Thông báo tự động về trạng thái boss
- Thiên cơ các phân phối phần thưởng linh hoạt theo loại boss
- Tùy chỉnh tỉ lệ rơi đồ hiếm theo từng loại boss

## Cài đặt

### Bước 1: Thêm các file mới
Các file mới đã được tạo trong thư mục `RxjhServer/Neo/`:
- `NpcClass.WorldBoss.cs` - Partial class mở rộng NpcClass với logic Boss
- `NpcClass.Events.cs` - Partial class xử lý sự kiện cho NpcClass
- `NpcClass.GuiDiTuVongWrapper.cs` - Wrapper cho phương thức GuiDiTuVongSoLieu
- `GuiDiTuVongSoLieuHook.cs` - Hook xử lý sự kiện boss chết
- `WorldBossManager.cs` - Class quản lý boss
- `BossType.cs` - Định nghĩa các loại boss và Thiên cơ các phần thưởng

### Bước 2: Tích hợp với code hiện tại
Để tích hợp hoàn toàn Thiên cơ các mới, bạn cần thực hiện một số thay đổi trong code hiện tại:

#### 1. Khởi tạo NpcHooks trong World.cs hoặc trong hàm khởi tạo server:
```csharp
// Thêm vào phương thức khởi tạo
NpcHookInitializer.Initialize();
```

#### 2. Sử dụng GuiDiTuVongSoLieuWrapper thay vì GuiDiTuVongSoLieu
Tìm tất cả nơi gọi `GuiDiTuVongSoLieu` và thay thế bằng `GuiDiTuVongSoLieuWrapper`, hoặc thêm hook vào:
```csharp
// Sau khi gọi GuiDiTuVongSoLieu
NpcClassHooks.AfterGuiDiTuVongSoLieu(npc);
```

#### 3. Đổi tên WorldBoss_Process trong World.cs
Biến `World.WorldBoss_Process` không còn được sử dụng trong Thiên cơ các mới. Thay vào đó, mỗi boss quản lý trạng thái riêng của nó.

## Sử dụng

### Khởi tạo Thiên cơ các quản lý boss
```csharp
World.WorldBossEvent = new HeroWorldBossClass();
```

### Thêm lịch trình boss mới
```csharp
// Boss thế giới (chắc chắn rơi vật phẩm hiếm)
World.WorldBossEvent.AddBossSchedule(11, 0, bossType: BossType.WorldBoss);

// Boss bang hội (có tỉ lệ 15% rơi vật phẩm hiếm)
World.WorldBossEvent.AddBossSchedule(14, 0, bossId: 15424, bossType: BossType.GuildBoss);
```

### Gọi boss thủ công
```csharp
// Boss thế giới
World.WorldBossEvent.SpawnWorldBossNow();

// Boss bang hội
World.WorldBossEvent.SpawnGuildBossNow();

// Boss triệu hồi (cần vị trí của người chơi)
World.WorldBossEvent.SpawnSummonBossNow(mapId, x, y);
```

### Phân phối phần thưởng thủ công
```csharp
World.WorldBossEvent.DistributeRewards(bossID);
```

## Cấu hình phần thưởng

### Cấu hình boss
Bạn có thể thay đổi cấu hình cho các loại boss trong file `BossType.cs`:

```csharp
// Cấu hình cho Boss Thế Giới
BossRewardConfig worldBossConfig = new BossRewardConfig
{
    BossId = 15423,
    Type = BossType.WorldBoss,
    Name = "Boss Thế Giới",
    GuaranteedRareDrops = 2, // Số lượng vật phẩm hiếm chắc chắn sẽ rơi
    PotentialDrops = new List<BossDropItem>
    {
        new BossDropItem(1000001390, "Bảo Châu 1", true),
        new BossDropItem(1000001391, "Bảo Châu 2", true),
        new BossDropItem(1000001392, "Bảo Châu 3", true),
        new BossDropItem(1000001393, "Bảo Châu 4", true),
        new BossDropItem(1000001394, "Bảo Châu 5", true)
    }
};

// Cấu hình cho Boss Guild
BossRewardConfig guildBossConfig = new BossRewardConfig
{
    BossId = 15424,
    Type = BossType.GuildBoss,
    Name = "Boss Bang Hội",
    RareDropRate = 0.15f, // 15% tỉ lệ rơi vật phẩm hiếm
    PotentialDrops = new List<BossDropItem>
    {
        new BossDropItem(1000001390, "Bảo Châu 1", true),
        // ...
    }
};

// Cấu hình cho Boss triệu hồi
BossRewardConfig summonBossConfig = new BossRewardConfig
{
    BossId = 15425,
    Type = BossType.SummonBoss,
    Name = "Boss Triệu Hồi",
    RareDropRate = 0.05f, // 5% tỉ lệ rơi vật phẩm hiếm
    PotentialDrops = new List<BossDropItem>
    {
        new BossDropItem(1000001390, "Bảo Châu 1", true),
        // ...
    }
};
```

### Thêm hoặc cập nhật cấu hình
Bạn có thể thêm hoặc cập nhật cấu hình cho boss trong runtime:
```csharp
// Lấy cấu hình boss
var bossConfig = BossConfigManager.GetBossConfig(bossId);

// Thay đổi cấu hình
bossConfig.RareDropRate = 0.2f; // Tăng tỉ lệ lên 20%

// Cập nhật lại cấu hình
BossConfigManager.AddOrUpdateBossConfig(bossConfig);
```

## Ghi chú kỹ thuật

### Cách xử lý khi boss chết
1. Khi boss chết, phương thức `GuiDiTuVongSoLieu` được gọi
2. Thông qua wrapper hoặc hook, `OnNpcDeath` được kích hoạt
3. `OnNpcDeath` kiểm tra nếu đây là boss đặc biệt và gọi `HandleWorldBossDeath`
4. `HandleWorldBossDeath` cập nhật trạng thái, dừng timer và phân phối phần thưởng

### Thiên cơ các phân phối phần thưởng
Khi boss bị tiêu diệt, Thiên cơ các phân phối phần thưởng theo quy trình:
1. Gọi `World.WorldBoss_TraoThuongPoint(bossID)` để trao điểm thưởng cơ bản
2. Dựa trên loại boss, Thiên cơ các sẽ xác định danh sách vật phẩm có thể rơi:
   - **WorldBoss**: Chắc chắn rơi ra N vật phẩm hiếm (GuaranteedRareDrops)
   - **GuildBoss**: Mỗi vật phẩm hiếm có tỉ lệ RareDropRate để rơi (mặc định 15%)
   - **SummonBoss**: Mỗi vật phẩm hiếm có tỉ lệ RareDropRate để rơi (mặc định 5%)
3. Phân phối vật phẩm dựa trên đóng góp damage (người gây nhiều damage có cơ hội cao hơn)

### Trạng thái boss
Boss sử dụng enum WorldBossState để quản lý trạng thái:
- `Disabled = 0` - Boss bị vô hiệu hóa
- `Waiting = 1` - Chờ boss được gọi
- `Announced = 2` - Đã thông báo boss sẽ xuất hiện
- `Active = 3` - Boss đang hoạt động
- `Killed = 4` - Boss đã bị giết
- `Expired = 5` - Hết thời gian đánh boss

## Lưu ý quan trọng
- Đảm bảo gọi `GuiDiTuVongSoLieuWrapper` thay vì `GuiDiTuVongSoLieu` trực tiếp
- Nếu không thể sử dụng wrapper, đảm bảo hook `NpcClassHooks.AfterGuiDiTuVongSoLieu` được gọi sau `GuiDiTuVongSoLieu`
- Thử nghiệm kỹ trước khi triển khai vào môi trường sản xuất
- Kiểm tra kỹ các tỉ lệ rơi vật phẩm để đảm bảo cân bằng game 