using System;
using System.Collections;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;

using Akka.Actor;
using RxjhServer.HelperTools;
using RxjhServer.HeroBoss;
using RxjhServer.Network;
using RxjhServer.ManageZone;
using RxjhServer.GroupQuest;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using HeroYulgang.Core.Actors;
using Microsoft.Data.SqlClient;
using RxjhServer.Database;
using HeroYulgang.Core.Network;
using HeroYulgang.Database.Entities.Public;

namespace RxjhServer;

public enum WorldState
{
	Stopped,
	Starting,
	Running,
	Stopping
}

public partial class World
{
	public static int Event_13Trieu_Progress;

	public static readonly Dictionary<int, X_WebShop_Category> WebShopCategoryList;
	public static Dictionary<string, X_<PERSON>_Bao_Cac_Loai> WebShopItemList;

	public static int Event_13Trieu_Total_Round;

	public static int Event_13Trieu_ThanNu_HP;

	public static int Event_13Trieu_TongDiemSo;

	public static Dictionary<int, Players> DanhSachNhanVat_ThamGia_Event_HeroDefend;

	public static int VoHuan_GioiHan_MoiNgay;

	public static int 讨伐副本最多人数;

	public static X_Boss_Event_FireDragon_Loai 讨伐战副本;

	public static EventClass_DaiChienHon dch_event_check;

	public static Dictionary<int, Players> DanhSachNhanVat_ThamGia_DCH;

	public static List<NpcClass> Chuyen_BanDo_2_3;

	public static List<NpcClass> Chuyen_BanDo_4_5;

	public static List<NpcClass> Chuyen_BanDo_6_7;

	public static List<NpcClass> Chuyen_BanDo_8;

	public static List<NpcClass> Chuyen_BanDo_9;

	public static List<NpcClass> Chuyen_BanDo_10;

	public static string SuTuHong_Lenh;

	public static int TLC_CTC_BatDau_QuangBa;

	public static int NguongSo_NhipTim;

	public static string NguoiChiemGiu_Den_Tenma;

	public static string ThienMa_ThanCung_TheLuc_ChiemLinh;

	public static string Offline_DanhQuai;

	public static int Offline_Pill_ID;

	public static string Offline_BuffMau_DP;

	public static int Offline_TreoMay_PhamVi;

	public static int Offline_Nhat_Item;

	public static int QuaiVatTuVongBienMatTriHoanThoiGian;

	public static int CoMoRa_TuyetRoiHayKhong;

	public static int XoaTen_TheLucChien_Random;

	public static string[] CauHinhBossTheoDame;

	public static string KiemSoat_SoLuong_DropBoss;

	public static string[] CauHinh_Quai_NhiemVu;

	public static int Debug;

	public static int ON_OFF_case_16666;

	public static int GioiHan_CuongHoa_TrangSuc;

	public static string[] TongXacSuat_CuongHoaTrangSuc;

	public static int TyLe_CuongHoa_TrangSuc1;

	public static int TyLe_CuongHoa_TrangSuc2;

	public static int TyLe_CuongHoa_TrangSuc3;

	public static int TyLe_CuongHoa_TrangSuc4;

	public static int TyLe_CuongHoa_TrangSuc5;

	public static int TyLe_CuongHoa_TrangSuc6;

	public static int TyLe_CuongHoa_TrangSuc7;

	public static int TyLe_CuongHoa_TrangSuc8;

	public static int TyLe_CuongHoa_TrangSuc9;

	public static int TyLe_CuongHoa_TrangSuc10;

	public static int TyLe_CuongHoa_TrangSuc11;

	public static int TyLe_CuongHoa_TrangSuc12;

	public static int TyLe_CuongHoa_TrangSuc13;

	public static int TyLe_CuongHoa_TrangSuc14;

	public static int TyLe_CuongHoa_TrangSuc15;

	public static int TyLe_CuongHoa_TrangSuc16;

	public static int CuongHoa_ThanThu_1_10;

	public static int CuongHoa_ThanThu_11_15;

	public static int CuongHoa_ThanThu_16_20;

	public static int CuongHoa_ThanThu_21_25;

	public static int CuongHoa_ThanThu_26_30;

	public static int CuongHoa_ThanThu_36_40;

	public static int CuongHoa_ThanThu_31_35;

	public static int CuongHoa_ThanThu_41_50;

	public static int CuongHoa_ThanThu_51_60;

	public static int CuongHoa_ThanThu_61_70;

	public static int CuongHoa_ThanThu_71_80;

	public static int CuongHoa_ThanThu_81_85;

	public static int CuongHoa_ThanThu_86_90;

	public static int CuongHoa_ThanThu_91_95;

	public static int CuongHoa_ThanThu_96_99;

	public static int HopThanh_Pet_Dong_1;

	public static int HopThanh_Pet_Dong_2;

	public static int HopThanh_Pet_Dong_3;

	public static int HopThanh_Pet_Dong_4;

	public static int Rate_Pet_CuongHoa_Level_1;

	public static int Rate_Pet_CuongHoa_Level_2;

	public static int Rate_Pet_CuongHoa_Level_3;

	public static int Rate_Pet_CuongHoa_Level_4;

	public static int Rate_Pet_CuongHoa_Level_5;

	public static int Rate_Pet_CuongHoa_Level_6;

	public static int Rate_Pet_CuongHoa_Level_7;

	public static int Rate_Pet_CuongHoa_Level_8;

	public static int Rate_Pet_CuongHoa_Level_9;

	public static int Rate_Pet_CuongHoa_Level_10;

	public static int Rate_Pet_CuongHoa_Level_11;

	public static int Rate_Pet_CuongHoa_Level_12;

	public static int Rate_Pet_CuongHoa_Level_13;

	public static int Rate_Pet_CuongHoa_Level_14;

	public static int Rate_Pet_CuongHoa_Level_15;

	public static int Rate_Pet_CuongHoa_Level_16;

	public static int Rate_Pet_CuongHoa_Level_17;

	public static int Rate_Pet_CuongHoa_Level_18;

	public static int Rate_Pet_CuongHoa_Level_19;

	public static int Rate_Pet_CuongHoa_Level_20;

	public static int Mo_TT_1;

	public static int Mo_TT_2;

	public static int Mo_TT_3;

	public static int Mo_TT_4;

	public static int Mo_TT_5;

	public static int Mo_TT_6;

	public static int Mo_TT_7;

	public static int CoHayKo_MoRa_Log_TreoShop;

	public static int CoHayKhongMoRa_CongDichChuyen;

	public static string NpcAdd;

	public static int GhiLogMuaNPC;

	public static int GhiLogBanNPC;

	public static int GhiLogMoHop;

	public static int GhiLogTyLeHopThanh;

	public static int GhiLogTyLeHopThanh_HoaLongThach;

	public static int GhiLogCuongHoa;

	public static string GhiLogCuongHoa_GiaTri;

	public static string[] GhiLogCuongHoa_GiaTri_Array;

	public static int GhiLogBuaCuongHoa;

	public static string GhiLogBuaCuongHoa_GiaTri;

	public static string[] GhiLogBuaCuongHoa_GiaTri_Array;

	public static int GhiLogCuongHoaAoChoang;

	public static string GhiLogCuongHoaAoChoang_GiaTri;

	public static string[] GhiLogCuongHoaAoChoang_GiaTri_Array;

	public static int GhiLogTienHoaThanThu;

	public static int GhiLogCuongHoaThanThu;

	public static string GhiLogCuongHoaThanThu_GiaTri;

	public static string[] GhiLogCuongHoaThanThu_GiaTri_Array;

	public static int GhiLogCuongHoaTrangSuc;

	public static string GhiLogCuongHoaTrangSuc_GiaTri;

	public static string[] GhiLogCuongHoaTrangSuc_GiaTri_Array;

	public static int GhiLogCuongHoaThucTinh;

	public static string GhiLogCuongHoaThucTinh_GiaTri;

	public static string[] GhiLogCuongHoaThucTinh_GiaTri_Array;

	public static int PhanThuong_ThemVao_TLC;

	public static int Co_Hay_Khong_Thuong_Them_X2VoHuan_X300_TLC;

	public static int Gioi_han_EXP_khi_co_TLC;

	public static int CoHayKhongMoRaGuiThu;

	public static int CoHayKo_Bat_Tat_TangHinh_Ninja;

	public static int Khoa_ChucNang_Auto;

	public static double Rate_Gold_Party;

	public static double Rate_Exp_Party;

	public static double Rate_KyNang_TT_Party;

	public static double Rate_KyNang_Party;

	public static double Drop_Cach_1_Level_Quai;

	public static double Drop_Cach_2_Level_Quai;

	public static double Drop_Cach_3_Level_Quai;

	public static double Drop_Cach_4_Level_Quai;

	public static double Drop_Cach_5_Level_Quai;

	public static double Drop_Cach_6_Level_Quai;

	public static double Drop_Cach_7_Level_Quai;

	public static double Drop_Cach_8_Level_Quai;

	public static double Drop_Cach_9_Level_Quai;

	public static double Drop_Cach_10_Level_Quai;

	public static double Drop_Cach_11_Level_Quai;

	public static double Drop_Cach_12_Level_Quai;

	public static double Drop_Cach_13_Level_Quai;

	public static double Drop_Cach_14_Level_Quai;

	public static double Drop_Cach_15_Level_Quai;

	public static double Drop_Cach_16_Level_Quai;

	public static double Drop_Cach_17_Level_Quai;

	public static double Drop_Cach_18_Level_Quai;

	public static double Drop_Cach_19_Level_Quai;

	public static double Drop_Cach_20_Level_Quai;

	public static double Giam_Gold_Drop_Level_100_114;

	public static double Giam_Gold_Drop_Level_115_119;

	public static double Giam_Gold_Drop_Level_120_124;

	public static double Giam_Gold_Drop_Level_125_128;

	public static double Giam_Gold_Drop_Level_129_130;

	public static double Gold_Drop_Cach_1_Level_Quai;

	public static double Gold_Drop_Cach_2_Level_Quai;

	public static double Gold_Drop_Cach_3_Level_Quai;

	public static double Gold_Drop_Cach_4_Level_Quai;

	public static double Gold_Drop_Cach_5_Level_Quai;

	public static double Gold_Drop_Cach_6_Level_Quai;

	public static double Gold_Drop_Cach_7_Level_Quai;

	public static double Gold_Drop_Cach_8_Level_Quai;

	public static double Gold_Drop_Cach_9_Level_Quai;

	public static double Gold_Drop_Cach_10_Level_Quai;

	public static double Gold_Drop_Cach_11_Level_Quai;

	public static double Gold_Drop_Cach_12_Level_Quai;

	public static double Gold_Drop_Cach_13_Level_Quai;

	public static double Gold_Drop_Cach_14_Level_Quai;

	public static double Gold_Drop_Cach_15_Level_Quai;

	public static double Gold_Drop_Cach_16_Level_Quai;

	public static double Gold_Drop_Cach_17_Level_Quai;

	public static double Gold_Drop_Cach_18_Level_Quai;

	public static double Gold_Drop_Cach_19_Level_Quai;

	public static double Gold_Drop_Cach_20_Level_Quai;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19;

	public static double Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20;

	public static double Bonus_Drop_BanDo_TDKH;

	public static double Time_Delay_Auto_Offline;

	public static int BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu;

	public static int MaxKhiCong_Tren1KhiCong;

	public static Dictionary<int, Event_Dragon_ToDoi_Loai> ToDoi_Event;

	public static double ServerVer;

	public static double ServerVerD;

	public static string ServerRegTime;

	public static string[] DiDong_TocDo;

	public static int MainServer;

	public static bool Droplog;

	public static int ServerIDStart;

	public static int OffLine_SoLuong;

	public static int TreoMay_Offline;

	public static string Key;

	public static string Key2;

	public static AtapiDevice Keyk;

	public static int AllItmelog;

	public static int CoMo_ThiTruongTraoDoiTienXu;

	public static int WhetherTheCurrentLineIsSilver;

	public static int ChoPhep_MoShop;

	public static int ChoPhep_GiaoDich;

	public static int ChoPhep_TreoMay;

	public static int TanCongLienTuc_SoLanTanCong;

	public static int LienTucCongKich_ThoiGianHieuQua;

	public static int BatHopPhap_CongKich_SuDungPlugin;

	public static bool AlWorldlog;

	public static int Time_Auto_Mo_Tuyet_Roi;

	public static Dictionary<int, X_Kiem_Tra_Thiet_Bi_Loai> KiemTraThietBiList;

	public static ThreadSafeDictionary<int, ActorNetState> list;

	// Các thuộc tính và biến thành viên từ Core.World
	private static World _instance;
	private readonly HeroYulgang.Core.Actors.ActorSystemManager _actorSystemManager;
	private readonly LoginServerClient _loginServerClient;

	public LoginServerClient loginServerClient => _loginServerClient;

	private CancellationTokenSource _cancellationTokenSource;
	private Task _updateTask;
	private readonly TimeSpan _updateInterval = TimeSpan.FromMilliseconds(100); // 10 updates per second
	private DateTime _lastUpdateTime;
	private long _totalUpdates;
	private long _totalUpdateTimeMs;

	public WorldState State { get; private set; } = WorldState.Stopped;
	public DateTime StartTime { get; private set; }
	public TimeSpan Uptime => DateTime.Now - StartTime;
	public float AverageUpdateTimeMs => _totalUpdates > 0 ? (float)_totalUpdateTimeMs / _totalUpdates : 0;

	public static World Instance => _instance ??= new World();

	public static List<int> KhiCong_CoBan_ID;

	public static List<X_Toa_Do_Class> DCH_ToaDo_TanHinh;

	public static Dictionary<string, EventTopClass> EventTop;

	public static List<X_Toa_Do_Class> TLC_ToaDo_CamPK;

	public static List<X_Toa_Do_Class> Event_ToaDo_DuongDua;

	public static List<X_Toa_Do_Class> DCH_ToaDo_UnCheck;

	public static Dictionary<string, EventTopDCHClass> EventTopDCH;

	public static ThreadSafeDictionary<int, Players> allConnectedChars;

	public static Dictionary<string, string> BannedList;

	public static Dictionary<int, X_Thang_Thien_Khi_Cong_Tong_Loai> ThangThienKhiCongList;

	public static Dictionary<int, X_Nhiem_Vu_Loai> NhiemVulist;

	//public static Dictionary<string, Database> Db;

	public static Dictionary<int, NpcClass> Npc_HaiThuoc_NamLam;

	public static Dictionary<int, NpcClass> Npc_DuongDua_F1;

	public static Dictionary<int, NpcClass> Npc_TetDoanNgo;

	public static Dictionary<int, NpcClass> Npc_KhuLuyenTap;

	public static Dictionary<int, NpcClass> NpcEvent_GiangSinh;

	public static Dictionary<int, NpcClass> NpcEvent_Tet_GiapThin;

	public static Dictionary<int, NpcClass> NpcEvent_DCH;

	public static Dictionary<int, NpcClass> Npc_Boss_Map;

	public static Queue m_Disposed;

	public static Queue SqlPool;

	public static Dictionary<int, MapClass> MapList;

	public static List<GiftCodeRewardsClass> GiftCodeRewards;

	public static List<GiftcodeClass> GiftCode;

	public static int Open_Auto_GiftCode;

	public static string[] Config_Auto_GiftCode;

	public static Dictionary<int, X_Bang_Chien_Class> HelpList;

	public static Dictionary<int, X_Bang_Chien_Class> HelpNameList;

	public static Dictionary<int, string> Maplist;

	public static Dictionary<int, NpcClass> NpcList;

	public static Dictionary<int, Wedding> Weddinglist;

	public static Dictionary<string, GuildMember> GuildList;

	public static Dictionary<int, X_Bach_Bao_Cac_Loai> BachBaoCat_ThuocTinhVatPhamClassList;

	public static Dictionary<int, PVPClass> PVP_TrangBi;

	public static Dictionary<int, PVPClass> PVP_TrangBi_16x_Chan;

	public static int MonChien_ProgressNew;

	public static int ThoiGianTanCong_Interval;

	public static int AtPort;

	public static bool IsTheDragonHallInUse;

	public static bool IsHuaMarriageHallInUse;

	public static bool WhetherTheSacramentalHallIsInUse;

	public static int TocDoLonNhat_VuotQuaSoLan_ThaoTac;

	public static int SoLan_VuotQuaChoPhep_Trong30Giay;

	public static int Time_Delay_Item_8000008_Con_Lai;

	public static int Time_Delay_Item_8000008_Cung;

	public static int Time_Delay_Item_8000008_ThichKhach;

	public static int Time_Delay_Item_8000008_MLC;

	public static int SoLuong_Item_DropBoss;

	public static int FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han;

	public static int HuyenBotPhai;

	public static int NamMinhHieu;

	public static int TamTaQuan;

	public static int LieuChinhQuan;

	public static int ThanVoMon;

	public static int LieuThienPhu;

	public static int TungNguyetQuan;

	public static int BachVoQuan;

	public static int BacHaiBangCung;

	public static int NamLam;

	public static int HoHapCoc;

	public static int XichThienGioi;

	public static int ThienDuSon;

	public static int ThanhDiaKiemHoang;

	public static int KhuLuyenTap1;

	public static int KhuLuyenTap9;

	public static int PhongThanKhau;

	public static int Rate_Rot_Quest;

	public static int Item_Quest;

	public static int So_Luong_Quest;

	public static int ID_Quai;

	public static int PhanThuongVoHuan_Quest;

	public static int LuongVoHuan_CuoiTuan;

	public static int GioiHanVoHuanMoiNgay_Cap2;

	public static int GioiHanVoHuanMoiNgay_Cap3;

	public static int GioiHanVoHuanMoiNgay_Cap4;

	public static int GioiHanVoHuanMoiNgay_Cap5;

	public static int GioiHanVoHuanMoiNgay_Cap6;

	public static int GioiHanVoHuanMoiNgay_Cap7;

	public static int GioiHanVoHuanMoiNgay_Cap8;

	public static int GioiHanVoHuanMoiNgay_Cap9;

	public static int GioiHanVoHuanMoiNgay_Cap10;

	public static int GioiHanVoHuanMoiNgay_Cap11;

	public static double Time_TuHa_KTTX;

	public static double TC_0_EXP;

	public static double TC_1_EXP;

	public static double TC_2_EXP;

	public static double TC_3_EXP;

	public static double TC_4_EXP;

	public static double TC_5_EXP;

	public static double TT_1_EXP;

	public static double TT_2_EXP;

	public static double TT_3_EXP;

	public static double TT_4_EXP;

	public static double TT_5_EXP;

	public static double TT_6_EXP;

	public static string GioiHan_LopNhanVat_KhongDuocKhoiTao;

	public static double Dame_Skill_Dao;

	public static double Dame_Skill_Kiem;

	public static double Dame_Skill_Thuong;

	public static double Dame_Skill_Kich_Hoat_Cung;

	public static double Dame_Skill_DaiPhu;

	public static double Dame_Skill_Ninja;

	public static double Dame_Skill_CamSu;

	public static double Dame_Skill_HBQ;

	public static double Dame_Skill_DHL;

	public static double Dame_Skill_QuyenSu;

	public static double Dame_Skill_MLC;

	public static double Dame_Skill_TH;

	public static double Dame_Skill_TN;

	public static double Skill_KichHoat_Cung_11x;

	public static double Skill_KichHoat_Cung_12x;

	public static double Skill_KichHoat_Cung_13x;

	public static double Skill_KichHoat_Cung_14x;

	public static double Skill_KichHoat_Cung_15x;

	public static double Skill_KichHoat_Cung_16x;

	public static double Skill_KichHoat_Nin_11x;

	public static double Skill_KichHoat_Nin_12x;

	public static double Skill_KichHoat_Nin_13x;

	public static double Skill_KichHoat_Nin_14x;

	public static double Skill_KichHoat_Nin_15x;

	public static double Skill_KichHoat_Nin_16x;

	public static double QuyenSu_PK_Combo_1;

	public static double QuyenSu_PK_Combo_2;

	public static double QuyenSu_PK_Combo_3;

	public static double QuyenSu_PK_Combo_4;

	public static double QuyenSu_PK_Combo_5;

	public static double QuyenSu_Monster_Combo_1;

	public static double QuyenSu_Monster_Combo_2;

	public static double QuyenSu_Monster_Combo_3;

	public static double QuyenSu_Monster_Combo_4;

	public static double QuyenSu_Monster_Combo_5;

	public static double LoggerVersion;

	public static List<DropClass> Drop_Jl;

	public static Dictionary<string, string> TheLucChien_Giam_NguoiChoi;

	public static Dictionary<int, IngameRanking> TheLucChien_XepHang_SoLieu;

	public static Dictionary<int, IngameRanking> VoLamHuyetChien_XepHang_SoLieu;

	public static Dictionary<int, IngameRanking> BangPhai_XepHang_SoLieu;

	public static List<X_Mon_Phai_Xep_Hang> BangPhaiXepHangSoLieu;

	public static Dictionary<int, X_Treo_May_Offline_Loai> TreoMay_SoLieu;

	public static int BanDau_ChiemLinh_Ngay;

	public static int Cong_Thanh_CuongHoa_Level;

	public static List<X_Mon_Phai_Lien_Minh_Trang_Thai> MonPhaiLienMinhTrangThai;

	public static double LucPhongNguVoCong_KiemSoat;

	public static double LucCongKichVoCong_BoiSo;

	public static int TurnOnCardSkills;

	public static int CardSkillLevel;

	public static int ToaDo_ThoiGianLamMoi;

	public static int KiemSoatThoiGian_TanCong;

	public static int CongHienNguyenBao_SoLuong;

	public static int CongHienNguyenBao_DiemVinhDu;

	public static int Script;

	public static int X2KinhNghiem_CapDo_GioiHanCaoNhat;

	public static int X2TienTai_CapDo_GioiHanCaoNhat;

	public static int X2LichLuyen_CapDo_GioiHanCaoNhat;

	public static int X2BaoSuat_CapDo_GioiHanCaoNhat;

	public static double X2CapDo_GioiHanCaoNhat_BoiSo;

	public static int GioiHan_Level_CaoNhat;

	public static int GioiHan_CuongHoa_ThanThu;

	public static int Gioi_Han_Level_100_MoHop_Gift_Code;

	public static int XacNhan_Dame_Train_Client_den_Server;

	public static int Event_MoHop_SoLuong;

	public static int TestChiSoConfigTrongSource;

	public static int TestChiSoConfigTrongSource1;

	public static double TestChiSoConfigTrongSource2;

	public static double TestChiSoConfigTrongSource3;

	public static double TestChiSoConfigTrongSource4;

	public static int Test_Packet_Bytes_1;

	public static int Test_Packet_Bytes_2;

	public static int Test_Packet_Bytes_3;

	public static int Test_Packet_Bytes_4;

	public static int Random_TamHoaTuDinh_Chinh;

	public static int Random_TamHoaTuDinh_Ta;

	public static int CoHayKo_KichHoat_TaiKhoan_VaoDuocGame;

	public static int ON_OFF_LogChangeSkill;

	public static int Speed_Attack_Monster;

	public static int Min_Delay_Skill_Monster;

	public static int Max_Delay_Skill_Monster;

	public static int Giam_Delay_Skill_CharDao_PK;

	public static int Giam_Delay_Skill_CharKiem_PK;

	public static int Giam_Delay_Skill_CharThuong_PK;

	public static int Giam_Delay_Skill_CharCung_PK;

	public static int Giam_Delay_Skill_CharDP_PK;

	public static int Giam_Delay_Skill_CharTK_PK;

	public static int Giam_Delay_Skill_CharCS_PK;

	public static int Giam_Delay_Skill_CharHBQ_PK;

	public static int Giam_Delay_Skill_CharDHL_PK;

	public static int Giam_Delay_Skill_CharQS_PK;

	public static int Giam_Delay_Skill_CharMLC_PK;

	public static int Giam_Delay_Skill_CharTH_PK;

	public static int Giam_Delay_Skill_CharTN_PK;

	public static int Giam_Delay_Skill_CharDao_Train;

	public static int Giam_Delay_Skill_CharKiem_Train;

	public static int Giam_Delay_Skill_CharThuong_Train;

	public static int Giam_Delay_Skill_CharCung_Train;

	public static int Giam_Delay_Skill_CharDP_Train;

	public static int Giam_Delay_Skill_CharTK_Train;

	public static int Giam_Delay_Skill_CharCS_Train;

	public static int Giam_Delay_Skill_CharHBQ_Train;

	public static int Giam_Delay_Skill_CharDHL_Train;

	public static int Giam_Delay_Skill_CharQS_Train;

	public static int Giam_Delay_Skill_CharMLC_Train;

	public static int Giam_Delay_Skill_CharTH_Train;

	public static int Giam_Delay_Skill_CharTN_Train;

	public static int Random_Type_Box_Item;

	public static double Thuong_ON_CPVP_NhanThemSatThuong;

	public static double Thuong_OFF_CPVP_NhanThemSatThuong;

	public static double Quyen_ON_CPVP_NhanThemSatThuong;

	public static double Quyen_OFF_CPVP_NhanThemSatThuong;

	public static int Random_Rate_KinhNghiem;

	public static int Random_Rate_Gold;

	public static int Random_Rate_KyNang;

	public static int Random_Rate_KyNangThangThien;

	public static int Kenh_Treo_Shop;

	public static int Kenh_MoHop_Event;

	public static int Time_SK1_Char_Cung;

	public static int Time_SK1_Char_DHL;

	public static int Time_SK1_Char_ConLai;

	public static int Item_Byte_Length_92;

	public static int Item_Db_Byte_Length;

	public static int VatPham_ThuocTinh_KichThuoc;

	public static int ThangThienKyNang_CapDoTangThem;

	public static int AutGC;

	public static double locklist;

	public static double locklist2;

	public static List<object> locklist3;

	public static int MoiLan_SuTuHong_TieuHaoNguyenBao;

	public static int ChoPhep_MoSoLuong_NhieuHon;

	public static int ChoPhep_MoSoLuong_NhieuHon_Mac_Address;

	public static int SoNguoiDangKyCongThanhChien;

	public static int Gioi_han_so_nguoi_vao_party;

	public static int Attack_Find_doi_phuong;

	public static int Def_Find_doi_phuong;

	public static int CoMoRa_HeThong_MonChien;

	public static int HeThong_MonChien_CanNguyenBao;

	public static int HeThong_MonChien_Gio;

	public static int HeThong_MonChien_Phut;

	public static int HeThong_MonChien_Giay;

	public static int BangChienThang_ID;

	public static int SoLuong_NguyenBao_MoiLanTieuThu;

	public static int MaximumNumberOfConnectionsToTheGameLoginPort;

	public static int ResetNhoHon_KhongTheVaoTLC_OLD;

	public static int ResetLonHon_KhongTheVaoTLC_NEW;

	public static int Gioi_han_chenh_lech_2_ben_vao_TLC;

	public static int Gioi_han_acc_vao_TLC;

	public static int Hieu_Ung_Ao_Choang_Chinh_Nam;

	public static int Hieu_Ung_Ao_Choang_Chinh_Nu;

	public static int Hieu_Ung_Ao_Choang_Ta_Nam;

	public static int Hieu_Ung_Ao_Choang_Ta_Nu;

	public static int PhanThuongTop20_TLC;

	public static int PhanThuongBenThangTLC;

	public static int PhanThuongBenThuaTLC;

	public static double Diem_TLC_Chinh;

	public static double Diem_TLC_Ta;

	public static int MaximumConnectionTimeOfTheGameLoginPort;

	public static int KiemTraVatPham_BatHopPhap;

	public static int KiemTraCacHoatDong_BatHopPhap;

	public static bool KiemTraCacMuc_BatHopPhap;

	public static int VatPhamCaoNhatCongKichGiaTri;

	public static int VatPhamCaoNhatPhongNguGiaTri;

	public static int VatPhamCaoNhatHPGiaTri;

	public static int VatPhamCaoNhatNoiCongGiaTri;

	public static int VatPhamCaoNhatTrungDichGiaTri;

	public static int VatPhamCaoNhatNeTranhGiaTri;

	public static int VatPhamCaoNhatCongKichVoCongGiaTri;

	public static int VatPhamCaoNhatKhiCongGiaTri;

	public static int VatPhamCaoNhatHopThanhGiaTri;

	public static int VatPhamCaoNhatPhuHonGiaTri;

	public static int VatPhamCaoNhatPhongNguVoCongGiaTri;

	public static int GioiHanCapDo_ToDoi;

	public static int CongTac_PhatHienNhipTim;

	public static int HoatDong_PhatHienNhipTim;

	public static int NguongThoiGian_PhatHienNhipTim;

	public static int ThoiLuong_PhatHienNhipTim;

	public static int CheDo_AnToan_TieuThuNguyenBao;

	public static int CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong;

	public static int CapDo_TrucTuyen;

	public static int NganPhieuDoiLayNguyenBao;

	public static int CoMoRaNganPhieu_DoiNguyenBaoHayKhong;

	public static int GoiQua_TrucTuyen;

	public static int Log_Hop_Code_Event;

	public static int LayDuoc_KinhNghiem_CapDo_ChenhLech;

	public static int PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong;

	public static int KhoaThietBi_TonNguyenBao;

	public static int MoKhoaThietBi_TonNguyenBao;

	public static int PhaiChang_MoRaTreoMay_BanThuong;

	public static int TreoMayBanThuong_ThoiGianChuKyLapLai;

	public static int TreoMayBanThuong_VoHuan;

	public static int TreoMayTieuTru_ThienAc;

	public static int Hut_Quai_SoLuong;

	public static int ServerChoPKHayKhong;

	public static int CoHayKo_BOSS_Rot_Item;

	public static int CoHayKo_Lock_GuiTien_Warehouse;

	public static int CoHayKo_Lock_RutTien_Warehouse;

	public static int TreoMayBanThuong_YeuCauDangCap;

	public static bool BOSSKhuVucKep_CoBatHayKhong;

	public static string TreoMayX2_ThoiGian;

	public static int PhaiChang_HoTroMoRong_CacVatPham_ChuSo;

	public static int ThoiGianTreoMay_AnToan;

	public static double TyLe_TangCuong_VatPham;

	public static int VuKhiPK_RoiDoBen;

	public static int DoPhongNguPK_RoiDoBen;

	public static int SoNguyenBao_ToiDa_TrongMotGiaoDich;

	public static int GioiHan_TongSoNguyenBao_1TaiKhoan;

	public static int HoatDong_PhatHienPhoi;

	public static int PhaiChang_MoRaVoHuan_HeThong;

	public static int PKCapDo_ChenhLech;

	public static int VoHuanBaoVe_CapDo;

	public static string TuVong_GiamBotVoHuan_SoLuong;

	public static string HeThongTaiChe_SoLan;

	public static double VoHuanThuVeTiLePhanTram;

	public static int ItemRecord;

	public static int LoginRecord;

	public static int DropRecord;

	public static int StoreRecord;

	public static int DrugRecord;

	public static int SyntheticRecord;

	public static int RecordKeepingDays;

	public static bool BlockIP;

	public static List<IPAddress> BipList;

	public static int AutomaticConnectionTime;

	public static int VersionVerificationTime;

	public static bool MainSocket;

	public static string SocketState;

	public static bool AutomaticallyOpenTheConnection;

	public static int SoLuongKetNoi_ToiDaChoPhep;

	public static bool Disconnect;

	public static bool AddToFilterList;

	public static bool CloseTheConnection;

	public static int WorldTime;

	public static int WTeamId;

	public static int ver;

	public static int tf;

	public static int jlMsg;

	public static int week;

	public static int SilverCoinSquareServerID;

	public static int Event_Tet_GiapThin_Progress;

	public static int Event_Noel_Progress;

	public static int Event_HaiThuoc_Progress;

	public static int Event_Duong_Dua_F1_Progress;

	public static int Event_TetDoanNgo_Progress;

	public static int Event_KhuLuyenTap_Progress;

	public static int Boss_Map_Progress;

	public static int TheLucChien_Progress;

	public static int TheLucChien_MidTime;

	public static int TheLucChien_ChinhPhai_DiemSo;

	public static int TheLucChien_TaPhai_DiemSo;

	public static int TheLucChien_ChinhPhai_SoNguoi;

	public static int TheLucChien_TaPhai_SoNguoi;

	public static int DCH_MidTime;

	public static int DCH_ChinhPhai_DiemSo;

	public static int DCH_TaPhai_DiemSo;

	public static int DCH_ChinhPhai_SoNguoi;

	public static int DCH_TaPhai_SoNguoi;

	public static int DCH_Progress;

	public static int Random_So_Drop;

	public static int Diem_Guild_Theo_Level;

	public static int Restart_Recovery;

	public static int Check_LoginAcc_2_Recovery;

	public static int Check_LoginAcc_Recovery;

	public static int CheckBugGold_Recovery;

	public static int EventExpALL_Recovery;

	public static int CoHayKo_BatTat_Event_Exp_TanThu;

	public static int CoHayKo_ON_OFF_Event_Exp_CuoiTuan;

	public static int CoHayKo_Auto_Kick_Feed_TLC;

	public static int CoHayKo_KTTX_KoCan_VatPham_HoTro;

	public static int CoHayKhong_MoRa_Backup_Database;

	public static int Backup_MoRa_Gio;

	public static int Backup_MoRa_Phut;

	public static int Backup_MoRa_Giay;

	public static int CoHayKhong_MoRa_Boss;

	public static int Boss_MoRa_Gio;

	public static int Boss_MoRa_Phut;

	public static int Boss_MoRa_Giay;

	public static int ON_OFF_Event_Boss_Map;

	public static int Boss_Map_Gio;

	public static int Boss_Map_Phut;

	public static int Boss_Map_Giay;

	public static int Boss_Map_Gio_Time_2;

	public static int Boss_Map_Phut_Time_2;

	public static int Boss_Map_Giay_Time_2;

	public static int ON_OFF_Kick_Mem_Ban_888;

	public static int CoHayKo_check_su_dung_SK1;

	public static int CapDoChoPhepGiaoDich;

	public static int CoHayKo_Drop_CoMoThongBao;

	public static int CoHayKhong_Trung_IP_PK_Tinh_Diem_TLC;

	public static int TheLucChien_Random_MoRa;

	public static int TheLucChien_MoRa;

	public static int TheLucChien_MoRa_Gio;

	public static int TheLucChien_MoRa_Phut;

	public static int TheLucChien_MoRa_Giay;

	public static int TheLucChien_TongThoiGian;

	public static int Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang;

	public static int Time_KichHoat_HopTraiNghiem_Gio;

	public static int Time_KichHoat_HopTraiNghiem_Phut;

	public static int Time_KichHoat_HopTraiNghiem_Giay;

	public static int Time_Huy_HopTraiNghiem_Gio;

	public static int Time_Huy_HopTraiNghiem_Phut;

	public static int Time_Huy_HopTraiNghiem_Giay;

	public static int Bat_Tat_SuKien_TetDoanNgo;

	public static int TetDoanNgo_Gio;

	public static int TetDoanNgo_Phut;

	public static int TetDoanNgo_Giay;

	public static int ON_OFF_Event_HaiThuoc_NamLam;

	public static int Gold_Nhan_Hop_Qua_HKGH;

	public static int HaiThuoc_NamLam_Gio;

	public static int HaiThuoc_NamLam_Phut;

	public static int HaiThuoc_NamLam_Giay;

	public static int ON_OFF_MoRa_Event_Boss_PK;

	public static int Boss_PK_MoRa_Gio;

	public static int Boss_PK_MoRa_Phut;

	public static int Boss_PK_MoRa_Giay;

	public static int DaiChienHon_MoRa_PhoBan;

	public static int DCH_MoRa_Gio;

	public static int DCH_MoRa_Phut;

	public static int DCH_MoRa_Giay;

	public static int ThoiGian_ChuanBi_DCH;

	public static int TongThoiGian_DCH;

	public static int CoHayKhong_MoRa_Event_Noel;

	public static int Noel_MoRa_Gio;

	public static int Noel_MoRa_Phut;

	public static int Noel_MoRa_Giay;

	public static int ID_Monster_Drop_Event_GiangSinh;

	public static int PhanThuong_Drop_Event_GiangSinh;

	public static int Event_KhuLuyenTap_MoRa;

	public static int KhuLuyenTap_MoRa_Gio;

	public static int KhuLuyenTap_MoRa_Phut;

	public static int KhuLuyenTap_MoRa_Giay;

	public static int ON_OFF_WORLD_BOSS;

	public static int WORLD_BOSS_MoRa_Gio;

	public static int WORLD_BOSS_MoRa_Phut;

	public static int WORLD_BOSS_MoRa_Giay;

	public static int CoHayKhong_MoRa_Event_TetGiapThin;

	public static int TetGiapThin_MoRa_Gio;

	public static int TetGiapThin_MoRa_Phut;

	public static int TetGiapThin_MoRa_Giay;

	public static int ID_Monster_Drop_Event_TetGiapThin;

	public static int PhanThuong_Drop_Event_TetGiapThin;

	public static int PhanThuong_Drop_Event_TetGiapThin_2;

	public static int CoHayKo_Pet_Train_Exp;

	public static int Time_Exp_Pet_Hour;

	public static int Time_Add_Exp_Pet_Minute;

	public static int ThoiGianChuanBi_ChoTheLucChien;

	public static int TheLucChien_ThuongLoai;

	public static int TheLucChien_ThuongSoLuong;

	public static int TheLucChien_ThuongGoiVatPham;

	public static int SoLuongChoPhep_NguoiChoiDatCuoc;

	public static float SanTapPhamVi_HieuQua;

	public static int PhiVaoCua_ToiThieu;

	public static double SanTapTienThue_TiLePhanTram;

	public static int SoLanTronThoat_ChoPhep;

	public static int NguyenBaoBiTru_SauKhiTruDiem;

	public static int TienBiTru_SauKhiTruDiem;

	public static int GiaTri_Gold_AutoBan;

	public static int Gold_DoiTheLuc_qua_TaPhai;

	public static int Gold_DoiTheLuc_qua_ChinhPhai;

	public static int Phi_Doi_Character;

	public static int Win_TLC_Phan_Thuong_VoHuan;

	public static int Lose_TLC_Phan_Thuong_VoHuan;

	public static int Win_DCH_Phan_Thuong_VoHuan;

	public static int Lose_DCH_Phan_Thuong_VoHuan;

	public static int Eve90Progress;

	public static int Eve90_ThoiGian;

	public static ThreadSafeDictionary<int, Players> evePlayers;

	public static ThreadSafeDictionary<int, Players> DCHPlayers;

	public static bool isvip;

	public static int Phan_Tram_Chia_Drop_Gold;

	public static int Phan_Tram_Chia_Ky_Nang;

	public static int Phan_Tram_Chia_Ky_Nang_TT;

	public static int Phan_Tram_Chia_Exp;

	public static int DelayBomMau_KhiPK;

	public static int NguyenBao_HopThanh;

	public static long Money_Max;

	public static double GuiDi_TocDo;

	public static double PhatDong_GuiDi_TocDo;

	public static double TiepNhan_TocDo;

	public static int KinhNghiem_BoiSo;

	public static int TyLe_HapHon;

	public static int Tien_BoiSo;

	public static int LichLuyenBoiSo;

	public static double ThangThien_LichLuyen_BoiSo;

	public static double TiLe_HopThanh_DaTuLinh;

	public static double GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi;

	public static int Rate_Drop_Server;

	public static double TyLe_HopThanh;

	public static double TyLe_CuongHoa;

	public static int GioiHan_CuongHoa_AoChoang;

	public static double TyLe_TangCuong_AoChoang;

	public static double TyLe_NangCap_ThietBi;

	public static double TyLe_NangCap_TrangSuc;

	public static string BaibaoCourtAddress;

	public static string BachBaoCacServerIP;

	public static int BaibaogeServerPort;

	public static string AccountVerificationServerIP;

	public static int AccountVerificationServerPort;

	public static int GameServerPort;

	public static int GameServerPort1;

	public static int GameServerPort2;

	public static int GameServerPort3;

	public static int GameServerPort4;

	public static int GameServerPort5;

	public static int ForwarderGatewayServicePort;

	public static int VIPLine;

	public static int MaximumOnline;

	public static int ServerGroupID;

	public static int ServerID;

	public static string ServerName;

	public static string ThongBao_KhiVaoGame;

	public static AtapiDevice KeykF;

	public static int SuTuHong_ID;

	public static Queue SuTuHongList;

	public static Dictionary<int, X_Cong_Thanh_Chien> CongThanhChienList;

	public static List<Players> DangKyDanhSach_NguoiChoiCongThanhChien;

	public static Dictionary<int, X_Cong_Thanh_So_Lieu> CongThanhSoLieu_list;

	public static int ThienMaThanCungPhoTuong_PhaiChangTuVong;

	public static int ThienMaThanCungDaiMon_PhaiChangTuVong;

	public static int ThienMaThanCungDongMon_PhaiChangTuVong;

	public static int CongThanhChien_CoMoRaHayKhong;

	public static int CongThanhChien_MoRa_Gio;

	public static int CongThanhChien_MoRa_Phut;

	public static int CongThanhChien_MoRa_Giay;

	public static int CongThanhChien_Progress;

	public static int CongThanhChien_ThoiGianChuanBi;

	public static int CongThanhChien_TongThoiGian;

	public static int SuTuHong_Max_SoLuong;

	public static int Encrypt;

	public static string EncryptionKey;

	public static byte[] g_cur_key;

	public static int Encrypt2;

	public static int PacketTitle;

	public static int HanChe_CapDo_Nhom;

	public static int VipThongBaoTrucTuyen;

	public static string NoiDung_VipThongBaoTrucTuyen;

	public static int ThongBaoTrucTuyen_BinhThuong;

	public static string NoiDung_ThongBaoTrucTuyen_BinhThuong;

	public static string VIP_BanDo;

	public static string BanDoKhoa_Chat;

	public static string SqlJl;

	public static int KiemTra_NguyenBao;

	public static int SoLuongTrangBi_ToiDa;

	public static int AutomaticArchive;

	public static int SilverCoinSquareServerPort;

	public static double TyLe_CuongHoa_ToiCao;

	public static string TongXacSuat_CuongHoaThanThu;

	public static long VuongLong_GoldCoin;

	public static double PhaiChangMoRa_CuuTuyenBanDo;

	public static double CuuTuyen_Gold_TiLe;

	public static double BanDoKhac_TiLe_Gold;

	public static int VuongLongDiaDo_ID;

	public static string FileMD5;

	public static string TaiTao_DaKimCuong_CongKich;

	public static string TaiTao_DaKimCuong_TruyThuong;

	public static string TaiTao_DaKimCuong_VoCong;

	public static string TaiTao_DaKimCuong_TrungDich;

	public static string TaiTao_DaKimCuong_SinhMenh;

	public static string TaiTao_HanNgocThach_PhongNgu;

	public static string TaiTao_HanNgocThach_NeTranh;

	public static string TaiTao_HanNgocThach_SinhMenh;

	public static string TaiTao_HanNgocThach_NoiCong;

	public static string TaiTao_HanNgocThach_VoPhong;

	public static double TyLePhanTram_PhongNguVoCong;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Dao;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Kiem;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Thuong;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Cung;

	public static double PhanTram_ChiSo_TrangBi_ULPT_DaiPhu;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Nin;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Cam;

	public static double PhanTram_ChiSo_TrangBi_ULPT_HBQ;

	public static double PhanTram_ChiSo_TrangBi_ULPT_DHL;

	public static double PhanTram_ChiSo_TrangBi_ULPT_Quyen;

	public static double PhanTram_ChiSo_TrangBi_ULPT_MLC;

	public static double PhanTram_ChiSo_TrangBi_ULPT_TH;

	public static double PhanTram_ChiSo_TrangBi_ULPT_TN;

	public static double TyLePhanTram_CongKichVoCong;

	public static int ChetCoMatKinhNghiemKhong;

	public static double PhanTram_GiamXuong_TienNhanDuoc;

	public static double VIPTyLe_HopThanhGiaTang_TiLePhanTram;

	public static double ThanNuPK_KhoangCach;

	public static double TuHaoPK_KhoangCach;

	public static double DaiPhuPK_KhoangCach;

	public static double CamSuPK_KhoangCach;

	public static double MaiLieuChanPK_KhoangCach;

	public static double CungTienPK_KhoangCach;

	public static double NhungNgheNghiepKhac_CongKichKhoangCach;

	public static double DaiPhu_DanhQuaiKhoangCach;

	public static double CungTien_DanhQuaiKhoangCach;

	public static double NhungNgheNghiepKhac_DanhQuaiKhoangCach;

	public static double ThienQuan_TyLePhanTramKinhNghiep_CoSo;

	public static int ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo;

	public static double ThienQuan_TyLePhanTramKinhNghiep_TangLen;

	public static int TangTiLe_RotVatPham_TrongDiaDo;

	public static int wg36;

	public static int wg37;

	public static int wg38;

	public static int wg39;

	public static int wg40;

	public static int wg35;

	public static int wg34;

	public static int wg33;

	public static int wg32;

	public static int wg31;

	public static int wg30;

	public static int wg29;

	public static int wg28;

	public static int wg27;

	public static int wg26;

	public static int wg25;

	public static int wf100;

	public static int wf95;

	public static int wf90;

	public static int wf85;

	public static int wf80;

	public static int wf78;

	public static int wf76;

	public static int wf74;

	public static int wf72;

	public static int wf70;

	public static int wf68;

	public static int g25;

	public static int g24;

	public static int g23;

	public static int g22;

	public static int g21;

	public static int g20;

	public static int f15;

	public static int f14;

	public static int f13;

	public static int f12;

	public static int f11;

	public static int f10;

	public static int TieuThu_ThietBi;

	public static int SoLuong_CapNhat_ThietBi;

	public static int VatPhamThuong_MonChienID;

	public static int ChoTuLuyenMoRa_ID;

	public static int CoMoKhoa_PhatHienCongKich_HayKhong;

	public static int KhoaNguoi_SoLanTanCong_CaoNhat;

	public static int KhoaNguoi_CongKich_KiemTraThaoTac;

	public static int XemBoiToan_PhiTieuHao;

	public static string ipaddress;

	public static Dictionary<int, X_Che_Tac_Vat_Pham_Loai> CheTacVatPhamDanhSach;

	public static Dictionary<int, X_Che_Duoc_Vat_Pham_Loai> CheDuocVatPhamDanhSach;

	public static string DangNhap_TruyenThuNoiDung;

	public static string Nguoi_GuiThu;

	//public static AtServerConnect ycServerConnect;

	public static string CongHienNguyenBao_Lenh;

	public static string TrungSinh1;

	public static string TrungSinh2;

	public static string TrungSinh3;

	public static string TrungSinh4;

	public static string TrungSinh5;

	public static string TrungSinh6;

	public static string TrungSinh7;

	public static string TrungSinh8;

	public static string TrungSinh9;

	public static string TrungSinh10;

	public static string TrungSinh11;

	public static string TrungSinh12;

	public static string Track;

	public static string CTC;

	public static string Lenh_Buff_Cung;

	public static string Lenh_Buff_Bong;

	public static string Lenh_Buff_CPVP;

	public static string Lenh_Buff_DaiPhu;

	public static string Lenh_Party_1;

	public static string Kick_UserName_TLC;

	public static string Add_Npc_map_TLC_se_thanh_Boss;

	public static string AddItemGM;

	public static string Add_Gold_GM;


	public static string Add_Gold_Mem;

	public static string Add_Drop;

	public static string Lenh_Nhiem_Vu;

	public static string AddKyNang;

	public static string Add_Kinh_Nghiem;

	public static string Xoa1;

	public static string Xoa2;

	public static string Xoa3;

	public static string Xoa4;

	public static string Xoa5;

	public static string XoaTatCa;

	public static string Relog;

	public static string DoiTheLuc;

	public static string DoiGioiTinh;

	public static string Add_GM;

	public static string XoaNguSac;

	public static string ChuyenDoiKenh;

	public static string XoaKimPhu;

	public static string XoaHuyenVuPhu;

	public static string XoaThuoc;

	public static string TreoShopOffine;

	public static string TanHinh;

	public static string XoaKhiCongChungThangThien;

	public static string XoaNguHanhVuKhi;

	public static string XoaNguHanhTrangBi;

	public static string Check_Info_Char;

	public static string Info_Thong_Tin;

	public static string[] BangHang1PhanThuong_DanhHieu;

	public static string[] BangHang2PhanThuong_DanhHieu;

	public static string[] BangHang3PhanThuong_DanhHieu;

	public static string PartitionNumber;

	// public static CongThanhChien CongThanhChien_BatDau;

	public static object MatDatVatPham_Lock;

	public static object MoRuong_Lock;

	public static Dictionary<int, double> lever;

	public static Dictionary<int, double> Wxlever;

	public static Dictionary<int, X_Thong_Cao_Loai> ThongBao;

	public static Dictionary<int, X_Dang_Cap_Ban_Thuong_Loai> DangCapBanThuong;

	public static Dictionary<int, X_Khi_Cong_Tang_Them_Thuoc_Tinh> KhiCongTangThem;

	public static Dictionary<int, X_Trao_Doi_Vat_Pham_Loai> VatPhamTraoDoi;

	public static Dictionary<int, X_Trao_Doi_Vat_Pham_Loai> PhucHoi_DuLieu_VatPham;

	public static ThreadSafeDictionary<long, X_Mat_Dat_Vat_Pham_Loai> ItmeTeM;

	public static Dictionary<int, MonSterClss> MonsterTemplateList;

	public static Dictionary<int, ItmeClass> ItemList;

	public static Dictionary<int, X_Vo_Cong_Loai> MagicList;

	public static Dictionary<int, X_To_Doi_Class> WToDoi;

	public static Dictionary<int, BbgSellClass> BachBaoCac_SoLieu;

	public static X_Bang_Phai_Chien_HuyetChien HuyetChien;

	public static X_Bang_Phai_Chien_MonChien BangChien;

	public static Event_Boss_Map Boss_Map_Event;

	public static Event_Hai_Thuoc_NamLam HaiThuoc_NamLam_Event;

	public static Event_Duong_Dua_F1 Event_DuongDua_F1_ON_OFF;

	public static Event_Tet_Doan_Ngo TetDoanNgo_DietSauBo_Event;

	public static Event_KhuLuyenTap KhuLuyenTap_PK_Event;

	public static Event_Boss_GiangSinh EventNoel;

	public static Event_Tet_GiapThin Event_Tet_GiapThin_Check;

	public static List<ShopClass> Shop;

	public static List<X_Kiem_Tra_Vat_Pham_Loai> VatPhamKiemTra;

	public static List<KillClass> Kill;

	public static List<DropClass> Drop;

	public static List<DropClass> Drop_GS;

	public static List<DropClass> BossDrop;

	public static List<DropClass> DCH_Drop;

	public static List<OpenClass> Open;

	public static List<MoveClass> Mover;

	public static List<X_Toa_Do_Class> DiDong;

	public static List<ItemSellClass> Set_SoLieu;

	public static EventClass eve;

	public static X_Tien_Ma_Chien_Tuan_Hoan_Thong_Cao TienMaChienThongBao;

	public static bool tmc_flag;

	public static bool Bat_Event_Boss_PK;

	public static string TheLucChien_ThuongThuocTinh;

	public static string TheLucChien_ThuongVatPham;

	public static ThreadSafeDictionary<int, Players> allPVPChars;

	public static List<X_Toa_Do_Class> KhuTapLuyenPK;

	public static List<X_Toa_Do_Class> TheLucChien_KhuVuc;

	public static List<X_Toa_Do_Class> BangChien_KhuVuc;

	public static EvePVPClass EVEPVP;

	public static List<int> BOSSListTime;

	public static int Log;

	public static int VerifyServerLog;

	public static int jllog;

	public static int Newversion;

	//public static ScriptClass ScriptClass;

	public static Connect conn;

	public static int MoiLan_TieuHaoSoLuong;

	public static int MoiLanTaiTao_TieuHaoThietLap;

	public static int DanhThuong_KhoangCach_ThoiGian;

	public static int Max_CuongHoa_ThucTinh;

	public static double ThucTinh_VuKhi_TrangBi_Len_1;

	public static double ThucTinh_VuKhi_TrangBi_Len_2;

	public static double ThucTinh_VuKhi_TrangBi_Len_3;

	public static double ThucTinh_VuKhi_TrangBi_Len_4;

	public static double ThucTinh_VuKhi_TrangBi_Len_5;

	public static double ThucTinh_VuKhi_TrangBi_Len_6;

	public static double ThucTinh_VuKhi_TrangBi_Len_7;

	public static double ThucTinh_VuKhi_TrangBi_Len_8;

	public static double ThucTinh_VuKhi_TrangBi_Len_9;

	public static double ThucTinh_VuKhi_TrangBi_Len_10;

	public static double ThucTinh_VuKhi_TrangBi_Len_11;

	public static double ThucTinh_VuKhi_TrangBi_Len_12;

	public static double ThucTinh_VuKhi_TrangBi_Len_13;

	public static double ThucTinh_VuKhi_TrangBi_Len_14;

	public static double ThucTinh_VuKhi_TrangBi_Len_15;

	public static double ThucTinh_VuKhi_TrangBi_Len_16;

	public static double ThucTinh_VuKhi_TrangBi_Len_17;

	public static double ThucTinh_VuKhi_TrangBi_Len_18;

	public static double ThucTinh_VuKhi_TrangBi_Len_19;

	public static double ThucTinh_VuKhi_TrangBi_Len_20;

	public static double TyLe_HopThanh_1;

	public static double TyLe_HopThanh_2;

	public static double TyLe_HopThanh_3;

	public static double TyLe_HopThanh_4;

	public static double TyLe_HopThanh_HoaLongThach_dong_1;

	public static double TienHoa_ThanThu_1;

	public static double TienHoa_ThanThu_2;

	public static double TienHoa_ThanThu_3;

	public static double TienHoa_ThanThu_4;

	public static double TienHoa_ThanThu_5;

	public static double TienHoa_ThanThu_6;

	public static double TienHoa_ThanThu_7;

	public static double TienHoa_ThanThu_8;

	public static double CuongHoa_DoThan_1;

	public static double CuongHoa_DoThan_2;

	public static double CuongHoa_DoThan_3;

	public static double CuongHoa_DoThan_4;

	public static double CuongHoa_DoThan_5;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_1;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_2;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_3;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_4;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_5;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_6;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_7;

	public static double CuongHoa_DoThan_5_VuKhi_NangCap_8;

	public static double CuongHoa_DoThan_6;

	public static double CuongHoa_DoThan_7;

	public static double CuongHoa_DoThan_8;

	public static double CuongHoa_DoThan_9;

	public static double CuongHoa_DoThan_10;

	public static double CuongHoa_DoThan_11;

	public static double CuongHoa_DoThan_12;

	public static double CuongHoa_DoThan_13;

	public static double CuongHoa_DoThan_14;

	public static double CuongHoa_DoThan_15;

	public static double CuongHoa_DoThan_16;

	public static double CuongHoa_DoThan_17;

	public static double CuongHoa_DoThan_18;

	public static double CuongHoa_DoThan_19;

	public static double CuongHoa_DoThan_20;

	public static double CuongHoa_DoThan_21;

	public static double HatNgoc_NangCap_1;

	public static double HatNgoc_NangCap_2;

	public static double HatNgoc_NangCap_3;

	public static double HatNgoc_NangCap_4;

	public static double HatNgoc_NangCap_5;

	public static double HatNgoc_NangCap_6;

	public static double HatNgoc_NangCap_7;

	public static double HatNgoc_NangCap_8;

	public static double HatNgoc_NangCap_9;

	public static double HatNgoc_NangCap_10;

	public static double HatNgoc_NangCap_11;

	public static double HatNgoc_NangCap_12;

	public static double HatNgoc_NangCap_13;

	public static double HatNgoc_NangCap_14;

	public static double HatNgoc_NangCap_15;

	public static double Item_MuiTen_NangCap_1;

	public static double Item_MuiTen_NangCap_2;

	public static double Item_MuiTen_NangCap_3;

	public static double Item_MuiTen_NangCap_4;

	public static double Item_MuiTen_NangCap_5;

	public static double Item_MuiTen_NangCap_6;

	public static double Item_MuiTen_NangCap_7;

	public static double Item_MuiTen_NangCap_8;

	public static double Item_MuiTen_NangCap_9;

	public static double Item_MuiTen_NangCap_10;

	public static double Item_MuiTen_NangCap_11;

	public static double Item_MuiTen_NangCap_12;

	public static double Item_MuiTen_NangCap_13;

	public static double Item_MuiTen_NangCap_14;

	public static double Item_MuiTen_NangCap_15;

	public static double Rate_Item_BoXanh_Len_BoHong;

	public static double Rate_Item_BoHong_Len_QuaBanh;

	public static double Rate_Item_QuaBanh_Len_Chuot;

	public static double CuongHoa1TyLe_HopThanh;

	public static double CuongHoa2TyLe_HopThanh;

	public static double CuongHoa3TyLe_HopThanh;

	public static double CuongHoa4TyLe_HopThanh;

	public static double CuongHoa5TyLe_HopThanh;

	public static double CuongHoa6TyLe_HopThanh;

	public static double CuongHoa7TyLe_HopThanh;

	public static double CuongHoa8TyLe_HopThanh;

	public static double CuongHoa9TyLe_HopThanh;

	public static double CuongHoa10TyLe_HopThanh;

	public static double CuongHoa11TyLe_HopThanh;

	public static double CuongHoa12TyLe_HopThanh;

	public static double CuongHoa13TyLe_HopThanh;

	public static double CuongHoa14TyLe_HopThanh;

	public static double CuongHoa15TyLe_HopThanh;

	public static double CuongHoa16TyLe_HopThanh;

	public static double CuongHoa17TyLe_HopThanh;

	public static double CuongHoa18TyLe_HopThanh;

	public static double CuongHoa19TyLe_HopThanh;

	public static int CuongHoa_GioiHan_Item_NPC_DKT;

	public static int BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT;

	public static double BuaCuongHoa_TiLe1;

	public static double BuaCuongHoa_TiLe2;

	public static double BuaCuongHoa_TiLe3;

	public static double BuaCuongHoa_TiLe4;

	public static double BuaCuongHoa_TiLe5;

	public static double BuaCuongHoa_TiLe6;

	public static double BuaCuongHoa_TiLe7;

	public static double BuaCuongHoa_TiLe8;

	public static double BuaCuongHoa_TiLe9;

	public static double BuaCuongHoa_TiLe10;

	public static double BuaCuongHoa_TiLe11;

	public static double BuaCuongHoa_TiLe12;

	public static double BuaCuongHoa_TiLe13;

	public static double BuaCuongHoa_TiLe14;

	public static double BuaCuongHoa_TiLe15;

	public static double Dame_Min_ThichKhach;

	public static double Dame_Min_CamSu;

	public static double Buff_DameTrain_CamSu_VeLai_ChiSo_Goc;

	public static double Dame_Min_MLC;

	public static double Dame_Min_TH;

	public static double Dame_Combo_DHL;

	public static double Giam_Dame_NinJa;

	public static int VuKhi7GiaiDoan_TangThemCongKich;

	public static int VuKhi8GiaiDoan_TangThemCongKich;

	public static int VuKhi9GiaiDoan_TangThemCongKich;

	public static int VuKhi10GiaiDoan_TangThemCongKich;

	public static int VuKhi11GiaiDoan_TangThemCongKich;

	public static int VuKhi12GiaiDoan_TangThemCongKich;

	public static int VuKhi13GiaiDoan_TangThemCongKich;

	public static int VuKhi14GiaiDoan_TangThemCongKich;

	public static int VuKhi15GiaiDoan_TangThemCongKich;

	public static int VuKhi16GiaiDoan_TangThemCongKich;

	public static int VuKhi17GiaiDoan_TangThemCongKich;

	public static int VuKhi18GiaiDoan_TangThemCongKich;

	public static int VuKhi19GiaiDoan_TangThemCongKich;

	public static int YPhuc7GiaiDoan_TangThemPhongNgu;

	public static int YPhuc8GiaiDoan_TangThemPhongNgu;

	public static int YPhuc9GiaiDoan_TangThemPhongNgu;

	public static int YPhuc10GiaiDoan_TangThemPhongNgu;

	public static int YPhuc11GiaiDoan_TangThemPhongNgu;

	public static int YPhuc12GiaiDoan_TangThemPhongNgu;

	public static int YPhuc13GiaiDoan_TangThemPhongNgu;

	public static int YPhuc14GiaiDoan_TangThemPhongNgu;

	public static int YPhuc15GiaiDoan_TangThemPhongNgu;

	public static int YPhuc16GiaiDoan_TangThemPhongNgu;

	public static int YPhuc17GiaiDoan_TangThemPhongNgu;

	public static int YPhuc18GiaiDoan_TangThemPhongNgu;

	public static int YPhuc19GiaiDoan_TangThemPhongNgu;

	public static int HoThu7GiaiDoan_TangThemPhongNgu;

	public static int HoThu8GiaiDoan_TangThemPhongNgu;

	public static int HoThu9GiaiDoan_TangThemPhongNgu;

	public static int HoThu10GiaiDoan_TangThemPhongNgu;

	public static int HoThu11GiaiDoan_TangThemPhongNgu;

	public static int HoThu12GiaiDoan_TangThemPhongNgu;

	public static int HoThu13GiaiDoan_TangThemPhongNgu;

	public static int HoThu14GiaiDoan_TangThemPhongNgu;

	public static int HoThu15GiaiDoan_TangThemPhongNgu;

	public static int HoThu16GiaiDoan_TangThemPhongNgu;

	public static int HoThu17GiaiDoan_TangThemPhongNgu;

	public static int HoThu18GiaiDoan_TangThemPhongNgu;

	public static int HoThu19GiaiDoan_TangThemPhongNgu;

	public static int Giay7GiaiDoan_TangThemPhongNgu;

	public static int Giay8GiaiDoan_TangThemPhongNgu;

	public static int Giay9GiaiDoan_TangThemPhongNgu;

	public static int Giay10GiaiDoan_TangThemPhongNgu;

	public static int Giay11GiaiDoan_TangThemPhongNgu;

	public static int Giay12GiaiDoan_TangThemPhongNgu;

	public static int Giay13GiaiDoan_TangThemPhongNgu;

	public static int Giay14GiaiDoan_TangThemPhongNgu;

	public static int Giay15GiaiDoan_TangThemPhongNgu;

	public static int Giay16GiaiDoan_TangThemPhongNgu;

	public static int Giay17GiaiDoan_TangThemPhongNgu;

	public static int Giay18GiaiDoan_TangThemPhongNgu;

	public static int Giay19GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap7GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap8GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap9GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap10GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap11GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap12GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap13GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap14GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap15GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap16GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap17GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap18GiaiDoan_TangThemPhongNgu;

	public static int NoiGiap19GiaiDoan_TangThemPhongNgu;

	public static int CreateNewJob;

	public static int Eventx2;

	public static int IdItemX2;

	public static int Eventx3;

	public static int IdItemX3;

	public static int EventX2ExpStatus;

	public static int EventX3ExpStatus;

	public static int IdItem_Bonus;

	public static int IdItem_Party;

	public static double Event_Bonus_Rate;

	public static int 讨伐伤害排行1;

	public static int 讨伐伤害排行2;

	public static int 讨伐伤害排行3;

	public static string 讨伐账号排行1;

	public static string 讨伐账号排行2;

	public static string 讨伐账号排行3;

	public static int 伤害排行1;

	public static int 伤害排行2;

	public static int 伤害排行3;

	public static int 伤害排行4;

	public static int 伤害排行5;

	public static int 伤害排行6;

	public static int 伤害排行7;

	public static int 伤害排行8;

	public static int 伤害排行9;

	public static int 伤害排行10;

	public static int 伤害排行11;

	public static int 伤害排行12;

	public static int 伤害排行13;

	public static int 伤害排行14;

	public static int 伤害排行15;

	public static int 伤害排行16;

	public static int 伤害排行17;

	public static int 伤害排行18;

	public static int 伤害排行19;

	public static int 伤害排行20;

	public static string 账号排行1;

	public static string 账号排行2;

	public static string 账号排行3;

	public static string 账号排行4;

	public static string 账号排行5;

	public static string 账号排行6;

	public static string 账号排行7;

	public static string 账号排行8;

	public static string 账号排行9;

	public static string 账号排行10;

	public static string 账号排行11;

	public static string 账号排行12;

	public static string 账号排行13;

	public static string 账号排行14;

	public static string 账号排行15;

	public static string 账号排行16;

	public static string 账号排行17;

	public static string 账号排行18;

	public static string 账号排行19;

	public static string 账号排行20;

	public static int ThoiGian_KichHoat_MoHop_TraiNghiem_16x;

	public static int ChatAllServer_OnOff;

	public static Dictionary<int, X_BachBao_Loai> 百宝阁属性物品类list;

	public static Dictionary<int, X_BachBao_Loai> 百宝阁属性物品类list2;
	public static int Language_Charset = 1252;

	public static bool ChoPhepKetHonCungGioi { get; internal set; }
	public static int Time_Animation { get; internal set; }
	public static bool QueueAttack { get; internal set; }
	public static int Time_Animation_Physical { get; private set; }
	public static int TamThanNgungTuAnimationCung { get; internal set; }
	public static int TamThanNgungTuAnimationNinja { get; internal set; }

	public static double TMQH_LongCD_Rate_0 { get; private set; }
	public static double TMQH_LongCD_Rate_1 { get; private set; }
	public static double TMQH_LongCD_DMG_1 { get; private set; }
	public static double TMQH_LongCD_DMG_2 { get; private set; }
	public static double TMQH_LongCD_DMG_1_PK { get; private set; }
	public static double TMQH_LongCD_DMG_2_PK { get; private set; }

	public static double TMQH_Rate_0 { get; private set; }
	public static double TMQH_Rate_1 { get; private set; }
	public static double TMQH_DMG_1 { get; private set; }
	public static double TMQH_DMG_2 { get; private set; }
	public static double TMQH_DMG_1_PK { get; private set; }
	public static double TMQH_DMG_2_PK { get; private set; }
	public static bool Dev_Server { get; internal set; }

	public static int NPC_HP_AMP { get; private set; }
	public static int NPC_ATK_AMP { get; private set; }
	public static int NPC_DEF_AMP { get; private set; }
	public static int NPC_ACC_AMP { get; private set; }
	public static int NPC_EVA_AMP { get; private set; }

	public static int MONSTER_DEF_AMP_MAGIC { get; private set; }
	public static int MONSTER_DEF_AMP_PHYSICAL { get; private set; }
	public static int Ninja_TocKich_TocDo { get; internal set; }
	public static int QuyenSu_TroiChan_ThoiGian { get; internal set; }
	public static int WorldBoss_TimeWait { get; internal set; }
	public static int WorldBoss_Process { get; internal set; }
	public static Zone DefaultZone { get; private set; }
	public static int ItemHoatDongWorldBoss { get; private set; }
	public static CongThanhChien CongThanhChien_BatDau;

	public static HeroWorldBossClass WorldBossEvent;
	public static Dictionary<int, WorldBossContributeClass> List_WorldBossContribute;


	// Phương thức để lấy NPC session ID tiếp theo
	public static int GetNextMonsterSessionId()
	{
		// Sử dụng SessionIdManager để cấp phát SessionID cho NPC/Monster
		return HeroYulgang.Core.Managers.SessionIdManager.Instance.AllocateNpcMonsterSessionId();
	}

	public static int GetNextNpcSessionId()
	{
		// Sử dụng SessionIdManager để cấp phát SessionID cho NPC/Monster
		return HeroYulgang.Core.Managers.SessionIdManager.Instance.AllocateNpcSessionId();
	}

	// GroupQuestManager instance

	public void clear()
	{
		wg40 = 0;
		wg39 = 0;
		wg38 = 0;
		wg37 = 0;
		wg36 = 0;
		wg35 = 0;
		wg34 = 0;
		wg33 = 0;
		wg32 = 0;
		wg31 = 0;
		wg30 = 0;
		wg29 = 0;
		wg28 = 0;
		wg27 = 0;
		wg26 = 0;
		wg25 = 0;
		wf100 = 0;
		wf95 = 0;
		wf90 = 0;
		wf85 = 0;
		wf80 = 0;
		wf78 = 0;
		wf76 = 0;
		wf74 = 0;
		wf72 = 0;
		wf70 = 0;
		wf68 = 0;
		g25 = 0;
		g24 = 0;
		g23 = 0;
		g22 = 0;
		g21 = 0;
		g20 = 0;
		f15 = 0;
		f14 = 0;
		f13 = 0;
		f12 = 0;
		f11 = 0;
		f10 = 0;
	}

	public static void UpdateAllRankingData()
	{
		try
		{
			TheLucChien_XepHang_SoLieu.Clear();
			var dBToDataTable = DBA.GetDBToDataTable("SELECT top 20 * FROM TBL_VinhDuHeThong where FLD_TYPE = 1 Order By FLD_DiemSo  Desc");
			if (dBToDataTable.Rows.Count != 0)
			{
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					TheLucChien_XepHang_SoLieu.Add(i, new()
					{
						PlayerName = dBToDataTable.Rows[i]["FLD_TenNhanVat"].ToString(),
						GuildName = dBToDataTable.Rows[i]["FLD_BangPhai"].ToString(),
						PlayerJob = (int)dBToDataTable.Rows[i]["FLD_NgheNghiep"],
						Faction = (int)dBToDataTable.Rows[i]["FLD_TheLuc"],
						PlayerLevel = (int)dBToDataTable.Rows[i]["FLD_DangCap"],
						Point = (int)dBToDataTable.Rows[i]["FLD_DiemSo"],
						RankingType = 1
					});
				}
			}
			dBToDataTable.Dispose();
			VoLamHuyetChien_XepHang_SoLieu.Clear();
			var dBToDataTable2 = DBA.GetDBToDataTable("SELECT  top  20  *  FROM  TBL_VinhDuHeThong  where  FLD_TYPE  =  2  Order  By  FLD_DiemSo  Desc");
			if (dBToDataTable2.Rows.Count != 0)
			{
				for (var j = 0; j < dBToDataTable2.Rows.Count; j++)
				{
					VoLamHuyetChien_XepHang_SoLieu.Add(j, new()
					{
						PlayerName = dBToDataTable2.Rows[j]["FLD_TenNhanVat"].ToString(),
						GuildName = dBToDataTable2.Rows[j]["FLD_BangPhai"].ToString(),
						PlayerJob = (int)dBToDataTable2.Rows[j]["FLD_NgheNghiep"],
						Faction = (int)dBToDataTable2.Rows[j]["FLD_TheLuc"],
						PlayerLevel = (int)dBToDataTable2.Rows[j]["FLD_DangCap"],
						Point = (int)dBToDataTable2.Rows[j]["FLD_DiemSo"],
						RankingType = 2
					});
				}
			}
			dBToDataTable2.Dispose();
			BangPhai_XepHang_SoLieu.Clear();
			var dBToDataTable3 = DBA.GetDBToDataTable("SELECT  top  20  *  FROM  TBL_VinhDuHeThong  where  FLD_TYPE  =  3  Order  By  FLD_DiemSo  Desc");
			if (dBToDataTable3.Rows.Count != 0)
			{
				for (var k = 0; k < dBToDataTable3.Rows.Count; k++)
				{
					BangPhai_XepHang_SoLieu.Add(k, new()
					{
						GuildName = dBToDataTable3.Rows[k]["FLD_BangPhai"].ToString(),
						GuildMaster = dBToDataTable3.Rows[k]["FLD_BangPhaiMonChu"].ToString(),
						PlayerJob = (int)dBToDataTable3.Rows[k]["FLD_NgheNghiep"],
						Faction = (int)dBToDataTable3.Rows[k]["FLD_TheLuc"],
						PlayerLevel = (int)dBToDataTable3.Rows[k]["FLD_DangCap"],
						Point = (int)dBToDataTable3.Rows[k]["FLD_DiemSo"],
						RankingType = 3
					});
				}
			}
			dBToDataTable3.Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Update All Ranking Data error " + ex.Message);
		}
	}

	public static void Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_9(string string_0, int int_0)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55BA0060000051B400010000000A000000BC17**************************************************00333****************************************************************************************************************************************************************************************************************************************************************************************0000055AA");
			var bytes = Encoding.Default.GetBytes(string_0);
			Buffer.BlockCopy(bytes, 0, array, 46, bytes.Length);
			if (int_0 == 2)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(6077), 0, array, 18, 2);
			}
			foreach (var value in allConnectedChars.Values)
			{
				if (value.Client != null)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
					value.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "World Send Full Server VoHuan Title To Get News SoLieu error 111 !!! [" + string_0 + "]" + ex.Message);
		}
	}

	public static void Gui_Full_VoHuan_Title_TinTuc_Effect_VoHuan_8(string string_0, int int_0)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55C200E8040051B400010000000A0000006411**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************00000055AA");
			var bytes = Encoding.Default.GetBytes(string_0);
			Buffer.BlockCopy(bytes, 0, array, 46, bytes.Length);
			if (int_0 == 1)
			{
				Buffer.BlockCopy(BitConverter.GetBytes(4451), 0, array, 18, 2);
			}
			foreach (var value in allConnectedChars.Values)
			{
				if (value.Client != null)
				{
					Buffer.BlockCopy(BitConverter.GetBytes(value.SessionID), 0, array, 4, 2);
					value.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Gửi Danh Hiệu Full Server VoHuan Để Nhận Tin SoLieu error 222 !!! ![" + string_0 + "]" + ex.Message);
		}
	}

	public static double KiemTra_ToDoi_NgheNghiep(X_To_Doi_Class TeamClass_0, Players players_0)
	{
		return 0.0;
	}

	public static byte[] PacketConversion(byte[] byte_0, int int_0, int int_1)
	{
		if (int_1 == 0)
		{
			var array = new byte[int_0 + 1];
			Buffer.BlockCopy(byte_0, 0, array, 0, 4);
			Buffer.BlockCopy(byte_0, 4, array, 5, int_0 - 4);
			Buffer.BlockCopy(BitConverter.GetBytes(byte_0[2] + 1), 0, array, 2, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(byte_0[8] + 1), 0, array, 9, 2);
			return array;
		}
		var array2 = new byte[int_0 - 1];
		Buffer.BlockCopy(byte_0, 0, array2, 0, 4);
		Buffer.BlockCopy(byte_0, 5, array2, 4, int_0 - 5);
		Buffer.BlockCopy(BitConverter.GetBytes(array2.Length - 6), 0, array2, 2, 2);
		Buffer.BlockCopy(BitConverter.GetBytes(array2.Length - 12), 0, array2, 8, 2);
		return array2;
	}

	public static int TinhToan_NguoiChoiDatCuoc_KetQua(int int_0, int int_1, int int_2)
	{
		var num = RNG.Next(0, 9) + int_0 + int_1;
		try
		{
			var num2 = 0;
			var num3 = 0;
			foreach (var value in allConnectedChars.Values)
			{
				if (!value.Client.TreoMay && value.WhetherToBet && value.BettingOnTheSpecialCode == int_2)
				{
					if (value.DatCuocVao_TyLeCuocVaTienThuong == "单")
					{
						num2++;
					}
					else
					{
						num3++;
					}
				}
			}
			if (num % 2 == 0)
			{
				if (num3 != 0 && num2 != 0)
				{
					var num4 = (int)(num2 * SoLuongChoPhep_NguoiChoiDatCuoc * (1.0 - SanTapTienThue_TiLePhanTram) / num3);
					foreach (var value2 in allConnectedChars.Values)
					{
						if (!value2.Client.TreoMay && value2.WhetherToBet && value2.BettingOnTheSpecialCode == int_2)
						{
							if (value2.DatCuocVao_TyLeCuocVaTienThuong == "双")
							{
							}
							value2.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
							value2.BettingOnTheSpecialCode = 0;
							value2.WhetherToBet = false;
							value2.NguyenBao_TaiKhoanTrangThai = false;
						}
					}
				}
				else if (num2 == 0)
				{
					foreach (var value3 in allConnectedChars.Values)
					{
						if (!value3.Client.TreoMay && value3.WhetherToBet && value3.BettingOnTheSpecialCode == int_2)
						{
							if (value3.DatCuocVao_TyLeCuocVaTienThuong == "双")
							{
								value3.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
							}
							value3.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
							value3.BettingOnTheSpecialCode = 0;
							value3.WhetherToBet = false;
							value3.NguyenBao_TaiKhoanTrangThai = false;
						}
					}
				}
				else if (num3 == 0)
				{
					foreach (var value4 in allConnectedChars.Values)
					{
						if (!value4.Client.TreoMay && value4.WhetherToBet && value4.BettingOnTheSpecialCode == int_2)
						{
							if (value4.DatCuocVao_TyLeCuocVaTienThuong == "单")
							{
							}
							value4.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
							value4.BettingOnTheSpecialCode = 0;
							value4.WhetherToBet = false;
							value4.NguyenBao_TaiKhoanTrangThai = false;
						}
					}
				}
			}
			else if (num2 != 0 && num3 != 0)
			{
				var num5 = (int)(num3 * SoLuongChoPhep_NguoiChoiDatCuoc * (1.0 - SanTapTienThue_TiLePhanTram) / num2);
				foreach (var value5 in allConnectedChars.Values)
				{
					if (!value5.Client.TreoMay && value5.WhetherToBet && value5.BettingOnTheSpecialCode == int_2)
					{
						if (value5.DatCuocVao_TyLeCuocVaTienThuong == "单")
						{
						}
						value5.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
						value5.BettingOnTheSpecialCode = 0;
						value5.WhetherToBet = false;
						value5.NguyenBao_TaiKhoanTrangThai = false;
					}
				}
			}
			else if (num3 == 0)
			{
				foreach (var value6 in allConnectedChars.Values)
				{
					if (!value6.Client.TreoMay && value6.WhetherToBet && value6.BettingOnTheSpecialCode == int_2)
					{
						if (value6.DatCuocVao_TyLeCuocVaTienThuong == "单")
						{
						}
						value6.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
						value6.BettingOnTheSpecialCode = 0;
						value6.WhetherToBet = false;
						value6.NguyenBao_TaiKhoanTrangThai = false;
					}
				}
			}
			else if (num2 == 0)
			{
				foreach (var value7 in allConnectedChars.Values)
				{
					if (!value7.Client.TreoMay && value7.WhetherToBet && value7.BettingOnTheSpecialCode == int_2)
					{
						if (value7.DatCuocVao_TyLeCuocVaTienThuong == "双")
						{
						}
						value7.DatCuocVao_TyLeCuocVaTienThuong = string.Empty;
						value7.BettingOnTheSpecialCode = 0;
						value7.WhetherToBet = false;
						value7.NguyenBao_TaiKhoanTrangThai = false;
					}
				}
			}
			return num;
		}
		catch
		{
			return num;
		}
	}

	public static Players FindPlayerByName(string string_0)
	{
		foreach (var value in allConnectedChars.Values)
		{
			if (value.AccountID == string_0)
			{
				return value;
			}
		}
		return null;
	}

	public static Players FindPlayerBySession(int int_0)
	{
		if (allConnectedChars.TryGetValue(int_0, out var value))
		{
			return value;
		}
		return null;
	}

	public static Players KiemTra_Ten_NguoiChoi(string string_0)
	{
		foreach (var value in allConnectedChars.Values)
		{
			if (value.CharacterName == string_0)
			{
				return value;
			}
		}
		return null;
	}

	public static void GuiThongBao(string string_0)
	{
		foreach (var value in allConnectedChars.Values)
		{
			if (!value.Client.TreoMay)
			{
				value.SystemNotification(string_0);
			}
		}
	}

	public static void SystemRollingAnnouncement(string string_0)
	{
		foreach (var value in allConnectedChars.Values)
		{
			if (!value.Client.TreoMay)
			{
				value.SystemRollingAnnouncement(string_0);
			}
		}
	}

	public static void ProcessLionRoarQueue()
	{
		if (SuTuHongList.Count > 0)
		{
			var x_Su_Tu_Hong_Class = (X_Su_Tu_Hong_Class)SuTuHongList.Dequeue();
			SendLionRoarMessageBroadcastData(x_Su_Tu_Hong_Class.FLD_INDEX, x_Su_Tu_Hong_Class.UserName, x_Su_Tu_Hong_Class.TxtId, x_Su_Tu_Hong_Class.Txt);
		}
	}

	private int method_0(int int_0, int int_1)
	{
		var result = 0;
		foreach (var value in KhiCongTangThem.Values)
		{
			if (value.FLD_JOB == int_1 && value.FLD_INDEX == int_0)
			{
				result = value.FLD_PID;
			}
		}
		return result;
	}

	public static void SendLionRoarMessageBroadcastData(int int_0, string string_0, int int_1, string string_1)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55B6002D016600A800CC****************************************************************************************************************************************************************************************************************************************************************************************************************************************************0000000055AA");
			array[10] = 35;
			var bytes = Encoding.Default.GetBytes(string_0);
			var bytes2 = Encoding.Default.GetBytes(string_1);
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 4, 2);
			array[34] = (byte)int_1;
			foreach (var value in allConnectedChars.Values)
			{
				if (value.Client != null && !value.Client.TreoMay)
				{
					value.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "World Transmit SuTuHong Tin tức QuangBaSoLieu error![" + int_0 + "]-[" + string_0 + "]-[" + string_1 + "]" + ex.Message);
		}
	}

	public static void SendFullServerLionRoarMessageBroadcastData(int int_0, string string_0, int int_1, string string_1, int int_2, int int_3, int int_4)
	{
		try
		{
			var array = Converter.HexStringToByte("AA55B600E5026600A80026****************************************00000056******************************************************************************************************************************************************************************************************************************************************************************0000000065********************0055AA");
			array[10] = (byte)int_4;
			Buffer.BlockCopy(BitConverter.GetBytes(int_2), 0, array, 169, 2);
			Buffer.BlockCopy(BitConverter.GetBytes(int_3), 0, array, 174, 4);
			var bytes = Encoding.Default.GetBytes(string_0);
			var bytes2 = Encoding.Default.GetBytes(string_1);
			Buffer.BlockCopy(bytes2, 0, array, 35, bytes2.Length);
			Buffer.BlockCopy(bytes, 0, array, 12, bytes.Length);
			Buffer.BlockCopy(BitConverter.GetBytes(int_0), 0, array, 4, 2);
			array[34] = (byte)int_1;
			foreach (var value in allConnectedChars.Values)
			{
				if (value.Client != null && !value.Client.TreoMay)
				{
					value.Client.Send_Map_Data(array, array.Length);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "World Transmit SuTuHong Tin tức QuangBaSoLieu error![" + int_0 + "]-[" + string_0 + "]-[" + string_1 + "]" + ex.Message);
		}
	}

	public static void GuiDi_TruyenAm_TinTuc(int int_0, string string_0, string string_1, string string_2, int int_1, string string_3)
	{
		try
		{
			foreach (var value in allConnectedChars.Values)
			{
				if (value.CharacterName == string_1.TrimStart().TrimEnd())
				{
					if (value.Config.TruyenAm == 0)
					{
						value.HeThongNhacNho("Đại hiệp có tin tức truyền âm, xin mở khóa truyền âm để xem!", 10, "Thiên cơ các");
					}
					else
					{
						value.GuiDi_TruyenAm_TinTuc(string_0, int_0, value, string_2, int_1);
					}
					break;
				}
			}
		}
		catch
		{
		}
	}

	public static void SendGangMessage(string string_0, byte[] byte_0, int int_0)
	{
		try
		{
			foreach (var value in allConnectedChars.Values)
			{
				if (value.GuildName == string_0 && value.Client != null)
				{
					value.Client.Send_Map_Data(byte_0, int_0);
				}
			}
		}
		catch
		{
		}
	}

	public static int AddTeam(X_To_Doi_Class TeamClass_0)
	{
		WTeamId++;
		WToDoi.Add(WTeamId, TeamClass_0);
		return WTeamId;
	}

	public static void AddWorldBoss(NpcClass boss)
	{
		if (List_WorldBossContribute.TryGetValue(boss.ID, out var _))
		{
			LogHelper.WriteLine(LogLevel.Error, $"A boss with ID {boss.ID} already exists.");
			return;
		}

		WorldBossContributeClass wb = new()
		{
			ID = boss.ID,
			Boss = boss,
			Contribute = new Dictionary<(int, int), DamageContribute>(),
			currentZone = DefaultZone,
			KilledBy = "",
			Rewarded = false
		};
		List_WorldBossContribute.Add(boss.ID, wb);
		LogHelper.WriteLine(LogLevel.Error, $"Boss with ID {boss.ID} added successfully.");
	}

	public static void RemoveWorldBoss(int bossId)
	{
		try
		{
			if (List_WorldBossContribute.TryGetValue(bossId, out var _))
			{
				List_WorldBossContribute.Remove(bossId);
				LogHelper.WriteLine(LogLevel.Info, $"Boss with ID {bossId} removed from contribute list.");
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Info, $"Boss with ID {bossId} not found in contribute list.");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Error removing boss {bossId} from contribute list: {ex.Message}");
		}
	}

	public static void Packet_Countdown(Players player, int int_109)
	{
		try
		{
			var array = Converter.HexStringToByte("AA552E000F2713222000090001000B000000010000000C0000002101****************************************02EE55AA");
			Buffer.BlockCopy(BitConverter.GetBytes(int_109), 0, array, 26, 2);
			player.Client?.Send_Map_Data(array, array.Length);
		}
		catch
		{
		}
	}



	public static void checkupdateguildLevel()
	{
		var string_ = $"SELECT  count(*)  FROM  TBL_XWWL_GuildMember  WHERE  FLD_NewGuildPoint > 0";
		int num;
		try
		{
			num = (int)DBA.GetDBValue_3(string_, "GameServer");
		}
		catch
		{
			num = 0;
		}
		if (num > 0)
		{
			UpdateGuildPoint();
		}
	}

	public static void UpdateGuildPoint()
	{
		var voHuan = 0;
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM TBL_XWWL_Guild");
		for (var i = 0; i < dBToDataTable.Rows.Count; i++)
		{
			var dBToDataTable2 = DBA.GetDBToDataTable(string.Format("SELECT * FROM TBL_XWWL_GuildMember where G_Name='{0}'", dBToDataTable.Rows[i]["G_Name"]));
			for (var j = 0; j < dBToDataTable2.Rows.Count; j++)
			{
				voHuan += (int)dBToDataTable2.Rows[j]["FLD_GuildPoint"];
			}
			DBA.GetDBToDataTable(string.Format("UPDATE TBL_XWWL_GuildMember SET FLD_NewGuildPoint= 0 WHERE G_Name='{0}'", dBToDataTable.Rows[i]["G_Name"]));
			var level = (voHuan < 160000) ? ((voHuan < 80000) ? ((voHuan < 40000) ? ((voHuan < 20000) ? ((voHuan < 10000) ? ((voHuan < 4000) ? ((voHuan < 3000) ? ((voHuan < 1500) ? ((voHuan < 1000) ? ((voHuan < 500) ? ((voHuan < 200) ? 1 : 2) : 3) : 4) : 5) : 6) : 7) : 7) : 7) : 7) : 7) : 7;
			DBA.GetDBToDataTable(string.Format("UPDATE TBL_XWWL_Guild SET BangPhaiVoHuan={0},Leve={1} WHERE G_Name='***'", voHuan, level, dBToDataTable.Rows[i]["G_Name"]));
			voHuan = 0;
		}
		foreach (var value in allConnectedChars.Values)
		{
			if (value.CheckGiaNhapBang)
			{
				value.CapnhatdiemthuongGuild();
			}
		}
	}

	private static void Event_x2_Va_Bonus()
	{
		Eventx2 = 0;
		IdItemX2 = 1000000921;
		Eventx3 = 0;
		IdItemX3 = 1000001277;
		IdItem_Party = 1000000922;
		IdItem_Bonus = 1000001278;
		Event_Bonus_Rate = 0.0;
		EventX2ExpStatus = 0;
		EventX3ExpStatus = 0;
		CreateNewJob = 0;
	}

	static World()
	{
		List_UpgradeItem = new Dictionary<int, UpgradeItemClass>();
		List_WorldBossContribute = new();
		ListItemOption = new Dictionary<long, ItemOptionClass>();
		ItemHoatDongWorldBoss = **********;
		WorldBoss_TimeWait = 5;
		Ninja_TocKich_TocDo = 150;
		QuyenSu_TroiChan_ThoiGian = 500;
		ThoiGian_GioiHan_PhysicalAttack = 800;
		Event_13Trieu_Progress = 0;
		Event_13Trieu_Total_Round = 40;
		Event_13Trieu_ThanNu_HP = 0;
		Event_13Trieu_TongDiemSo = 0;
		DanhSachNhanVat_ThamGia_Event_HeroDefend = new();
		VoHuan_GioiHan_MoiNgay = 2000;
		讨伐副本最多人数 = 30;
		NpcEvent_DCH = new();
		PhucHoi_DuLieu_VatPham = new();
		ChatAllServer_OnOff = 0;
		Event_x2_Va_Bonus();
		WebShopCategoryList = new Dictionary<int, X_WebShop_Category>();
		WebShopItemList = new Dictionary<string, X_Bach_Bao_Cac_Loai>();
		Event_ToaDo_DuongDua = new();
		TLC_ToaDo_CamPK = new();
		DCH_ToaDo_UnCheck = new();
		DCH_ToaDo_TanHinh = new();
		DanhSachNhanVat_ThamGia_DCH = new();
		EventTopDCH = new();
		KiemSoat_SoLuong_DropBoss = "6,10";
		Npc_Boss_Map = new();
		NpcEvent_GiangSinh = new();
		Npc_HaiThuoc_NamLam = new();
		Npc_DuongDua_F1 = new();
		Npc_TetDoanNgo = new();
		Npc_KhuLuyenTap = new();
		NpcEvent_Tet_GiapThin = new();
		GioiHan_LopNhanVat_KhongDuocKhoiTao = "";
		ToDoi_Event = new();
		Chuyen_BanDo_2_3 = new();
		Chuyen_BanDo_4_5 = new();
		Chuyen_BanDo_6_7 = new();
		Chuyen_BanDo_8 = new();
		Chuyen_BanDo_9 = new();
		Chuyen_BanDo_10 = new();
		NguongSo_NhipTim = 0;
		TLC_CTC_BatDau_QuangBa = 1;
		Time_Auto_Mo_Tuyet_Roi = 0;
		Offline_DanhQuai = "";
		Offline_Pill_ID = **********;
		Offline_BuffMau_DP = "";
		Offline_Nhat_Item = 0;
		Offline_TreoMay_PhamVi = 0;
		QuaiVatTuVongBienMatTriHoanThoiGian = 0;
		CoMoRa_TuyetRoiHayKhong = 0;
		tmc_flag = false;
		Bat_Event_Boss_PK = false;
		XoaTen_TheLucChien_Random = 0;
		CoHayKo_Lock_GuiTien_Warehouse = 0;
		CoHayKo_Lock_RutTien_Warehouse = 0;
		Debug = 0;
		ON_OFF_case_16666 = 0;
		ThoiGian_KichHoat_MoHop_TraiNghiem_16x = 0;
		Hut_Quai_SoLuong = 0;
		ServerChoPKHayKhong = 0;
		CoHayKo_BOSS_Rot_Item = 0;
		Phan_Tram_Chia_Drop_Gold = 0;
		Phan_Tram_Chia_Ky_Nang = 0;
		Phan_Tram_Chia_Ky_Nang_TT = 0;
		Phan_Tram_Chia_Exp = 0;
		SilverCoinSquareServerID = 28;
		SilverCoinSquareServerPort = 13065;
		GioiHan_CuongHoa_TrangSuc = 0;
		TongXacSuat_CuongHoaTrangSuc = new string[2];
		TyLe_CuongHoa_TrangSuc1 = 0;
		TyLe_CuongHoa_TrangSuc2 = 0;
		TyLe_CuongHoa_TrangSuc3 = 0;
		TyLe_CuongHoa_TrangSuc4 = 0;
		TyLe_CuongHoa_TrangSuc5 = 0;
		TyLe_CuongHoa_TrangSuc6 = 0;
		TyLe_CuongHoa_TrangSuc7 = 0;
		TyLe_CuongHoa_TrangSuc8 = 0;
		TyLe_CuongHoa_TrangSuc9 = 0;
		TyLe_CuongHoa_TrangSuc10 = 0;
		TyLe_CuongHoa_TrangSuc11 = 0;
		TyLe_CuongHoa_TrangSuc12 = 0;
		TyLe_CuongHoa_TrangSuc13 = 0;
		TyLe_CuongHoa_TrangSuc14 = 0;
		TyLe_CuongHoa_TrangSuc15 = 0;
		TyLe_CuongHoa_TrangSuc16 = 0;
		CuongHoa_ThanThu_1_10 = 0;
		CuongHoa_ThanThu_11_15 = 0;
		CuongHoa_ThanThu_16_20 = 0;
		CuongHoa_ThanThu_21_25 = 0;
		CuongHoa_ThanThu_26_30 = 0;
		CuongHoa_ThanThu_31_35 = 0;
		CuongHoa_ThanThu_36_40 = 0;
		CuongHoa_ThanThu_41_50 = 0;
		CuongHoa_ThanThu_51_60 = 0;
		CuongHoa_ThanThu_61_70 = 0;
		CuongHoa_ThanThu_71_80 = 0;
		CuongHoa_ThanThu_81_85 = 0;
		CuongHoa_ThanThu_86_90 = 0;
		CuongHoa_ThanThu_91_95 = 0;
		CuongHoa_ThanThu_96_99 = 0;
		HopThanh_Pet_Dong_1 = 0;
		HopThanh_Pet_Dong_2 = 0;
		HopThanh_Pet_Dong_3 = 0;
		HopThanh_Pet_Dong_4 = 0;
		Rate_Pet_CuongHoa_Level_1 = 0;
		Rate_Pet_CuongHoa_Level_2 = 0;
		Rate_Pet_CuongHoa_Level_3 = 0;
		Rate_Pet_CuongHoa_Level_4 = 0;
		Rate_Pet_CuongHoa_Level_5 = 0;
		Rate_Pet_CuongHoa_Level_6 = 0;
		Rate_Pet_CuongHoa_Level_7 = 0;
		Rate_Pet_CuongHoa_Level_8 = 0;
		Rate_Pet_CuongHoa_Level_9 = 0;
		Rate_Pet_CuongHoa_Level_10 = 0;
		Rate_Pet_CuongHoa_Level_11 = 0;
		Rate_Pet_CuongHoa_Level_12 = 0;
		Rate_Pet_CuongHoa_Level_13 = 0;
		Rate_Pet_CuongHoa_Level_14 = 0;
		Rate_Pet_CuongHoa_Level_15 = 0;
		Rate_Pet_CuongHoa_Level_16 = 0;
		Rate_Pet_CuongHoa_Level_17 = 0;
		Rate_Pet_CuongHoa_Level_18 = 0;
		Rate_Pet_CuongHoa_Level_19 = 0;
		Rate_Pet_CuongHoa_Level_20 = 0;
		NguoiChiemGiu_Den_Tenma = string.Empty;
		MonPhaiLienMinhTrangThai = new();
		Mo_TT_1 = 0;
		Mo_TT_2 = 0;
		Mo_TT_3 = 0;
		Mo_TT_4 = 0;
		Mo_TT_5 = 0;
		Mo_TT_6 = 0;
		Mo_TT_7 = 0;
		CoHayKo_MoRa_Log_TreoShop = 0;
		GhiLogMuaNPC = 0;
		GhiLogBanNPC = 0;
		GhiLogMoHop = 0;
		CoHayKhongMoRa_CongDichChuyen = 0;
		GhiLogCuongHoaTrangSuc_GiaTri_Array = new string[2];
		GhiLogCuongHoaTrangSuc = 0;
		GhiLogCuongHoaTrangSuc_GiaTri = "0;10";
		GhiLogCuongHoa_GiaTri_Array = new string[2];
		GhiLogCuongHoa = 0;
		GhiLogCuongHoa_GiaTri = "0;15";
		GhiLogTyLeHopThanh = 0;
		GhiLogTyLeHopThanh_HoaLongThach = 0;
		GhiLogBuaCuongHoa_GiaTri_Array = new string[2];
		GhiLogBuaCuongHoa = 0;
		GhiLogBuaCuongHoa_GiaTri = "0;15";
		GhiLogCuongHoaThucTinh_GiaTri_Array = new string[2];
		GhiLogCuongHoaThucTinh = 0;
		GhiLogCuongHoaThucTinh_GiaTri = "0;15";
		GhiLogCuongHoaAoChoang_GiaTri_Array = new string[2];
		GhiLogCuongHoaAoChoang = 0;
		GhiLogCuongHoaAoChoang_GiaTri = "0;100";
		GhiLogTienHoaThanThu = 0;
		GhiLogCuongHoaThanThu_GiaTri_Array = new string[2];
		GhiLogCuongHoaThanThu = 0;
		GhiLogCuongHoaThanThu_GiaTri = "0;100";
		PhanThuong_ThemVao_TLC = 0;
		CoHayKhongMoRaGuiThu = 0;
		Co_Hay_Khong_Thuong_Them_X2VoHuan_X300_TLC = 0;
		Gioi_han_EXP_khi_co_TLC = 0;
		MaxKhiCong_Tren1KhiCong = 50;
		BatTat_NhatItem_UuTien_NhatTuDo_KoVutItem_Va_CuoiThu = 0;
		Rate_Gold_Party = 0.0;
		Rate_Exp_Party = 0.0;
		Rate_KyNang_TT_Party = 0.0;
		Rate_KyNang_Party = 0.0;
		Drop_Cach_1_Level_Quai = 0.0;
		Drop_Cach_2_Level_Quai = 0.0;
		Drop_Cach_3_Level_Quai = 0.0;
		Drop_Cach_4_Level_Quai = 0.0;
		Drop_Cach_5_Level_Quai = 0.0;
		Drop_Cach_6_Level_Quai = 0.0;
		Drop_Cach_7_Level_Quai = 0.0;
		Drop_Cach_8_Level_Quai = 0.0;
		Drop_Cach_9_Level_Quai = 0.0;
		Drop_Cach_10_Level_Quai = 0.0;
		Drop_Cach_11_Level_Quai = 0.0;
		Drop_Cach_12_Level_Quai = 0.0;
		Drop_Cach_13_Level_Quai = 0.0;
		Drop_Cach_14_Level_Quai = 0.0;
		Drop_Cach_15_Level_Quai = 0.0;
		Drop_Cach_16_Level_Quai = 0.0;
		Drop_Cach_17_Level_Quai = 0.0;
		Drop_Cach_18_Level_Quai = 0.0;
		Drop_Cach_19_Level_Quai = 0.0;
		Drop_Cach_20_Level_Quai = 0.0;
		Giam_Gold_Drop_Level_100_114 = 0.0;
		Giam_Gold_Drop_Level_115_119 = 0.0;
		Giam_Gold_Drop_Level_120_124 = 0.0;
		Giam_Gold_Drop_Level_125_128 = 0.0;
		Giam_Gold_Drop_Level_129_130 = 0.0;
		Gold_Drop_Cach_1_Level_Quai = 0.0;
		Gold_Drop_Cach_2_Level_Quai = 0.0;
		Gold_Drop_Cach_3_Level_Quai = 0.0;
		Gold_Drop_Cach_4_Level_Quai = 0.0;
		Gold_Drop_Cach_5_Level_Quai = 0.0;
		Gold_Drop_Cach_6_Level_Quai = 0.0;
		Gold_Drop_Cach_7_Level_Quai = 0.0;
		Gold_Drop_Cach_8_Level_Quai = 0.0;
		Gold_Drop_Cach_9_Level_Quai = 0.0;
		Gold_Drop_Cach_10_Level_Quai = 0.0;
		Gold_Drop_Cach_11_Level_Quai = 0.0;
		Gold_Drop_Cach_12_Level_Quai = 0.0;
		Gold_Drop_Cach_13_Level_Quai = 0.0;
		Gold_Drop_Cach_14_Level_Quai = 0.0;
		Gold_Drop_Cach_15_Level_Quai = 0.0;
		Gold_Drop_Cach_16_Level_Quai = 0.0;
		Gold_Drop_Cach_17_Level_Quai = 0.0;
		Gold_Drop_Cach_18_Level_Quai = 0.0;
		Gold_Drop_Cach_19_Level_Quai = 0.0;
		Gold_Drop_Cach_20_Level_Quai = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_1 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_2 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_3 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_4 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_5 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_6 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_7 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_8 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_9 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_10 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_11 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_12 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_13 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_14 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_15 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_16 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_17 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_18 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_19 = 0.0;
		Tang_DropGold_Monster_LV_150_TroLen_Cach_LV_20 = 0.0;
		Bonus_Drop_BanDo_TDKH = 0.0;
		Time_Delay_Auto_Offline = 0.0;
		CoHayKo_Bat_Tat_TangHinh_Ninja = 0;
		Khoa_ChucNang_Auto = 0;
		ServerVer = 2.0;
		ServerVerD = 0.0;
		ServerRegTime = string.Empty;
		DiDong_TocDo = new string[5];
		MainServer = 1;
		Droplog = false;
		ServerIDStart = 0;
		OffLine_SoLuong = 0;
		TreoMay_Offline = 0;
		Key = "192.168.0.4";
		Key2 = "192.168.0.4";
		Keyk = new();
		AllItmelog = 1;
		CoMo_ThiTruongTraoDoiTienXu = 0;
		WhetherTheCurrentLineIsSilver = 0;
		ChoPhep_MoShop = 0;
		ChoPhep_GiaoDich = 0;
		ChoPhep_TreoMay = 0;
		Newversion = 9;
		TanCongLienTuc_SoLanTanCong = 1;
		LienTucCongKich_ThoiGianHieuQua = 1000;
		BatHopPhap_CongKich_SuDungPlugin = 0;
		AlWorldlog = true;
		百宝阁属性物品类list = new();
		百宝阁属性物品类list2 = new();
		KiemTraThietBiList = new();
		list = new();
		KhiCong_CoBan_ID = new();
		EventTop = new();
		allConnectedChars = new();
		BannedList = new();
		ThangThienKhiCongList = new();
		NhiemVulist = new();
		m_Disposed = Queue.Synchronized(new());
		SqlPool = Queue.Synchronized(new());
		MapList = new();
		HelpList = new();
		HelpNameList = new();
		Maplist = new();
		NpcList = new();
		Weddinglist = new();
		GuildList = new();
		BachBaoCat_ThuocTinhVatPhamClassList = new();
		TreoMay_SoLieu = new();
		PVP_TrangBi = new();
		PVP_TrangBi_16x_Chan = new();
		MonChien_ProgressNew = 0;
		ThoiGianTanCong_Interval = 1000;
		AtPort = 55980;
		IsTheDragonHallInUse = false;
		IsHuaMarriageHallInUse = false;
		WhetherTheSacramentalHallIsInUse = false;
		TocDoLonNhat_VuotQuaSoLan_ThaoTac = 0;
		SoLan_VuotQuaChoPhep_Trong30Giay = 0;
		Time_Delay_Item_8000008_Con_Lai = 0;
		Time_Delay_Item_8000008_Cung = 0;
		Time_Delay_Item_8000008_ThichKhach = 0;
		Time_Delay_Item_8000008_MLC = 0;
		SoLuong_Item_DropBoss = 0;
		FLD_RXPIONT_So_Luong_Can_Log_Vuot_Gioi_Han = 0;
		HuyenBotPhai = 0;
		TamTaQuan = 0;
		LieuChinhQuan = 0;
		ThanVoMon = 0;
		LieuThienPhu = 0;
		NamMinhHieu = 0;
		TungNguyetQuan = 0;
		BachVoQuan = 0;
		BacHaiBangCung = 0;
		NamLam = 0;
		HoHapCoc = 0;
		XichThienGioi = 0;
		ThienDuSon = 0;
		ThanhDiaKiemHoang = 0;
		KhuLuyenTap1 = 0;
		KhuLuyenTap9 = 0;
		PhongThanKhau = 0;
		Rate_Rot_Quest = 0;
		So_Luong_Quest = 0;
		Item_Quest = 0;
		ID_Quai = 0;
		PhanThuongVoHuan_Quest = 0;
		LuongVoHuan_CuoiTuan = 100000;
		GioiHanVoHuanMoiNgay_Cap2 = 0;
		GioiHanVoHuanMoiNgay_Cap3 = 0;
		GioiHanVoHuanMoiNgay_Cap4 = 0;
		GioiHanVoHuanMoiNgay_Cap5 = 0;
		GioiHanVoHuanMoiNgay_Cap6 = 0;
		GioiHanVoHuanMoiNgay_Cap7 = 0;
		GioiHanVoHuanMoiNgay_Cap8 = 0;
		GioiHanVoHuanMoiNgay_Cap9 = 0;
		GioiHanVoHuanMoiNgay_Cap10 = 0;
		GioiHanVoHuanMoiNgay_Cap11 = 0;
		Time_TuHa_KTTX = 0.0;
		TC_0_EXP = 0.0;
		TC_1_EXP = 0.0;
		TC_2_EXP = 0.0;
		TC_3_EXP = 0.0;
		TC_4_EXP = 0.0;
		TC_5_EXP = 0.0;
		TT_1_EXP = 0.0;
		TT_2_EXP = 0.0;
		TT_3_EXP = 0.0;
		TT_4_EXP = 0.0;
		TT_5_EXP = 0.0;
		TT_6_EXP = 0.0;
		Dame_Skill_Dao = 0.0;
		Dame_Skill_Kiem = 0.0;
		Dame_Skill_Thuong = 0.0;
		Dame_Skill_Kich_Hoat_Cung = 0.0;
		Dame_Skill_DaiPhu = 0.0;
		Dame_Skill_Ninja = 0.0;
		Dame_Skill_CamSu = 0.0;
		Dame_Skill_HBQ = 0.0;
		Dame_Skill_DHL = 0.0;
		Dame_Skill_QuyenSu = 0.0;
		Dame_Skill_MLC = 0.0;
		Dame_Skill_TH = 0.0;
		Dame_Skill_TN = 0.0;
		Skill_KichHoat_Cung_11x = 0.0;
		Skill_KichHoat_Cung_12x = 0.0;
		Skill_KichHoat_Cung_13x = 0.0;
		Skill_KichHoat_Cung_14x = 0.0;
		Skill_KichHoat_Cung_15x = 0.0;
		Skill_KichHoat_Cung_16x = 0.0;
		Skill_KichHoat_Nin_11x = 0.0;
		Skill_KichHoat_Nin_12x = 0.0;
		Skill_KichHoat_Nin_13x = 0.0;
		Skill_KichHoat_Nin_14x = 0.0;
		Skill_KichHoat_Nin_15x = 0.0;
		Skill_KichHoat_Nin_16x = 0.0;
		QuyenSu_PK_Combo_1 = 0.0;
		QuyenSu_PK_Combo_2 = 0.0;
		QuyenSu_PK_Combo_3 = 0.0;
		QuyenSu_PK_Combo_4 = 0.0;
		QuyenSu_PK_Combo_5 = 0.0;
		QuyenSu_Monster_Combo_1 = 0.0;
		QuyenSu_Monster_Combo_2 = 0.0;
		QuyenSu_Monster_Combo_3 = 0.0;
		QuyenSu_Monster_Combo_4 = 0.0;
		QuyenSu_Monster_Combo_5 = 0.0;
		LoggerVersion = 0.0;
		Drop_Jl = new();
		TheLucChien_Giam_NguoiChoi = new();
		TheLucChien_XepHang_SoLieu = new();
		VoLamHuyetChien_XepHang_SoLieu = new();
		BangPhai_XepHang_SoLieu = new();
		BangPhaiXepHangSoLieu = new();
		LucPhongNguVoCong_KiemSoat = 100.0;
		LucCongKichVoCong_BoiSo = 1.0;
		TurnOnCardSkills = 1;
		CardSkillLevel = 120;
		ToaDo_ThoiGianLamMoi = 3000;
		KiemSoatThoiGian_TanCong = 600;
		CongHienNguyenBao_SoLuong = 0;
		CongHienNguyenBao_DiemVinhDu = 0;
		Script = 0;
		X2KinhNghiem_CapDo_GioiHanCaoNhat = 59;
		X2TienTai_CapDo_GioiHanCaoNhat = 59;
		X2LichLuyen_CapDo_GioiHanCaoNhat = 59;
		X2BaoSuat_CapDo_GioiHanCaoNhat = 59;
		X2CapDo_GioiHanCaoNhat_BoiSo = 2.0;
		GioiHan_Level_CaoNhat = 200;
		GioiHan_CuongHoa_ThanThu = 0;
		Gioi_Han_Level_100_MoHop_Gift_Code = 0;
		ON_OFF_LogChangeSkill = 0;
		Event_MoHop_SoLuong = 0;
		TestChiSoConfigTrongSource = 0;
		TestChiSoConfigTrongSource1 = 0;
		TestChiSoConfigTrongSource2 = 0.0;
		TestChiSoConfigTrongSource3 = 0.0;
		TestChiSoConfigTrongSource4 = 0.0;
		Test_Packet_Bytes_1 = 0;
		Test_Packet_Bytes_2 = 0;
		Test_Packet_Bytes_3 = 0;
		Test_Packet_Bytes_4 = 0;
		Speed_Attack_Monster = 0;
		Min_Delay_Skill_Monster = 0;
		Max_Delay_Skill_Monster = 0;
		Giam_Delay_Skill_CharDao_PK = 0;
		Giam_Delay_Skill_CharKiem_PK = 0;
		Giam_Delay_Skill_CharThuong_PK = 0;
		Giam_Delay_Skill_CharCung_PK = 0;
		Giam_Delay_Skill_CharDP_PK = 0;
		Giam_Delay_Skill_CharTK_PK = 0;
		Giam_Delay_Skill_CharCS_PK = 0;
		Giam_Delay_Skill_CharHBQ_PK = 0;
		Giam_Delay_Skill_CharDHL_PK = 0;
		Giam_Delay_Skill_CharQS_PK = 0;
		Giam_Delay_Skill_CharMLC_PK = 0;
		Giam_Delay_Skill_CharTH_PK = 0;
		Giam_Delay_Skill_CharTN_PK = 0;
		Giam_Delay_Skill_CharDao_Train = 0;
		Giam_Delay_Skill_CharKiem_Train = 0;
		Giam_Delay_Skill_CharThuong_Train = 0;
		Giam_Delay_Skill_CharCung_Train = 0;
		Giam_Delay_Skill_CharDP_Train = 0;
		Giam_Delay_Skill_CharTK_Train = 0;
		Giam_Delay_Skill_CharCS_Train = 0;
		Giam_Delay_Skill_CharHBQ_Train = 0;
		Giam_Delay_Skill_CharDHL_Train = 0;
		Giam_Delay_Skill_CharQS_Train = 0;
		Giam_Delay_Skill_CharMLC_Train = 0;
		Giam_Delay_Skill_CharTH_Train = 0;
		Giam_Delay_Skill_CharTN_Train = 0;
		XacNhan_Dame_Train_Client_den_Server = 0;
		Random_TamHoaTuDinh_Chinh = 0;
		Random_TamHoaTuDinh_Ta = 0;
		CoHayKo_KichHoat_TaiKhoan_VaoDuocGame = 0;
		Kenh_Treo_Shop = 0;
		Kenh_MoHop_Event = 0;
		Thuong_ON_CPVP_NhanThemSatThuong = 0.0;
		Thuong_OFF_CPVP_NhanThemSatThuong = 0.0;
		Quyen_ON_CPVP_NhanThemSatThuong = 0.0;
		Quyen_OFF_CPVP_NhanThemSatThuong = 0.0;
		Random_Type_Box_Item = 0;
		Random_Rate_KinhNghiem = 0;
		Random_Rate_Gold = 0;
		Random_Rate_KyNang = 0;
		Random_Rate_KyNangThangThien = 0;
		Time_SK1_Char_Cung = 0;
		Time_SK1_Char_DHL = 0;
		Time_SK1_Char_ConLai = 0;
		Item_Byte_Length_92 = 92;
		Item_Db_Byte_Length = 76;
		VatPham_ThuocTinh_KichThuoc = 56;
		ThangThienKyNang_CapDoTangThem = 3;
		AutGC = 0;
		locklist = 0.0;
		locklist2 = 0.0;
		locklist3 = new();
		MoiLan_SuTuHong_TieuHaoNguyenBao = 20;
		ChoPhep_MoSoLuong_NhieuHon = 0;
		ChoPhep_MoSoLuong_NhieuHon_Mac_Address = 0;
		SoNguoiDangKyCongThanhChien = 0;
		Gioi_han_so_nguoi_vao_party = 0;
		CoMoRa_HeThong_MonChien = 0;
		HeThong_MonChien_CanNguyenBao = 0;
		HeThong_MonChien_Gio = 0;
		HeThong_MonChien_Phut = 0;
		HeThong_MonChien_Giay = 0;
		BangChienThang_ID = 0;
		SoLuong_NguyenBao_MoiLanTieuThu = 10;
		MaximumNumberOfConnectionsToTheGameLoginPort = 20;
		ResetNhoHon_KhongTheVaoTLC_OLD = 0;
		ResetLonHon_KhongTheVaoTLC_NEW = 0;
		Gioi_han_chenh_lech_2_ben_vao_TLC = 0;
		PhanThuongTop20_TLC = 0;
		PhanThuongBenThangTLC = 0;
		PhanThuongBenThuaTLC = 0;
		Hieu_Ung_Ao_Choang_Chinh_Nam = 0;
		Hieu_Ung_Ao_Choang_Chinh_Nu = 0;
		Hieu_Ung_Ao_Choang_Ta_Nam = 0;
		Hieu_Ung_Ao_Choang_Ta_Nu = 0;
		Diem_TLC_Chinh = 0.095;
		Diem_TLC_Ta = 0.095;
		MaximumConnectionTimeOfTheGameLoginPort = 1000;
		KiemTraVatPham_BatHopPhap = 0;
		KiemTraCacHoatDong_BatHopPhap = 3;
		KiemTraCacMuc_BatHopPhap = false;
		VatPhamCaoNhatCongKichGiaTri = 0;
		VatPhamCaoNhatPhongNguGiaTri = 0;
		VatPhamCaoNhatHPGiaTri = 0;
		VatPhamCaoNhatNoiCongGiaTri = 0;
		VatPhamCaoNhatTrungDichGiaTri = 0;
		VatPhamCaoNhatNeTranhGiaTri = 0;
		VatPhamCaoNhatCongKichVoCongGiaTri = 0;
		VatPhamCaoNhatKhiCongGiaTri = 0;
		VatPhamCaoNhatHopThanhGiaTri = 0;
		VatPhamCaoNhatPhuHonGiaTri = 0;
		VatPhamCaoNhatPhongNguVoCongGiaTri = 0;
		GioiHanCapDo_ToDoi = 15;
		CongTac_PhatHienNhipTim = 0;
		HoatDong_PhatHienNhipTim = 0;
		NguongThoiGian_PhatHienNhipTim = 10000;
		ThoiLuong_PhatHienNhipTim = 0;
		CheDo_AnToan_TieuThuNguyenBao = 0;
		CoMo_TrucTuyen_ChoNguoiMoi_LamQuenHayKhong = 0;
		CapDo_TrucTuyen = 0;
		NganPhieuDoiLayNguyenBao = 0;
		CoMoRaNganPhieu_DoiNguyenBaoHayKhong = 0;
		GoiQua_TrucTuyen = 0;
		Log_Hop_Code_Event = 0;
		LayDuoc_KinhNghiem_CapDo_ChenhLech = 20;
		PhaiChang_MoRaThietBi_ThemChucNang_MoKhoaHayKhong = 0;
		KhoaThietBi_TonNguyenBao = 0;
		MoKhoaThietBi_TonNguyenBao = 0;
		PhaiChang_MoRaTreoMay_BanThuong = 0;
		TreoMayBanThuong_ThoiGianChuKyLapLai = 0;
		TreoMayBanThuong_VoHuan = 50;
		TreoMayTieuTru_ThienAc = 150;
		TreoMayBanThuong_YeuCauDangCap = 0;
		BOSSKhuVucKep_CoBatHayKhong = false;
		TreoMayX2_ThoiGian = "20;22";
		PhaiChang_HoTroMoRong_CacVatPham_ChuSo = 0;
		ThoiGianTreoMay_AnToan = 0;
		TyLe_TangCuong_VatPham = 0.0;
		VuKhiPK_RoiDoBen = 0;
		DoPhongNguPK_RoiDoBen = 0;
		SoNguyenBao_ToiDa_TrongMotGiaoDich = 0;
		GioiHan_TongSoNguyenBao_1TaiKhoan = 0;
		HoatDong_PhatHienPhoi = 0;
		PhaiChang_MoRaVoHuan_HeThong = 0;
		PKCapDo_ChenhLech = 20;
		VoHuanBaoVe_CapDo = 20;
		TuVong_GiamBotVoHuan_SoLuong = "0;0;0;0;0;0";
		HeThongTaiChe_SoLan = "0;0;0;0;0;0";
		VoHuanThuVeTiLePhanTram = 0.2;
		ItemRecord = 0;
		LoginRecord = 0;
		DropRecord = 0;
		StoreRecord = 0;
		DrugRecord = 0;
		SyntheticRecord = 0;
		RecordKeepingDays = 30;
		BlockIP = true;
		BipList = new();
		AutomaticConnectionTime = 10;
		VersionVerificationTime = 10000;
		MainSocket = false;
		SocketState = "Stoped";
		AutomaticallyOpenTheConnection = true;
		SoLuongKetNoi_ToiDaChoPhep = 100;
		Disconnect = true;
		AddToFilterList = true;
		CloseTheConnection = true;
		WorldTime = 0;
		WTeamId = 1;
		ver = 3;
		tf = 0;
		jlMsg = 0;
		week = (int)DateTime.Now.DayOfWeek;
		Random_So_Drop = 0;
		Diem_Guild_Theo_Level = 0;
		Event_Tet_GiapThin_Progress = 0;
		Event_Noel_Progress = 0;
		DCH_MidTime = 0;
		TheLucChien_Progress = 0;
		TheLucChien_MidTime = 0;
		TheLucChien_ChinhPhai_DiemSo = 0;
		TheLucChien_TaPhai_DiemSo = 0;
		TheLucChien_ChinhPhai_SoNguoi = 0;
		TheLucChien_TaPhai_SoNguoi = 0;
		CoHayKhong_MoRa_Boss = 0;
		ON_OFF_Event_Boss_Map = 0;
		Boss_Map_Gio = 0;
		Boss_Map_Phut = 0;
		Boss_Map_Giay = 0;
		Boss_Map_Gio_Time_2 = 0;
		Boss_Map_Phut_Time_2 = 0;
		Boss_Map_Giay_Time_2 = 0;
		ON_OFF_Kick_Mem_Ban_888 = 0;
		Restart_Recovery = 0;
		CheckBugGold_Recovery = 0;
		EventExpALL_Recovery = 0;
		Check_LoginAcc_Recovery = 0;
		Check_LoginAcc_2_Recovery = 0;
		CoHayKo_BatTat_Event_Exp_TanThu = 0;
		CoHayKo_ON_OFF_Event_Exp_CuoiTuan = 0;
		CoHayKo_Auto_Kick_Feed_TLC = 0;
		CoHayKhong_MoRa_Backup_Database = 0;
		CoHayKo_KTTX_KoCan_VatPham_HoTro = 0;
		Backup_MoRa_Gio = 0;
		Backup_MoRa_Phut = 0;
		Backup_MoRa_Giay = 0;
		Boss_MoRa_Gio = 0;
		Boss_MoRa_Phut = 0;
		Boss_MoRa_Giay = 0;
		CoHayKo_check_su_dung_SK1 = 0;
		DelayBomMau_KhiPK = 0;
		CapDoChoPhepGiaoDich = 0;
		CoHayKhong_Trung_IP_PK_Tinh_Diem_TLC = 0;
		CoHayKo_Drop_CoMoThongBao = 0;
		TheLucChien_Random_MoRa = 0;
		TheLucChien_MoRa = 0;
		TheLucChien_MoRa_Gio = 0;
		TheLucChien_MoRa_Phut = 0;
		TheLucChien_MoRa_Giay = 0;
		TheLucChien_TongThoiGian = 15;
		ThoiGianChuanBi_ChoTheLucChien = 5;
		Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang = 5;
		Time_KichHoat_HopTraiNghiem_Gio = 0;
		Time_KichHoat_HopTraiNghiem_Phut = 0;
		Time_KichHoat_HopTraiNghiem_Giay = 0;
		Time_Huy_HopTraiNghiem_Gio = 0;
		Time_Huy_HopTraiNghiem_Phut = 0;
		Time_Huy_HopTraiNghiem_Giay = 0;
		Bat_Tat_SuKien_TetDoanNgo = 0;
		TetDoanNgo_Gio = 0;
		TetDoanNgo_Phut = 0;
		TetDoanNgo_Giay = 0;
		ON_OFF_Event_HaiThuoc_NamLam = 0;
		Gold_Nhan_Hop_Qua_HKGH = 0;
		HaiThuoc_NamLam_Gio = 0;
		HaiThuoc_NamLam_Phut = 0;
		HaiThuoc_NamLam_Giay = 0;
		DaiChienHon_MoRa_PhoBan = 0;
		DCH_MoRa_Gio = 0;
		DCH_MoRa_Phut = 0;
		DCH_MoRa_Giay = 0;
		ThoiGian_ChuanBi_DCH = 0;
		TongThoiGian_DCH = 0;
		ON_OFF_MoRa_Event_Boss_PK = 0;
		Boss_PK_MoRa_Gio = 0;
		Boss_PK_MoRa_Phut = 0;
		Boss_PK_MoRa_Giay = 0;
		CoHayKhong_MoRa_Event_Noel = 0;
		Noel_MoRa_Gio = 0;
		Noel_MoRa_Phut = 0;
		Noel_MoRa_Giay = 0;
		ID_Monster_Drop_Event_GiangSinh = 0;
		PhanThuong_Drop_Event_GiangSinh = 0;
		Event_KhuLuyenTap_MoRa = 0;
		KhuLuyenTap_MoRa_Gio = 0;
		KhuLuyenTap_MoRa_Phut = 0;
		KhuLuyenTap_MoRa_Giay = 0;
		ON_OFF_WORLD_BOSS = 0;
		WORLD_BOSS_MoRa_Gio = 0;
		WORLD_BOSS_MoRa_Phut = 0;
		WORLD_BOSS_MoRa_Giay = 0;
		CoHayKhong_MoRa_Event_TetGiapThin = 0;
		TetGiapThin_MoRa_Gio = 0;
		TetGiapThin_MoRa_Phut = 0;
		TetGiapThin_MoRa_Giay = 0;
		ID_Monster_Drop_Event_TetGiapThin = 0;
		PhanThuong_Drop_Event_TetGiapThin = 0;
		PhanThuong_Drop_Event_TetGiapThin_2 = 0;
		CoHayKo_Pet_Train_Exp = 0;
		Time_Exp_Pet_Hour = 0;
		Time_Add_Exp_Pet_Minute = 0;
		TheLucChien_ThuongLoai = 0;
		TheLucChien_ThuongSoLuong = 500;
		TheLucChien_ThuongGoiVatPham = 0;
		SoLuongChoPhep_NguoiChoiDatCuoc = 100;
		SanTapPhamVi_HieuQua = 60f;
		PhiVaoCua_ToiThieu = 100;
		SanTapTienThue_TiLePhanTram = 0.2;
		SoLanTronThoat_ChoPhep = 10;
		NguyenBaoBiTru_SauKhiTruDiem = 10;
		TienBiTru_SauKhiTruDiem = 10000;
		GiaTri_Gold_AutoBan = 300000;
		Gold_DoiTheLuc_qua_TaPhai = 20000;
		Phi_Doi_Character = 0;
		Win_TLC_Phan_Thuong_VoHuan = 0;
		Lose_TLC_Phan_Thuong_VoHuan = 0;
		Win_DCH_Phan_Thuong_VoHuan = 0;
		Lose_DCH_Phan_Thuong_VoHuan = 0;
		Gold_DoiTheLuc_qua_ChinhPhai = 20000;
		Eve90Progress = 0;
		Eve90_ThoiGian = 0;
		evePlayers = new();
		DCHPlayers = new();
		isvip = false;
		NguyenBao_HopThanh = 5;
		Money_Max = 999999999999L;
		GuiDi_TocDo = 0.0;
		PhatDong_GuiDi_TocDo = 0.0;
		TiepNhan_TocDo = 0.0;
		KinhNghiem_BoiSo = 1;
		TyLe_HapHon = 70;
		Tien_BoiSo = 0;
		LichLuyenBoiSo = 80;
		ThangThien_LichLuyen_BoiSo = 80.0;
		Rate_Drop_Server = 10;
		TyLe_HopThanh = 0.0;
		TyLe_CuongHoa = 0.0;
		TiLe_HopThanh_DaTuLinh = 0.0;
		GioiHan_SoLuong_ThuocTinh_VuKhi_TrangBi = 0.0;
		GioiHan_CuongHoa_AoChoang = 0;
		TyLe_TangCuong_AoChoang = 0.0;
		TyLe_NangCap_ThietBi = 0.0;
		TyLe_NangCap_TrangSuc = 0.0;
		BaibaoCourtAddress = "http://bbg.xwwl.net/login.aspx?server=1";
		BachBaoCacServerIP = "127.0.0.1";
		BaibaogeServerPort = 9001;
		AccountVerificationServerIP = "127.0.0.1";
		AccountVerificationServerPort = 55970;
		GameServerPort = 13001;
		GameServerPort1 = 13001;
		GameServerPort2 = 13001;
		GameServerPort3 = 13001;
		GameServerPort4 = 13001;
		GameServerPort5 = 13001;
		ForwarderGatewayServicePort = 50020;
		VIPLine = 0;
		MaximumOnline = 100;
		ServerGroupID = 1;
		ServerID = 0;
		ServerName = " - [Thieu World... Debug de tim world bi thieu] - ";
		ThongBao_KhiVaoGame = "";
		KeykF = new();
		SuTuHong_ID = 0;
		SuTuHongList = Queue.Synchronized(new());
		CongThanhChienList = new();
		DangKyDanhSach_NguoiChoiCongThanhChien = new();
		CongThanhSoLieu_list = new();
		ThienMaThanCungPhoTuong_PhaiChangTuVong = 0;
		ThienMaThanCungDaiMon_PhaiChangTuVong = 0;
		ThienMaThanCungDongMon_PhaiChangTuVong = 0;
		CongThanhChien_CoMoRaHayKhong = 0;
		CongThanhChien_MoRa_Gio = 0;
		CongThanhChien_MoRa_Phut = 0;
		CongThanhChien_MoRa_Giay = 0;
		CongThanhChien_Progress = 0;
		CongThanhChien_TongThoiGian = 0;
		CongThanhChien_ThoiGianChuanBi = 0;
		SuTuHong_Max_SoLuong = 100;
		Gioi_han_acc_vao_TLC = 0;
		Encrypt = 1;
		EncryptionKey = "CCDA2343677C3790";
		g_cur_key = new byte[8] { 204, 218, 35, 67, 103, 124, 55, 144 };
		Config_Auto_GiftCode = new string[1] { "" };
		Encrypt2 = 0;
		PacketTitle = 0;
		HanChe_CapDo_Nhom = 10;
		VipThongBaoTrucTuyen = 0;
		NoiDung_VipThongBaoTrucTuyen = "尊贵的VIP玩家{0}Online了！大家欢迎！";
		ThongBaoTrucTuyen_BinhThuong = 0;
		NoiDung_ThongBaoTrucTuyen_BinhThuong = "Giang hồ mạng [{0}] đã quay trở lại game";
		VIP_BanDo = string.Empty;
		BanDoKhoa_Chat = string.Empty;
		SqlJl = string.Empty;
		KiemTra_NguyenBao = 0;
		SoLuongTrangBi_ToiDa = 96;
		AutomaticArchive = 1;
		TyLe_CuongHoa_ToiCao = 0.0;
		TongXacSuat_CuongHoaThanThu = "20;180";
		VuongLong_GoldCoin = 0L;
		PhaiChangMoRa_CuuTuyenBanDo = 1.0;
		CuuTuyen_Gold_TiLe = 0.8;
		BanDoKhac_TiLe_Gold = 0.8;
		VuongLongDiaDo_ID = 0;
		FileMD5 = string.Empty;
		TaiTao_DaKimCuong_CongKich = string.Empty;
		TaiTao_DaKimCuong_TruyThuong = string.Empty;
		TaiTao_DaKimCuong_VoCong = string.Empty;
		TaiTao_DaKimCuong_TrungDich = string.Empty;
		TaiTao_DaKimCuong_SinhMenh = string.Empty;
		TaiTao_HanNgocThach_PhongNgu = string.Empty;
		TaiTao_HanNgocThach_NeTranh = string.Empty;
		TaiTao_HanNgocThach_SinhMenh = string.Empty;
		TaiTao_HanNgocThach_NoiCong = string.Empty;
		TaiTao_HanNgocThach_VoPhong = string.Empty;
		PhanTram_ChiSo_TrangBi_ULPT_Dao = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Kiem = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Thuong = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Cung = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_DaiPhu = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Nin = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Cam = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_HBQ = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_DHL = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_Quyen = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_MLC = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_TH = 0.0;
		PhanTram_ChiSo_TrangBi_ULPT_TN = 0.0;
		TyLePhanTram_PhongNguVoCong = 0.925;
		TyLePhanTram_CongKichVoCong = 0.01;
		ChetCoMatKinhNghiemKhong = 0;
		PhanTram_GiamXuong_TienNhanDuoc = 0.0;
		VIPTyLe_HopThanhGiaTang_TiLePhanTram = 0.0;
		ThanNuPK_KhoangCach = 50.0;
		TuHaoPK_KhoangCach = 50.0;
		DaiPhuPK_KhoangCach = 50.0;
		CamSuPK_KhoangCach = 50.0;
		MaiLieuChanPK_KhoangCach = 50.0;
		CungTienPK_KhoangCach = 50.0;
		NhungNgheNghiepKhac_CongKichKhoangCach = 50.0;
		CungTien_DanhQuaiKhoangCach = 60.0;
		DaiPhu_DanhQuaiKhoangCach = 50.0;
		NhungNgheNghiepKhac_DanhQuaiKhoangCach = 40.0;
		ThienQuan_TyLePhanTramKinhNghiep_CoSo = 0.0;
		ThienQuan_TyLePhanTram_RotVatPham_TangCao_CoSo = 0;
		ThienQuan_TyLePhanTramKinhNghiep_TangLen = 0.0;
		TangTiLe_RotVatPham_TrongDiaDo = 0;
		wg36 = 0;
		wg37 = 0;
		wg38 = 0;
		wg39 = 0;
		wg40 = 0;
		wg35 = 0;
		wg34 = 0;
		wg33 = 0;
		wg32 = 0;
		wg31 = 0;
		wg30 = 0;
		wg29 = 0;
		wg28 = 0;
		wg27 = 0;
		wg26 = 0;
		wg25 = 0;
		wf100 = 0;
		wf95 = 0;
		wf90 = 0;
		wf85 = 0;
		wf80 = 0;
		wf78 = 0;
		wf76 = 0;
		wf74 = 0;
		wf72 = 0;
		wf70 = 0;
		wf68 = 0;
		g25 = 0;
		g24 = 0;
		g23 = 0;
		g22 = 0;
		g21 = 0;
		g20 = 0;
		f15 = 0;
		f14 = 0;
		f13 = 0;
		f12 = 0;
		f11 = 0;
		f10 = 0;
		TieuThu_ThietBi = 0;
		SoLuong_CapNhat_ThietBi = 0;
		VatPhamThuong_MonChienID = 0;
		ChoTuLuyenMoRa_ID = 0;
		CoMoKhoa_PhatHienCongKich_HayKhong = 0;
		KhoaNguoi_SoLanTanCong_CaoNhat = 10;
		KhoaNguoi_CongKich_KiemTraThaoTac = 0;
		XemBoiToan_PhiTieuHao = 1;
		ipaddress = "127.0.0.1";
		CheTacVatPhamDanhSach = new();
		CheDuocVatPhamDanhSach = new();
		DangNhap_TruyenThuNoiDung = "欢迎进入[{0}]创新意19.0仿官，本服长久稳定，公Hoa公正，您值得信赖!";
		Nguoi_GuiThu = "创新意江湖";
		DanhThuong_KhoangCach_ThoiGian = 1000;
		Max_CuongHoa_ThucTinh = 0;
		ThucTinh_VuKhi_TrangBi_Len_1 = 100.0;
		ThucTinh_VuKhi_TrangBi_Len_2 = 90.0;
		ThucTinh_VuKhi_TrangBi_Len_3 = 80.0;
		ThucTinh_VuKhi_TrangBi_Len_4 = 70.0;
		ThucTinh_VuKhi_TrangBi_Len_5 = 60.0;
		ThucTinh_VuKhi_TrangBi_Len_6 = 50.0;
		ThucTinh_VuKhi_TrangBi_Len_7 = 40.0;
		ThucTinh_VuKhi_TrangBi_Len_8 = 30.0;
		ThucTinh_VuKhi_TrangBi_Len_9 = 20.0;
		ThucTinh_VuKhi_TrangBi_Len_10 = 10.0;
		ThucTinh_VuKhi_TrangBi_Len_11 = 5.0;
		ThucTinh_VuKhi_TrangBi_Len_12 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_13 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_14 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_15 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_16 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_17 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_18 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_19 = 0.0;
		ThucTinh_VuKhi_TrangBi_Len_20 = 0.0;
		TyLe_HopThanh_1 = 0.0;
		TyLe_HopThanh_2 = 0.0;
		TyLe_HopThanh_3 = 0.0;
		TyLe_HopThanh_4 = 0.0;
		TyLe_HopThanh_HoaLongThach_dong_1 = 0.0;
		TienHoa_ThanThu_1 = 0.0;
		TienHoa_ThanThu_2 = 0.0;
		TienHoa_ThanThu_3 = 0.0;
		TienHoa_ThanThu_4 = 0.0;
		TienHoa_ThanThu_5 = 0.0;
		TienHoa_ThanThu_6 = 0.0;
		TienHoa_ThanThu_7 = 0.0;
		TienHoa_ThanThu_8 = 0.0;
		CuongHoa_DoThan_1 = 100.0;
		CuongHoa_DoThan_2 = 90.0;
		CuongHoa_DoThan_3 = 80.0;
		CuongHoa_DoThan_4 = 70.0;
		CuongHoa_DoThan_5 = 60.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_1 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_2 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_3 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_4 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_5 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_6 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_7 = 0.0;
		CuongHoa_DoThan_5_VuKhi_NangCap_8 = 0.0;
		CuongHoa_DoThan_6 = 50.0;
		CuongHoa_DoThan_7 = 40.0;
		CuongHoa_DoThan_8 = 30.0;
		CuongHoa_DoThan_9 = 20.0;
		CuongHoa_DoThan_10 = 20.0;
		CuongHoa_DoThan_11 = 10.0;
		CuongHoa_DoThan_12 = 0.0;
		CuongHoa_DoThan_13 = 0.0;
		CuongHoa_DoThan_14 = 0.0;
		CuongHoa_DoThan_15 = 0.0;
		CuongHoa_DoThan_16 = 0.0;
		CuongHoa_DoThan_17 = 0.0;
		CuongHoa_DoThan_18 = 0.0;
		CuongHoa_DoThan_19 = 0.0;
		CuongHoa_DoThan_20 = 0.0;
		CuongHoa_DoThan_21 = 0.0;
		HatNgoc_NangCap_1 = 0.0;
		HatNgoc_NangCap_2 = 0.0;
		HatNgoc_NangCap_3 = 0.0;
		HatNgoc_NangCap_4 = 0.0;
		HatNgoc_NangCap_5 = 0.0;
		HatNgoc_NangCap_6 = 0.0;
		HatNgoc_NangCap_7 = 0.0;
		HatNgoc_NangCap_8 = 0.0;
		HatNgoc_NangCap_9 = 0.0;
		HatNgoc_NangCap_10 = 0.0;
		HatNgoc_NangCap_11 = 0.0;
		HatNgoc_NangCap_12 = 0.0;
		HatNgoc_NangCap_13 = 0.0;
		HatNgoc_NangCap_14 = 0.0;
		HatNgoc_NangCap_15 = 0.0;
		Item_MuiTen_NangCap_1 = 0.0;
		Item_MuiTen_NangCap_2 = 0.0;
		Item_MuiTen_NangCap_3 = 0.0;
		Item_MuiTen_NangCap_4 = 0.0;
		Item_MuiTen_NangCap_5 = 0.0;
		Item_MuiTen_NangCap_6 = 0.0;
		Item_MuiTen_NangCap_7 = 0.0;
		Item_MuiTen_NangCap_8 = 0.0;
		Item_MuiTen_NangCap_9 = 0.0;
		Item_MuiTen_NangCap_10 = 0.0;
		Item_MuiTen_NangCap_11 = 0.0;
		Item_MuiTen_NangCap_12 = 0.0;
		Item_MuiTen_NangCap_13 = 0.0;
		Item_MuiTen_NangCap_14 = 0.0;
		Item_MuiTen_NangCap_15 = 0.0;
		Rate_Item_BoXanh_Len_BoHong = 0.0;
		Rate_Item_BoHong_Len_QuaBanh = 0.0;
		Rate_Item_QuaBanh_Len_Chuot = 0.0;
		CuongHoa1TyLe_HopThanh = 100.0;
		CuongHoa2TyLe_HopThanh = 90.0;
		CuongHoa3TyLe_HopThanh = 80.0;
		CuongHoa4TyLe_HopThanh = 70.0;
		CuongHoa5TyLe_HopThanh = 60.0;
		CuongHoa6TyLe_HopThanh = 50.0;
		CuongHoa7TyLe_HopThanh = 40.0;
		CuongHoa8TyLe_HopThanh = 30.0;
		CuongHoa9TyLe_HopThanh = 20.0;
		CuongHoa10TyLe_HopThanh = 10.0;
		CuongHoa11TyLe_HopThanh = 5.0;
		CuongHoa12TyLe_HopThanh = 5.0;
		CuongHoa13TyLe_HopThanh = 5.0;
		CuongHoa14TyLe_HopThanh = 5.0;
		CuongHoa15TyLe_HopThanh = 5.0;
		CuongHoa16TyLe_HopThanh = 5.0;
		CuongHoa17TyLe_HopThanh = 5.0;
		CuongHoa18TyLe_HopThanh = 5.0;
		CuongHoa19TyLe_HopThanh = 5.0;
		CuongHoa_GioiHan_Item_NPC_DKT = 0;
		BatDau_VK_TB_KhongMat_CuongHoa_Tai_NPC_DKT = 0;
		BuaCuongHoa_TiLe1 = 0.0;
		BuaCuongHoa_TiLe2 = 0.0;
		BuaCuongHoa_TiLe3 = 0.0;
		BuaCuongHoa_TiLe4 = 0.0;
		BuaCuongHoa_TiLe5 = 0.0;
		BuaCuongHoa_TiLe6 = 0.0;
		BuaCuongHoa_TiLe7 = 0.0;
		BuaCuongHoa_TiLe8 = 0.0;
		BuaCuongHoa_TiLe9 = 0.0;
		BuaCuongHoa_TiLe10 = 0.0;
		BuaCuongHoa_TiLe11 = 0.0;
		BuaCuongHoa_TiLe12 = 0.0;
		BuaCuongHoa_TiLe13 = 0.0;
		BuaCuongHoa_TiLe14 = 0.0;
		BuaCuongHoa_TiLe15 = 0.0;
		Dame_Min_ThichKhach = 0.0;
		Dame_Min_CamSu = 0.0;
		Buff_DameTrain_CamSu_VeLai_ChiSo_Goc = 0.0;
		Dame_Min_MLC = 0.0;
		Dame_Min_TH = 0.0;
		Dame_Combo_DHL = 0.0;
		Giam_Dame_NinJa = 0.0;
		VuKhi7GiaiDoan_TangThemCongKich = 0;
		VuKhi8GiaiDoan_TangThemCongKich = 0;
		VuKhi9GiaiDoan_TangThemCongKich = 0;
		VuKhi10GiaiDoan_TangThemCongKich = 0;
		VuKhi11GiaiDoan_TangThemCongKich = 0;
		VuKhi12GiaiDoan_TangThemCongKich = 0;
		VuKhi13GiaiDoan_TangThemCongKich = 0;
		VuKhi14GiaiDoan_TangThemCongKich = 0;
		VuKhi15GiaiDoan_TangThemCongKich = 0;
		VuKhi16GiaiDoan_TangThemCongKich = 0;
		VuKhi17GiaiDoan_TangThemCongKich = 0;
		VuKhi18GiaiDoan_TangThemCongKich = 0;
		VuKhi19GiaiDoan_TangThemCongKich = 0;
		YPhuc7GiaiDoan_TangThemPhongNgu = 0;
		YPhuc8GiaiDoan_TangThemPhongNgu = 0;
		YPhuc9GiaiDoan_TangThemPhongNgu = 0;
		YPhuc10GiaiDoan_TangThemPhongNgu = 0;
		YPhuc11GiaiDoan_TangThemPhongNgu = 0;
		YPhuc12GiaiDoan_TangThemPhongNgu = 0;
		YPhuc13GiaiDoan_TangThemPhongNgu = 0;
		YPhuc14GiaiDoan_TangThemPhongNgu = 0;
		YPhuc15GiaiDoan_TangThemPhongNgu = 0;
		YPhuc16GiaiDoan_TangThemPhongNgu = 0;
		YPhuc17GiaiDoan_TangThemPhongNgu = 0;
		YPhuc18GiaiDoan_TangThemPhongNgu = 0;
		YPhuc19GiaiDoan_TangThemPhongNgu = 0;
		HoThu7GiaiDoan_TangThemPhongNgu = 0;
		HoThu8GiaiDoan_TangThemPhongNgu = 0;
		HoThu9GiaiDoan_TangThemPhongNgu = 0;
		HoThu10GiaiDoan_TangThemPhongNgu = 0;
		HoThu11GiaiDoan_TangThemPhongNgu = 0;
		HoThu12GiaiDoan_TangThemPhongNgu = 0;
		HoThu13GiaiDoan_TangThemPhongNgu = 0;
		HoThu14GiaiDoan_TangThemPhongNgu = 0;
		HoThu15GiaiDoan_TangThemPhongNgu = 0;
		HoThu16GiaiDoan_TangThemPhongNgu = 0;
		HoThu17GiaiDoan_TangThemPhongNgu = 0;
		HoThu18GiaiDoan_TangThemPhongNgu = 0;
		HoThu19GiaiDoan_TangThemPhongNgu = 0;
		Giay7GiaiDoan_TangThemPhongNgu = 0;
		Giay8GiaiDoan_TangThemPhongNgu = 0;
		Giay9GiaiDoan_TangThemPhongNgu = 0;
		Giay10GiaiDoan_TangThemPhongNgu = 0;
		Giay11GiaiDoan_TangThemPhongNgu = 0;
		Giay12GiaiDoan_TangThemPhongNgu = 0;
		Giay13GiaiDoan_TangThemPhongNgu = 0;
		Giay14GiaiDoan_TangThemPhongNgu = 0;
		Giay15GiaiDoan_TangThemPhongNgu = 0;
		Giay16GiaiDoan_TangThemPhongNgu = 0;
		Giay17GiaiDoan_TangThemPhongNgu = 0;
		Giay18GiaiDoan_TangThemPhongNgu = 0;
		Giay19GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap7GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap8GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap9GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap10GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap11GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap12GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap13GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap14GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap15GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap16GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap17GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap18GiaiDoan_TangThemPhongNgu = 0;
		NoiGiap19GiaiDoan_TangThemPhongNgu = 0;
		try
		{
			GiftCode = new();
			GiftCodeRewards = new();
			Kill = new();
			HelpNameList = new();
			allPVPChars = new();
			WToDoi = new();
			ThongBao = new();
			DangCapBanThuong = new();
			KhiCongTangThem = new();
			VatPhamTraoDoi = new();
			lever = new();
			Wxlever = new();
			ItemList = new();
			BachBaoCac_SoLieu = new();
			ItmeTeM = new();
			MagicList = new();
			MonsterTemplateList = new();
			VatPhamKiemTra = new();
			BossDrop = new();
			DCH_Drop = new();
			Drop = new();
			Drop_GS = new();
			Open = new();
			Set_SoLieu = new();
			Shop = new();
			Mover = new();
			DiDong = new();
			KhuTapLuyenPK = new();
			BOSSListTime = new();
			TheLucChien_KhuVuc = new();
			BangChien_KhuVuc = new();
			MoRuong_Lock = new();
			Keyk.CpuID = "";
			Keyk.DriveID = "";
			Keyk.IP = "127.0.0.1";
			Keyk.Mac = "";
			isvip = false;
			MatDatVatPham_Lock = new();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "System error: World " + ex);
			Environment.Exit(0);
		}
	}



	public static void SetGiftCode()
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM GiftCode", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Table Gift Code ---- NULL");
		}
		else
		{
			GiftCode.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				GiftCode.Add(new()
				{
					GiftCode = dBToDataTable.Rows[i]["GiftCode"].ToString(),
					Type = int.Parse(dBToDataTable.Rows[i]["Type"].ToString())
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Load Gift Code: " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public static void SetGiftCodeRewards()
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM GiftCode_Rewards", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Table Gift Code Rewards ---- NULL");
		}
		else
		{
			GiftCodeRewards.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				GiftCodeRewards.Add(new()
				{
					Type = int.Parse(dBToDataTable.Rows[i]["Type"].ToString()),
					Rewards = dBToDataTable.Rows[i]["Rewards"].ToString()
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Load Gift Code Rewards: " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public static void PortReplacementNotice()
	{
		foreach (WGSockClient client in WGServer.clients)
		{
			client.Sendd("UPDATE_SERVER_PORT|" + GameServerPort1);
		}
	}

	public static void PortReplacementNotice(string string_0)
	{
		foreach (WGHandler client in WGServer.clients)
		{
			if (client.ServerId == string_0)
			{
				client.Sendd("UPDATE_SERVER_PORT|" + GameServerPort1);
			}
		}
	}
	public static int SendItemMail(string sender, string receiver, byte[] descriptionByte, double price, X_Vat_Pham_Loai item, int day = 1)
	{
		var query = """
                        INSERT INTO MailCod (SENDER, RECEIVER, ITEM_NAME, ITEMBYTE, PRICE, STATUS,PAID, DESCRIPTION,CREATED_AT,EXPIRED_AT) VALUES (@sender, @receiver,@itemName, @itemByte, @price, @status, @paid, @description, @createdAt,@expiredAt)
                        """;
		int status = 3;
		if (item == null)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi sendItemEmail, item null");
		}
		ItemList.TryGetValue(BitConverter.ToInt32(item.VatPham_ID, 0), out var itemInfo);
		var sqlParameters = new List<SqlParameter>
			{
				new("@sender", sender),
				new("@description",descriptionByte),
				new("@itemName", itemInfo != null ? itemInfo.ItmeNAME : ""),
				new("@receiver", receiver),
				new("@itemByte", item.VatPham_byte),
				new("@price",price),
				new("@paid",false),
				new("@status",status),
				new("@createdAt", DateTime.Now),
				new("@expiredAt", DateTime.Now.AddDays(day)),
			};

		var rowsAffected = DBA.ExeSqlCommand(query, sqlParameters.ToArray(), "BBG").GetAwaiter().GetResult();
		return rowsAffected;
	}
	public static void ThongBaoGuiDi(string txt, int type)
	{
		try
		{
			foreach (var value in allConnectedChars.Values)
			{
				if (!value.Client.TreoMay && value != null)
				{
					if (type == 0)
					{
						value.SystemNotification(txt);
					}
					else
					{
						value.SystemRollingAnnouncement(txt);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "SetLogs  错误" + ex.Message);
		}
	}
	public static void WorldBoss_TraoThuongPoint(int ID)
	{
		try
		{
			var description = "Baòn ðaÞ nhâòn ðýõòc phâÌn thýõÒng ðaình boss thêì giõìi. Vui loÌng nhâòn thýõÒng trýõìc 30 ngaÌy hoãòc vâòt phâÒm seÞ biò xoìa!";
			var byteDesc = Encoding.GetEncoding(Language_Charset).GetBytes(description);

			if (List_WorldBossContribute.TryGetValue(ID, out var boss))
			{
				foreach (var item in boss.Contribute.Values)
				{
					if (item.Damage > 0)
					{
						var reward = CreateAnItem(ItemHoatDongWorldBoss, 1);
						if (reward == null)
						{
							LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tạo vật phẩm hoạt động Boss Thế Giới");
							return;
						}
						SendItemMail("[GM]", item.PlayerName, byteDesc, 0, reward, 30);
						SendMailCodNotificationByAdmin(item.WorldID, item.SessionID);
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "WorldBoss_TraoThuongPoint Error " + ex.Message);
		}

	}

	public static X_Vat_Pham_Loai CreateAnItem(int pid, int SoLuong)
	{
		X_Vat_Pham_Loai newItem = new();
		new ItmeClass();
		var VatPham_ID = BitConverter.GetBytes(pid);
		ItmeClass item = ItmeClass.GetItmeID(BitConverter.ToInt32(VatPham_ID, 0));
		if (item == null)
		{
			LogHelper.WriteLine(LogLevel.Error, "Item không tồn tại");
			return null;
		}

		byte[] bytes = BitConverter.GetBytes(RxjhClass.CreateItemSeries());
		byte[] VatPham_ThuocTinh = new byte[56];

		//VatPham_ID = RandomStoneOption(VatPham_ID, item);
		Buffer.BlockCopy(BitConverter.GetBytes(item.FLD_MAGIC0), 0, VatPham_ThuocTinh, 0, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(item.FLD_MAGIC1), 0, VatPham_ThuocTinh, 4, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(item.FLD_MAGIC2), 0, VatPham_ThuocTinh, 8, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(item.FLD_MAGIC3), 0, VatPham_ThuocTinh, 12, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(item.FLD_MAGIC4), 0, VatPham_ThuocTinh, 16, 4);

		byte[] array2 = new byte[Item_Db_Byte_Length];
		Buffer.BlockCopy(bytes, 0, array2, 0, 8);
		Buffer.BlockCopy(VatPham_ID, 0, array2, 8, 4);
		Buffer.BlockCopy(BitConverter.GetBytes(SoLuong), 0, array2, 12, 4);
		Buffer.BlockCopy(VatPham_ThuocTinh, 0, array2, 16, VatPham_ThuocTinh.Length);

		newItem.VatPham_byte = array2;
		return newItem;
	}

	public World()
	{
		// Khởi tạo các thành phần từ Core.World
		_actorSystemManager = ActorSystemManager.Instance;
		_loginServerClient = LoginServerClient.Instance;

		// Khởi tạo các thành phần từ RxjhServer.World
		ZoneManager.Instance.Initialize();
		//ScriptClass = new();
		SetupWorldBoss();
		Timer.DelayCall(TimeSpan.FromMilliseconds(2000.0), TimeSpan.FromMilliseconds(2000.0), Event_PhoBan_ALL);
	}


	private void BUFF_PILL_TLC()
	{
		if (eve == null || TheLucChien_Progress != 3 || !tmc_flag)
		{
			return;
		}
		foreach (var value in allConnectedChars.Values)
		{
			if (value.NhanVatToaDo_BanDo == 801)
			{
				if (value.TheLucChien_PhePhai == "CHINH_PHAI" && TheLucChien_TaPhai_DiemSo - TheLucChien_ChinhPhai_DiemSo >= 10)
				{
					value.Buff_TLC();
				}
				else if (value.TheLucChien_PhePhai == "TA_PHAI" && TheLucChien_ChinhPhai_DiemSo - TheLucChien_TaPhai_DiemSo >= 10)
				{
					value.Buff_TLC();
				}
				else if (value.AppendStatusList.ContainsKey(1008002191))
				{
					value.AppendStatusList[1008002191].ThoiGianKetThucSuKien();
					value.HeThongNhacNho("Hỗ trợ từ thế lực đã kết thúc!!", 20, "Thiên cơ các");
				}
			}
			else if (value.AppendStatusList.ContainsKey(1008002191))
			{
				value.AppendStatusList[1008002191].ThoiGianKetThucSuKien();
				value.HeThongNhacNho("Hỗ trợ từ thế lực đã kết thúc!!", 20, "Thiên cơ các");
			}
		}
	}

	private void Event_PhoBan_ALL()
	{
		if (CoHayKo_Auto_Kick_Feed_TLC == 1 && eve != null)
		{
			Bat_Auto_Kick_TheLucChien_World();
		}
		if (CoHayKhong_MoRa_Backup_Database == 1 && DateTime.Now.Hour == Backup_MoRa_Gio && DateTime.Now.Minute == Backup_MoRa_Phut && (DateTime.Now.Second == Backup_MoRa_Giay || DateTime.Now.Second == Backup_MoRa_Giay + 1 || DateTime.Now.Second == Backup_MoRa_Giay + 2))
		{
			Backup_Database();
		}
		//if (TheLucChien_Random_MoRa == 1 && DateTime.Now.Hour == TheLucChien_MoRa_Gio && DateTime.Now.Minute == TheLucChien_MoRa_Phut && DateTime.Now.DayOfWeek.ToString() == "Saturday" && (DateTime.Now.Second == TheLucChien_MoRa_Giay || DateTime.Now.Second == TheLucChien_MoRa_Giay + 1 || DateTime.Now.Second == TheLucChien_MoRa_Giay + 2))
		//{
		//	Bat_TheLucChien_Random_World();
		//}
		if (TheLucChien_MoRa == 1 && DateTime.Now.Hour == TheLucChien_MoRa_Gio && DateTime.Now.Minute == TheLucChien_MoRa_Phut && (DateTime.Now.DayOfWeek.ToString() == "Monday" || DateTime.Now.DayOfWeek.ToString() == "Tuesday" || DateTime.Now.DayOfWeek.ToString() == "Wednesday" || DateTime.Now.DayOfWeek.ToString() == "Thursday" || DateTime.Now.DayOfWeek.ToString() == "Friday" || DateTime.Now.DayOfWeek.ToString() == "Sunday") && (DateTime.Now.Second == TheLucChien_MoRa_Giay || DateTime.Now.Second == TheLucChien_MoRa_Giay + 1 || DateTime.Now.Second == TheLucChien_MoRa_Giay + 2))
		{
			Bat_TheLucChien_ChinhTa_World();
		}
		//else if (Event_KhuLuyenTap_MoRa == 1 && DateTime.Now.Hour == KhuLuyenTap_MoRa_Gio && DateTime.Now.Minute == KhuLuyenTap_MoRa_Phut && (DateTime.Now.DayOfWeek.ToString() == "Tuesday" || DateTime.Now.DayOfWeek.ToString() == "Thursday" || DateTime.Now.DayOfWeek.ToString() == "Saturday") && (DateTime.Now.Second == KhuLuyenTap_MoRa_Giay || DateTime.Now.Second == KhuLuyenTap_MoRa_Giay + 1 || DateTime.Now.Second == KhuLuyenTap_MoRa_Giay + 2))
		//{
		//	Bat_Event_KLT();
		//}
		//else if (CoHayKhong_MoRa_Event_Noel == 1 && DateTime.Now.Hour == Noel_MoRa_Gio && DateTime.Now.Minute == Noel_MoRa_Phut && (DateTime.Now.DayOfWeek.ToString() == "Monday" || DateTime.Now.DayOfWeek.ToString() == "Wednesday" || DateTime.Now.DayOfWeek.ToString() == "Friday") && (DateTime.Now.Second == Noel_MoRa_Giay || DateTime.Now.Second == Noel_MoRa_Giay + 1 || DateTime.Now.Second == Noel_MoRa_Giay + 2))
		//{
		//	foreach (var value in allConnectedChars.Values)
		//	{
		//		CoMoRa_TuyetRoiHayKhong = 1;
		//		value.Packet_TuyetRoi();
		//	}
		//	LogHelper.WriteLine(LogLevel.Info, "AUTO BẬT EVENT GIÁNG SINH");
		//	EventNoel = new();
		//}
		//else if (DaiChienHon_MoRa_PhoBan == 1 && DateTime.Now.Hour == DCH_MoRa_Gio && DateTime.Now.Minute == DCH_MoRa_Phut && (DateTime.Now.DayOfWeek.ToString() == "Monday" || DateTime.Now.DayOfWeek.ToString() == "Wednesday" || DateTime.Now.DayOfWeek.ToString() == "Friday") && (DateTime.Now.Second == DCH_MoRa_Giay || DateTime.Now.Second == DCH_MoRa_Giay + 1 || DateTime.Now.Second == DCH_MoRa_Giay + 2))
		//{
		//	Bat_DaiChienHon_World();
		//}
		//else if (ON_OFF_WORLD_BOSS == 1 && DateTime.Now.Hour == WORLD_BOSS_MoRa_Gio && DateTime.Now.Minute == WORLD_BOSS_MoRa_Phut && DateTime.Now.DayOfWeek.ToString() == "Sunday" && (DateTime.Now.Second == WORLD_BOSS_MoRa_Giay || DateTime.Now.Second == WORLD_BOSS_MoRa_Giay + 1 || DateTime.Now.Second == WORLD_BOSS_MoRa_Giay + 2))
		//{
		//	Event_Boss();
		//}
		//else if (ON_OFF_Event_Boss_Map == 1 && DateTime.Now.Hour == Boss_Map_Gio && DateTime.Now.Minute == Boss_Map_Phut && (DateTime.Now.DayOfWeek.ToString() == "Monday" || DateTime.Now.DayOfWeek.ToString() == "Tuesday" || DateTime.Now.DayOfWeek.ToString() == "Wednesday" || DateTime.Now.DayOfWeek.ToString() == "Thursday" || DateTime.Now.DayOfWeek.ToString() == "Friday") && (DateTime.Now.Second == Boss_Map_Giay || DateTime.Now.Second == Boss_Map_Giay + 1 || DateTime.Now.Second == Boss_Map_Giay + 2))
		//{
		//	Event_Boss_Map_World();
		//}
		//else if (Open_Auto_GiftCode != 0)
		//{
		//	Gift_Code_Reward();
		//}
	}

	public static void AutoOffline()
	{
		Logger.Instance.Info("Kích hoạt chế độ Auto Offline");
		// Logic xử lý Auto Offline
		Task.Run(() =>
        {
            LoadCharacterOffAttack();
        });

		Logger.Instance.Info("Đã bật chế độ Auto Offline");
	}

	public static void AutoParty()
	{
		Logger.Instance.Info("Kích hoạt chế độ Auto Party");
		// Logic xử lý Auto Party

		Logger.Instance.Info("Đã bật chế độ Auto Party");
	}

	private static void LoadCharacterOffAttack()
	{
		try
		{
			var dbTable = DBA.GetDBToDataTable(@"
			    SELECT DISTINCT TOP 500
			        a.FLD_ID,
			        a.FLD_LEVEL,
			        a.FLD_INDEX,
			        b.*
			    FROM
			        TBL_XWWL_Char AS a
			    INNER JOIN
			        heroAccount.dbo.TBL_ACCOUNT AS b
			    ON
			        a.FLD_ID = b.FLD_ID
			    WHERE
			        b.FLD_ONLINE <> 1
			    ORDER BY
			        a.FLD_LEVEL DESC");
			foreach (DataRow row in dbTable.Rows)
			{
				OffAttackACharacter(row);
			}
		}
		catch (Exception ex)
		{
			Logger.Instance.Error($"Lỗi khi tải thông tin Offline: {ex.Message}");
		}
	}

	private static void OffAttackACharacter(DataRow item)
	{
		var acc_ = item["FLD_ID"].ToString();
		try
		{


			if (allConnectedChars.Values.Any(play => play.AccountID == acc_))
			{
				return;
			}

			// Get the index and generate other required values
			var index_ = (int)item["FLD_INDEX"];
			var intptr_0 = (IntPtr)(allConnectedChars.Count + 1000);
			var port = (ushort)RNG.Next(10000, 50000);

			// Create client information and NetState object

			var player = new Players
			{
				AccountID = acc_,
				LanIp = acc_
			};

			// Tạo SessionID duy nhất cho offline player
			var offlineSessionId = GetNextOfflineSessionId();

			// Tạo OfflineActorNetState cho offline player
			var fakeEndPoint = new IPEndPoint(System.Net.IPAddress.Parse("127.0.0.1"), port);
			var offlineActorNetState = new OfflineActorNetState(offlineSessionId, fakeEndPoint);

			// Thiết lập Client cho player
			player.Client = offlineActorNetState;
			offlineActorNetState.Player = player;
			player.KetNoi_DangNhap2(acc_, "", "127.0.0.1", "0", "", "", "", "");
			// Initialize the player and handle login
			//player.LoginPlayerX(acc_, 250, "127.0.0.1", "**********");
			player.GetAListOfPeople(new byte[0], 0);
			player.HandleCharacterLogin(index_);

			player.Auto_Train_Track = DateTime.Now.AddMilliseconds(-12000.0);
			player.Auto_TreoMay_TheoDoi_ThoiGian = 0;
			player.TuDongTiepTe = 1;
			player.ChucNang_Auto_ThucHien = 1;
			player.Offline_TreoMay_Mode_ON_OFF = 1;
			player.Offline_TreoMay_ToaDo_X = (int)player.NhanVatToaDo_X;
			player.Offline_TreoMay_ToaDo_Y = (int)player.NhanVatToaDo_Y;
			player.Offline_TreoMay_BanDo = player.NhanVatToaDo_BanDo;
			player.Auto_Offline_Timer.Elapsed += player.Auto_Offline_Function;
			player.Auto_Offline_Timer.AutoReset = true;
			player.Auto_Offline_Timer.Enabled = true;
			player.Client.Offline();
			// Find the highest-level skill
			int skillId = 0;
			try
			{
				var skill = player.FindHighestLevelVoCong();
				if (skill != null)
					skillId = skill.FLD_PID;
			}
			catch (Exception)
			{
				skillId = 0;
			}

			player.OfflineTreoMaySkill_ID = skillId;

			// Handle specific job logic
			if (player.Player_Job == 4)
			{
				skillId = 400001;
				player.CurrentlyActiveSkill_ID = 400001;
				player.OfflineTreoMaySkill_ID = 0;
			}
			else if (player.Player_Job == 6)
			{
				// Additional job logic if needed
			}

			// Save character data and finalize offline state
			player.StoredProcedureForSavingCharacterData();
			player.Client.TreoMay = true;
			World.OffLine_SoLuong++;
		}
		catch (Exception ex)
		{
			//Log Stacktrace
			Logger.Instance.Error($"Lỗi khi offline character {acc_}: {ex.StackTrace}");
			Logger.Instance.Error($"Lỗi khi offline character {acc_}: {ex.Message}");
		}
	}


	public static void Bat_Auto_Kick_TheLucChien_World()
	{
		try
		{
			if (eve == null || TheLucChien_Progress != 3)
			{
				return;
			}
			foreach (var value3 in allConnectedChars.Values)
			{
				if (value3.NhanVatToaDo_BanDo != 801)
				{
					continue;
				}
				var theLucChien_ChinhPhai_DiemSo = TheLucChien_ChinhPhai_DiemSo;
				var theLucChien_TaPhai_DiemSo = TheLucChien_TaPhai_DiemSo;
				if (EventTop == null || !EventTop.TryGetValue(value3.CharacterName, out var value) || value.TuVongSoLuong < 10 || value.GietNguoiSoLuong != 0)
				{
					continue;
				}
				if (EventTop.TryGetValue(value3.CharacterName, out value))
				{
					if (value.TuVongSoLuong > 0)
					{
						if (value3.TheLucChien_PhePhai == "TA_PHAI")
						{
							TheLucChien_ChinhPhai_SoNguoi--;
							TheLucChien_ChinhPhai_DiemSo -= value.TuVongSoLuong / 2;
						}
						else if (value3.TheLucChien_PhePhai == "CHINH_PHAI")
						{
							TheLucChien_TaPhai_SoNguoi--;
							TheLucChien_TaPhai_DiemSo -= value.TuVongSoLuong / 2;
						}
					}
					if (value.GietNguoiSoLuong > 0)
					{
						if (value3.TheLucChien_PhePhai == "TA_PHAI")
						{
							TheLucChien_TaPhai_SoNguoi--;
							TheLucChien_TaPhai_DiemSo -= value.GietNguoiSoLuong / 2;
						}
						else if (value3.TheLucChien_PhePhai == "CHINH_PHAI")
						{
							TheLucChien_ChinhPhai_SoNguoi--;
							TheLucChien_ChinhPhai_DiemSo -= value.GietNguoiSoLuong / 2;
						}
					}
					if (value3.Tao_GietNguoi_Trong_TLC.Count > 0)
					{
						foreach (var item in value3.Tao_GietNguoi_Trong_TLC.Select((EventTopClass x) => x.TenNhanVat).Distinct())
						{
							if (EventTop.TryGetValue(item, out var value2))
							{
								value2.GietNguoiSoLuong -= value.TuVongSoLuong / 2;
							}
						}
						value3.Tao_GietNguoi_Trong_TLC.Clear();
					}
					EventTop.Remove(value3.CharacterName);
					var txt = "[" + value3.CharacterName + "] -Kill:[" + value.GietNguoiSoLuong + "] -Die:[" + value.TuVongSoLuong + "]-----Diem9Truoc:[" + theLucChien_ChinhPhai_DiemSo + "]-Sau:[" + TheLucChien_ChinhPhai_DiemSo + "]-----DiemTaTruoc:[" + theLucChien_TaPhai_DiemSo + "]-Sau:[" + TheLucChien_TaPhai_DiemSo + "][";
					// logo.Log_Kick_TheLucChien(txt);
				}
				if (value3.NhanVat_HP <= 0 || value3.PlayerTuVong)
				{
					value3.NhanVat_HP = value3.CharacterMax_HP;
					value3.CapNhat_HP_MP_SP();
					value3.PlayerTuVong = false;
					value3.DeathMove(value3.NhanVatToaDo_X, value3.NhanVatToaDo_Y, value3.NhanVatToaDo_Z, value3.NhanVatToaDo_BanDo);
				}
				value3.Tao_GietNguoi_Trong_TLC.Clear();
				EventClass.GUI_DI_THE_LUC_CHIEN_THAM_DU_FEED_MANG_BI_KICK_RA_TIN_TUC(value3);
				EventClass.KET_THUC_THE_LUC_CHIEN_NHAC_NHO(value3);
				if (value3.Client != null)
				{
					GuiThongBao("Nhân vật [" + value3.CharacterName + "] bị kick khỏi TLC vì giết [" + value.GietNguoiSoLuong + "] - chết [" + value.TuVongSoLuong + "] mạng");
					value.TuVongSoLuong = 0;
					value.GietNguoiSoLuong = 0;
					value3.HeThongNhacNho("Đại hiệp bị trục xuất vì liên tục hi sinh quá nhiều trong Thế Lực Chiến!!", 7, "Thiên cơ các");
					value3.Mobile(420f, 1740f, 15f, 101, 0);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi [Auto Kick Feed TLC] - " + ex.Message);
		}
	}

	public static void Bat_Event_Noel_World()
	{
		try
		{
			if (EventNoel != null)
			{
				EventNoel.Dispose();
			}
			EventNoel = new();
			if (CoMoRa_TuyetRoiHayKhong != 0)
			{
				return;
			}
			CoMoRa_TuyetRoiHayKhong = 1;
			foreach (var value in allConnectedChars.Values)
			{
				if (!value.Client.TreoMay)
				{
					value.Packet_TuyetRoi();
				}
			}
		}
		catch
		{
		}
	}

	public static void Bat_TheLucChien_ChinhTa_World()
	{
		try
		{
			if (eve != null)
			{
				eve.Dispose();
			}
			eve = new();
			foreach (var value in allConnectedChars.Values)
			{
				if (value.Player_Job_level >= 2 && !value.Client.TreoMay)
				{
					value.RollingAnnouncement(637);
					value.GuiDi_TheLucChien_LoiMoi_New2();
					var text = "Thế lực Chiến sắp khai diễn, quần hùng hội tụ! Trong [" + Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang + "] phút tới, kinh nghiệm, vàng và bảo vật rơi vãi khắp nơi tăng gấp bội. Đại chiến khởi sự từ [21:00 đến 21:25], anh hùng giang hồ, hãy sẵn sàng tung hoành!";
					conn.Transmit("PK_MESSAGE|" + 10 + "|" + text);
					break;
				}
			}
		}
		catch
		{
		}
	}

	//public static void Bat_TheLucChien_Random_World()
	//{
	//	try
	//	{
	//		//if (eve != null)
	//		//{
	//		//	eve.Dispose();
	//		//}
	//		//eve = new();
	//		//tmc_flag = true;
	//		//foreach (var value in allConnectedChars.Values)
	//		//{
	//		//	if (value.Player_Job_level >= 2 && !value.Client.TreoMay)
	//		//	{
	//		//		value.RollingAnnouncement(637);
	//		//		value.GuiDi_TheLucChien_LoiMoi_New2();
	//		//		var text = "TH熟 L蒡C CHI熟N (Random) 餫ng di贽n ra,\r\n t沆t Exp Gold Drop trong [" + Thoi_Gian_Ket_Thuc_Giam_Kinh_Nghiem_nho_hon_hoac_bang + "] phu靦,\r\n b沆t 疴蘵 t\ue435 [21:00 > 21:25] !!";
	//		//		conn.Transmit("PK_MESSAGE|" + 10 + "|" + text);
	//		//		break;
	//		//	}
	//		//}
	//	}
	//	catch
	//	{
	//	}
	//}

	//public static void Bat_Event_KLT()
	//{
	//	try
	//	{
	//		//if (KhuLuyenTap_PK_Event != null)
	//		//{
	//		//	KhuLuyenTap_PK_Event.Dispose();
	//		//}
	//		//KhuLuyenTap_PK_Event = new();
	//		//LogHelper.WriteLine(LogLevel.Info, "Auto mở Khu Luyện Tập !!");
	//	}
	//	catch
	//	{
	//		LogHelper.WriteLine(LogLevel.Error, "Lỗi bật Event Khu Luyện Tập !!!");
	//	}
	//}

	public static void Bat_DaiChienHon_World()
	{
		try
		{
			if (dch_event_check != null)
			{
				dch_event_check.Dispose();
			}
			dch_event_check = new();
			LogHelper.WriteLine(LogLevel.Info, "Auto mở Đại Chiến Hồn !!");
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi bật Event Dai Chien Hon !!!");
		}
	}

	//public static void WORLD_Event_Hai_Thuoc()
	//{
	//	try
	//	{
	//		if (HaiThuoc_NamLam_Event == null)
	//		{
	//			HaiThuoc_NamLam_Event = new();
	//			LogHelper.WriteLine(LogLevel.Info, "AUTO BẬT Event Hái Thuốc Nam Lâm");
	//		}
	//		else
	//		{
	//			HaiThuoc_NamLam_Event.Dispose();
	//			LogHelper.WriteLine(LogLevel.Info, "TẮT Event Hái Thuốc Nam Lâm");
	//		}
	//	}
	//	catch
	//	{
	//		LogHelper.WriteLine(LogLevel.Error, "Lỗi bật Event HaiThuoc !!!");
	//	}
	//}




	public static void Event_Boss_Map_World()
	{
		try
		{
			//if (Boss_Map_Event == null)
			//{
			//	Boss_Map_Event = new();
			//	LogHelper.WriteLine(LogLevel.Info, "AUTO BẬT Event BOSS MAP");
			//}
			//else
			//{
			//	Boss_Map_Event.Dispose();
			//	LogHelper.WriteLine(LogLevel.Info, "TẮT Event BOSS MAP");
			//	GuiThongBao("Sự kiện Boss Map kết thúc, tất cả Boss đã biến mất !!");
			//}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi bật Event Boss Map World !!!");
		}
	}


	private void ThoiGianSuKien60giay(object sender, ElapsedEventArgs e)
	{
	}


	public void SetScript()
	{
		//ScriptManager.Instance.Initialize();
	}

	public static void ToanCucNhacNho(string string_0, int NumberColor, string TinNhan)
	{
		try
		{

			foreach (var value in allConnectedChars.Values)
			{
				if (value != null && value.Client != null && !value.Client.TreoMay)
				{
					try
					{
						value.HeThongNhacNho(TinNhan, NumberColor, string_0.Trim());
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, $"Lỗi gửi thông báo cho người chơi: {ex.Message}");
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi ToanCucNhacNho :" + ex.Message);
		}
	}

	public static readonly Dictionary<int, UpgradeItemClass> List_UpgradeItem;
	public static void SetUpgradeItem()
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_Upgrade_Item", "PublicDb");
		if (dBToDataTable == null) return;
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "TBL_Upgrade_Item ---- no data");
		}
		else
		{
			List_UpgradeItem.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				UpgradeItemClass upgradeItemClass = new()
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					ItemID = (int)dBToDataTable.Rows[i]["ItemID"],
					ItemName = dBToDataTable.Rows[i]["ItemName"].ToString(),
					ItemLevel = (int)dBToDataTable.Rows[i]["ItemLevel"],
					ItemType = (int)dBToDataTable.Rows[i]["ItemType"],
					Upgrade_PP = (int)dBToDataTable.Rows[i]["Upgrade_PP"],
					NguyenLieu_ID = (int)dBToDataTable.Rows[i]["NguyenLieu_ID"],
					GiamCuongHoa = (int)dBToDataTable.Rows[i]["GiamCuongHoa"],
					YeuCauCuongHoa = (int)dBToDataTable.Rows[i]["YeuCauCuongHoa"]
				};
				List_UpgradeItem.Add(upgradeItemClass.ItemID, upgradeItemClass);
			}

			LogHelper.WriteLine(LogLevel.Info, "Load TBL_Upgrade_Item: " + dBToDataTable.Rows.Count);
		}

		dBToDataTable.Dispose();
	}

	public static bool KiemTraSoLieu_DatabaseConfig()
	{
		// using (SqlConnection sqlConnection = new(DBA.getstrConnection("rxjhaccount")))
		// {
		// 	try
		// 	{
		// 		sqlConnection.Open();
		// 	}
		// 	catch
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "SoLieu Thư viện rxjhaccount Lỗi cấu hình，không thể kết nối");
		// 		return false;
		// 	}
		// 	finally
		// 	{
		// 		sqlConnection.Close();
		// 	}
		// }
		// using (SqlConnection sqlConnection2 = new(DBA.getstrConnection("GameServer")))
		// {
		// 	try
		// 	{
		// 		sqlConnection2.Open();
		// 	}
		// 	catch
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "SoLieu Thư viện rxjhgame Lỗi cấu hình，không thể kết nối");
		// 		return false;
		// 	}
		// 	finally
		// 	{
		// 		sqlConnection2.Close();
		// 	}
		// }
		// using (SqlConnection sqlConnection3 = new(DBA.getstrConnection("PublicDb")))
		// {
		// 	try
		// 	{
		// 		sqlConnection3.Open();
		// 	}
		// 	catch
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "SoLieu Thư viện PublicDb Lỗi cấu hình，không thể kết nối");
		// 		return false;
		// 	}
		// 	finally
		// 	{
		// 		sqlConnection3.Close();
		// 	}
		// }
		// using (SqlConnection sqlConnection4 = new(DBA.getstrConnection("WebDb")))
		// {
		// 	try
		// 	{
		// 		sqlConnection4.Open();
		// 	}
		// 	catch
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "SoLieu Thư viện WebDb Lỗi cấu hình，không thể kết nối");
		// 		return false;
		// 	}
		// 	finally
		// 	{
		// 		sqlConnection4.Close();
		// 	}
		// }
		// using (SqlConnection sqlConnection5 = new(DBA.getstrConnection("BBG")))
		// {
		// 	try
		// 	{
		// 		sqlConnection5.Open();
		// 	}
		// 	catch
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "SoLieu Thư viện BBG Lỗi cấu hình，không thể kết nối");
		// 		return false;
		// 	}
		// 	finally
		// 	{
		// 		sqlConnection5.Close();
		// 	}
		// }
		return true;
	}


	public void SetConfig2()
	{
		// Db.Add("rxjhaccount", new()
		// {
		// 	ServerDb = "rxjhaccount",
		// 	SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("rxjhaccount", "Server").Trim(), Config.IniReadValue("rxjhaccount", "UserName").Trim(), Config.IniReadValue("rxjhaccount", "PassWord").Trim(), Config.IniReadValue("rxjhaccount", "DataName").Trim())
		// });
		// Db.Add("GameServer", new()
		// {
		// 	ServerDb = "GameServer",
		// 	SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("GameServer", "Server").Trim(), Config.IniReadValue("GameServer", "UserName").Trim(), Config.IniReadValue("GameServer", "PassWord").Trim(), Config.IniReadValue("GameServer", "DataName").Trim())
		// });
		// Db.Add("PublicDb", new()
		// {
		// 	ServerDb = "PublicDb",
		// 	SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("PublicDb", "Server").Trim(), Config.IniReadValue("PublicDb", "UserName").Trim(), Config.IniReadValue("PublicDb", "PassWord").Trim(), Config.IniReadValue("PublicDb", "DataName").Trim())
		// });
		// Db.Add("WebDb", new()
		// {
		// 	ServerDb = "WebDb",
		// 	SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("WebDb", "Server").Trim(), Config.IniReadValue("WebDb", "UserName").Trim(), Config.IniReadValue("WebDb", "PassWord").Trim(), Config.IniReadValue("WebDb", "DataName").Trim())
		// });
		// Db.Add("BBG", new()
		// {
		// 	ServerDb = "BBG",
		// 	SqlConnect = string.Format("Data Source={0};uid={1};pwd=***;database={3};Packet Size=4096;Pooling=true;Max Pool Size=512;Min Pool Size=1", Config.IniReadValue("BBG", "Server").Trim(), Config.IniReadValue("BBG", "UserName").Trim(), Config.IniReadValue("BBG", "PassWord").Trim(), Config.IniReadValue("BBG", "DataName").Trim())
		// });
	}

	public void SetWxLever()
	{
		Wxlever.Clear();
		Wxlever.Add(0, 0.0);
		Wxlever.Add(1, 1000.0);
		Wxlever.Add(2, 15000.0);
		Wxlever.Add(3, 50000.0);
		Wxlever.Add(4, 100000.0);
		Wxlever.Add(5, 599999.0);
		Wxlever.Add(6, 1199999.0);
		Wxlever.Add(7, 1200000.0);
		Wxlever.Add(8, 2000000.0);
		Wxlever.Add(9, 5000000.0);
		LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu 9 cấp độ võ huân thành công");
	}


	public void SetKhuAnToan()
	{
		KhuTapLuyenPK.Add(new()
		{
			Rxjh_name = "KhuTapLuyenPK",
			Rxjh_Map = 2301,
			Rxjh_X = 120f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		KhuTapLuyenPK.Add(new()
		{
			Rxjh_name = "KhuTapLuyenPK",
			Rxjh_Map = 2341,
			Rxjh_X = 120f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		TheLucChien_KhuVuc.Add(new()
		{
			Rxjh_name = "TheLucChien_KhuVuc",
			Rxjh_Map = 801,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
		BangChien_KhuVuc.Add(new()
		{
			Rxjh_name = "BangChien_KhuVuc",
			Rxjh_Map = 7301,
			Rxjh_X = 0f,
			Rxjh_Y = 0f,
			Rxjh_Z = 15f
		});
	}


	public void SetJianc()
	{
		if (KiemTraVatPham_BatHopPhap != 2)
		{
			return;
		}
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  KiemTraThietBi", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			KiemTraThietBiList.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				KiemTraThietBiList.Add((int)dBToDataTable.Rows[i]["VatPhamLoaiHinh"], new()
				{
					VatPhamCaoNhatCongKichGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatCongKichGiaTri"],
					VatPhamCaoNhatPhongNguGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatPhongNguGiaTri"],
					VatPhamCaoNhatHPGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatHPGiaTri"],
					VatPhamCaoNhatNoiCongGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatNoiCongGiaTri"],
					VatPhamCaoNhatTrungDichGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatTrungDichGiaTri"],
					VatPhamCaoNhatNeTranhGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatNeTranhGiaTri"],
					VatPhamCaoNhatCongKichVoCongGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatCongKichVoCongGiaTri"],
					VatPhamCaoNhatKhiCongGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatKhiCongGiaTri"],
					VatPhamCaoNhatPhuHonGiaTri = (int)dBToDataTable.Rows[i]["VatPhamCaoNhatPhuHonGiaTri"]
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu kiểm tra thiết bị hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}


	public void SetQG()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_SKILL", "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count != 0)
			{
				KhiCongTangThem.Clear();
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					KhiCongTangThem.Add(i, new()
					{
						FLD_PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
						FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"],
						FLD_JOB = (int)dBToDataTable.Rows[i]["FLD_JOB"],
						FLD_NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						FLD_BonusRateValuePerPoint1 = (double)dBToDataTable.Rows[i]["FLD_BonusRateValuePerPoint1"],
						FLD_BonusRateValuePerPoint2 = (double)dBToDataTable.Rows[i]["FLD_BonusRateValuePerPoint2"]
					});
				}
				LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu tỷ lệ thưởng khí công hoàn thành " + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		KhiCong_CoBan_ID.Clear();
		for (var j = 0; j < 12; j++)
		{
			for (var k = 1; k < 14; k++)
			{
				KhiCong_CoBan_ID.Add(method_0(j, k));
			}
		}
		KhiCong_CoBan_ID.Sort();
	}


	public void SetKill()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  XWWL_kill", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count != 0)
		{
			Kill.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Kill.Add(new()
				{
					Txt = dBToDataTable.Rows[i]["txt"].ToString(),
					Sffh = (int)dBToDataTable.Rows[i]["sffh"]
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu bộ lọc hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}


	public void SetDangCapBanThuong()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  DangCapBanThuong", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải phần thưởng cấp độ [Không có dữ liệu phần thưởng cấp độ]");
		}
		else
		{
			DangCapBanThuong.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				DangCapBanThuong.Add(i, new()
				{
					DangCap = (int)dBToDataTable.Rows[i]["DangCap"],
					VoHuan = (int)dBToDataTable.Rows[i]["VoHuan"],
					NguyenBao = (int)dBToDataTable.Rows[i]["NguyenBao"],
					SinhMenh = (int)dBToDataTable.Rows[i]["SinhMenh"],
					CongKich = (int)dBToDataTable.Rows[i]["CongKich"],
					PhongNgu = (int)dBToDataTable.Rows[i]["PhongNgu"],
					NeTranh = (int)dBToDataTable.Rows[i]["NeTranh"],
					TrungDich = (int)dBToDataTable.Rows[i]["TrungDich"],
					NoiCong = (int)dBToDataTable.Rows[i]["NoiCong"],
					Set = (int)dBToDataTable.Rows[i]["Set_ID"],
					TienBac = dBToDataTable.Rows[i]["TienBac"].ToString(),
					GoiVatPham = dBToDataTable.Rows[i]["GoiVatPham"].ToString()
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu phần thưởng cấp độ hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}


	public void SetDaThuocTinh()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_STONE", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải thuộc tính đá [Không có dữ liệu về đá]");
		}
		else
		{
			clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				SetStone((int)dBToDataTable.Rows[i]["FLD_TYPE"], (int)dBToDataTable.Rows[i]["FLD_VALUE"], (int)dBToDataTable.Rows[i]["FLD_TangGiam"]);
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu hiệu ứng thuộc tính đá đã hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetStone(int int_0, int int_1, int int_2)
	{
		try
		{
			switch (int_0)
			{
				case 2:
					switch (int_1)
					{
						case 10:
							f10 += int_2;
							break;
						case 11:
							f11 += int_2;
							break;
						case 12:
							f12 += int_2;
							break;
						case 13:
							f13 += int_2;
							break;
						case 14:
							f14 += int_2;
							break;
						case 15:
							f15 += int_2;
							break;
					}
					break;
				case 1:
					switch (int_1)
					{
						case 20:
							g20 += int_2;
							break;
						case 21:
							g21 += int_2;
							break;
						case 22:
							g22 += int_2;
							break;
						case 23:
							g23 += int_2;
							break;
						case 24:
							g24 += int_2;
							break;
						case 25:
							g25 += int_2;
							break;
					}
					break;
				case 11:
					switch (int_1)
					{
						case 69:
							break;
						case 71:
							break;
						case 73:
							break;
						case 75:
							break;
						case 77:
							break;
						case 79:
							break;
						case 81:
						case 82:
						case 83:
						case 84:
							break;
						case 86:
						case 87:
						case 88:
						case 89:
							break;
						case 91:
						case 92:
						case 93:
						case 94:
							break;
						case 96:
						case 97:
						case 98:
						case 99:
							break;
						case 68:
							wf68 += int_2;
							break;
						case 70:
							wf70 += int_2;
							break;
						case 72:
							wf72 += int_2;
							break;
						case 74:
							wf74 += int_2;
							break;
						case 76:
							wf76 += int_2;
							break;
						case 78:
							wf78 += int_2;
							break;
						case 80:
							wf80 += int_2;
							break;
						case 85:
							wf85 += int_2;
							break;
						case 90:
							wf90 += int_2;
							break;
						case 95:
							wf95 += int_2;
							break;
						case 100:
							wf100 += int_2;
							break;
					}
					break;
				case 7:
					switch (int_1)
					{
						case 25:
							wg25 += int_2;
							break;
						case 26:
							wg26 += int_2;
							break;
						case 27:
							wg27 += int_2;
							break;
						case 28:
							wg28 += int_2;
							break;
						case 29:
							wg29 += int_2;
							break;
						case 30:
							wg30 += int_2;
							break;
						case 31:
							wg31 += int_2;
							break;
						case 32:
							wg32 += int_2;
							break;
						case 33:
							wg33 += int_2;
							break;
						case 34:
							wg34 += int_2;
							break;
						case 35:
							wg35 += int_2;
							break;
						case 36:
							wg36 += int_2;
							break;
						case 37:
							wg37 += int_2;
							break;
						case 38:
							wg38 += int_2;
							break;
						case 39:
							wg39 += int_2;
							break;
						case 40:
							wg40 += int_2;
							break;
					}
					break;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi đặt đá|" + int_0 + "|" + int_1 + "|" + int_2 + "|" + ex.Message);
		}
	}


	public void SetVatPhamTraoDoi()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  VatPhamTraoDoi", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải trao đổi vật phẩm [Không có dữ liệu trao đổi vật phẩm]");
		}
		else
		{
			VatPhamTraoDoi.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				VatPhamTraoDoi.Add(i, new()
				{
					CanVatPham = dBToDataTable.Rows[i]["CanVatPham"].ToString(),
					VoHuan = (int)dBToDataTable.Rows[i]["VoHuan"],
					NguyenBao = (int)dBToDataTable.Rows[i]["NguyenBao"],
					SinhMenh = (int)dBToDataTable.Rows[i]["SinhMenh"],
					CongKich = (int)dBToDataTable.Rows[i]["CongKich"],
					PhongNgu = (int)dBToDataTable.Rows[i]["PhongNgu"],
					NeTranh = (int)dBToDataTable.Rows[i]["NeTranh"],
					TrungDich = (int)dBToDataTable.Rows[i]["TrungDich"],
					NoiCong = (int)dBToDataTable.Rows[i]["NoiCong"],
					Set = (int)dBToDataTable.Rows[i]["Set_ID"],
					TienBac = dBToDataTable.Rows[i]["TienBac"].ToString(),
					GoiVatPham = dBToDataTable.Rows[i]["GoiVatPham"].ToString()
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu trao đổi vật phẩm đã hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}


	public void SetThongBao()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_Gg", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải thông báo [Không có dữ liệu Gg để đọc trong Database]");
		}
		else
		{
			ThongBao.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				ThongBao.Add(i, new()
				{
					msg = dBToDataTable.Rows[i]["txt"].ToString(),
					type = (int)dBToDataTable.Rows[i]["type"]
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu thông báo hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetTreoMayUpdate()
	{
		var dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TreoMay_KiemSoat", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tăng thêm TreoMay Số liệu mới ra sai ---- Không có TreoMay Số liệu");
		}
		else
		{
			TreoMay_SoLieu.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				X_Treo_May_Offline_Loai x_Treo_May_Offline_Loai = new();
				x_Treo_May_Offline_Loai.TreoMay_ThoiGian = (int)dBToDataTable.Rows[i]["TreoMay_ThoiGian"];
				x_Treo_May_Offline_Loai.TreoMay_BanDo = (int)dBToDataTable.Rows[i]["TreoMay_BanDo"];
				x_Treo_May_Offline_Loai.TreoMay_ToaDo_X = float.Parse(dBToDataTable.Rows[i]["TreoMay_ToaDo_X"].ToString());
				x_Treo_May_Offline_Loai.TreoMay_ToaDo_Y = float.Parse(dBToDataTable.Rows[i]["TreoMay_ToaDo_Y"].ToString());
				x_Treo_May_Offline_Loai.TreoMayNguyenBaoSoLuong = (int)dBToDataTable.Rows[i]["TreoMayNguyenBaoSoLuong"];
				x_Treo_May_Offline_Loai.TreoMayTichLuySoLuong = (int)dBToDataTable.Rows[i]["TreoMayTichLuySoLuong"];
				x_Treo_May_Offline_Loai.TreoMayDangCapNhoNhat = (int)dBToDataTable.Rows[i]["TreoMayDangCapNhoNhat"];
				x_Treo_May_Offline_Loai.TreoMayDangCapLonNhat = (int)dBToDataTable.Rows[i]["TreoMayDangCapLonNhat"];
				x_Treo_May_Offline_Loai.TreoMay_ToaDo_PhamVi = (int)dBToDataTable.Rows[i]["TreoMay_ToaDo_PhamVi"];
				x_Treo_May_Offline_Loai.TreoMay_NhacNho = dBToDataTable.Rows[i]["TreoMay_NhacNho"].ToString();
				TreoMay_SoLieu.Add(i, x_Treo_May_Offline_Loai);
			}
			LogHelper.WriteLine(LogLevel.Info, "Tăng thêm TreoMay Số liệu hoàn thành - " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public int Getmonterbyqitem(int qitem)
	{
		if (qitem == Item_Quest)
		{
			return ID_Quai;
		}
		return qitem switch
		{
			********* => 10007,
			********* => 10009,
			********* => 10008,
			********* => 10011,
			********* => 10012,
			********* => 15020,
			********* => 15019,
			********* => 15018,
			********* => 10018,
			********* => 10010,
			_ => 0,
		};
	}


	public void SetNhiemVuSoLieuMoi()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT *  FROM  TBL_XWWL_MISSION", "PublicDb");
		var num = 0;
		try
		{
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi mới khi tải dữ liệu nhiệm vụ [Không có dữ liệu chuyển nhiệm vụ]");
			}
			else
			{
				NhiemVulist.Clear();
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					X_Nhiem_Vu_Loai x_Nhiem_Vu_Loai = new();
					x_Nhiem_Vu_Loai.RwID = (int)dBToDataTable.Rows[i]["FLD_PID"];
					num = x_Nhiem_Vu_Loai.RwID;
					x_Nhiem_Vu_Loai.NhiemVu_Ten = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
					x_Nhiem_Vu_Loai.NhiemVuDangCap = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
					x_Nhiem_Vu_Loai.NhiemVu_ChinhTa = (int)dBToDataTable.Rows[i]["FLD_ZX"];
					x_Nhiem_Vu_Loai.NgheNghiep = (int)dBToDataTable.Rows[i]["FLD_JOB"];
					x_Nhiem_Vu_Loai.NpcID = (int)dBToDataTable.Rows[i]["FLD_NPCID"];
					x_Nhiem_Vu_Loai.NPCNAME = dBToDataTable.Rows[i]["FLD_NPCNAME"].ToString();
					x_Nhiem_Vu_Loai.ToaDo_NPC.MAP_ID = (int)dBToDataTable.Rows[i]["FLD_MAP"];
					x_Nhiem_Vu_Loai.ToaDo_NPC.ToaDoX = (int)dBToDataTable.Rows[i]["FLD_X"];
					x_Nhiem_Vu_Loai.ToaDo_NPC.ToaDoY = (int)dBToDataTable.Rows[i]["FLD_Y"];
					x_Nhiem_Vu_Loai.NhiemVu_TieuSuNoiDung = dBToDataTable.Rows[i]["FLD_MSG"].ToString();
					x_Nhiem_Vu_Loai.NhiemVuSwitch = (int)dBToDataTable.Rows[i]["FLD_ON"];
					x_Nhiem_Vu_Loai.NhiemVu_Type = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
					var text = dBToDataTable.Rows[i]["FLD_NEED_ITEM"].ToString();
					if (text.Length > 0)
					{
						var text2 = text;
						var separator = new char[1] { ';' };
						var array = text2.Split(separator);
						var array2 = array;
						foreach (var text3 in array2)
						{
							X_Nhiem_Vu_Can_Vat_Pham_Loai x_Nhiem_Vu_Can_Vat_Pham_Loai = new();
							var separator2 = new char[1] { '|' };
							var array3 = text3.Split(separator2);
							x_Nhiem_Vu_Can_Vat_Pham_Loai.VatPham_ID = int.Parse(array3[0]);
							x_Nhiem_Vu_Can_Vat_Pham_Loai.VatPhamSoLuong = int.Parse(array3[1]);
							x_Nhiem_Vu_Loai.NhiemVuCanVatPham.Add(x_Nhiem_Vu_Can_Vat_Pham_Loai);
						}
					}
					var text4 = dBToDataTable.Rows[i]["FLD_GET_ITEM"].ToString();
					if (text4.Length > 0)
					{
						var text5 = text4;
						var separator3 = new char[1] { ';' };
						var array4 = text5.Split(separator3);
						var array5 = array4;
						foreach (var text6 in array5)
						{
							X_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai x_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai = new();
							var separator4 = new char[1] { '|' };
							var array6 = text6.Split(separator4);
							x_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai.VatPham_ID = int.Parse(array6[0]);
							x_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai.VatPhamSoLuong = int.Parse(array6[1]);
							x_Nhiem_Vu_Loai.NhiemVu_NhanVatPham.Add(x_Nhiem_Vu_Thu_Hoach_Duoc_Vat_Pham_Loai);
						}
					}
					x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu = (byte[])dBToDataTable.Rows[i]["FLD_DATA"];
					if (x_Nhiem_Vu_Loai.RwID == 1201)
					{
					}
					if (x_Nhiem_Vu_Loai.RwID == 1203)
					{
					}
					if (x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu.Length < 5)
					{
						continue;
					}
					x_Nhiem_Vu_Loai.NpcID = BitConverter.ToInt16(x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu, 2);
					x_Nhiem_Vu_Loai.NhiemVuGiaiDoan_SoLuong = BitConverter.ToInt16(x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu, 4);
					if (x_Nhiem_Vu_Loai.NhiemVuGiaiDoan_SoLuong == 0)
					{
						X_Nhiem_Vu_Giai_Doan_Loai x_Nhiem_Vu_Giai_Doan_Loai = new();
						var array7 = new byte[530];
						Buffer.BlockCopy(x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu, 6, array7, 0, 530);
						x_Nhiem_Vu_Giai_Doan_Loai.GiaiDoanID = BitConverter.ToInt16(array7, 0);
						x_Nhiem_Vu_Giai_Doan_Loai.NpcID = BitConverter.ToInt16(array7, 2);
						x_Nhiem_Vu_Giai_Doan_Loai.MucDo_KhoKhan = BitConverter.ToInt16(array7, 4);
						for (var l = 0; l < 10; l++)
						{
							var array8 = new byte[12];
							Buffer.BlockCopy(array7, l * 12 + 6, array8, 0, 12);
							if (BitConverter.ToInt64(array8, 0) == 0)
							{
								break;
							}
							x_Nhiem_Vu_Giai_Doan_Loai.GiaiDoanCanVatPham_.Add(l, new()
							{
								QuaiVat_ID = Getmonterbyqitem(BitConverter.ToInt32(array8, 4)),
								VatPham_ID = BitConverter.ToInt32(array8, 4),
								TongSoVatPham = BitConverter.ToInt16(array8, 8),
								ThucTe_CanSoLuong = BitConverter.ToInt16(array8, 10)
							});
						}
						for (var m = 0; m < 10; m++)
						{
							var array9 = new byte[8];
							Buffer.BlockCopy(array7, m * 8 + 126, array9, 0, 8);
							if (BitConverter.ToInt32(array9, 0) == 0)
							{
								break;
							}
							x_Nhiem_Vu_Giai_Doan_Loai.GiaiDoanPhanThuongVatPham_.Add(m, new()
							{
								VatPham_ID = BitConverter.ToInt32(array9, 0),
								VatPhamSoLuong = BitConverter.ToInt32(array9, 4)
							});
						}
						var array10 = new byte[14];
						Buffer.BlockCopy(array7, 206, array10, 0, 14);
						x_Nhiem_Vu_Giai_Doan_Loai.NPCNAME = Encoding.Default.GetString(array10).Replace("\0", string.Empty).Trim();
						x_Nhiem_Vu_Giai_Doan_Loai.NpcMAP_ID = BitConverter.ToInt16(array7, 220);
						x_Nhiem_Vu_Giai_Doan_Loai.ToaDo_NPCX = BitConverter.ToInt32(array7, 222);
						x_Nhiem_Vu_Giai_Doan_Loai.ToaDo_NPCY = BitConverter.ToInt32(array7, 226);
						var array11 = new byte[300];
						Buffer.BlockCopy(array7, 230, array11, 0, 300);
						x_Nhiem_Vu_Giai_Doan_Loai.NhiemVu_GiaiDoanNoiDung = Encoding.Default.GetString(array11).Replace("\0", string.Empty).Trim();
						x_Nhiem_Vu_Loai.NhiemVu_GiaiDoan.Add(x_Nhiem_Vu_Giai_Doan_Loai);
					}
					else
					{
						for (var n = 0; n < x_Nhiem_Vu_Loai.NhiemVuGiaiDoan_SoLuong; n++)
						{
							X_Nhiem_Vu_Giai_Doan_Loai x_Nhiem_Vu_Giai_Doan_Loai2 = new();
							var array12 = new byte[530];
							Buffer.BlockCopy(x_Nhiem_Vu_Loai.GiaiDoan_ThucHien_SoLieu, n * 530 + 6, array12, 0, 530);
							x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanID = BitConverter.ToInt16(array12, 0);
							x_Nhiem_Vu_Giai_Doan_Loai2.NpcID = BitConverter.ToInt16(array12, 2);
							x_Nhiem_Vu_Giai_Doan_Loai2.MucDo_KhoKhan = BitConverter.ToInt16(array12, 4);
							for (var num2 = 0; num2 < 10; num2++)
							{
								var array13 = new byte[12];
								Buffer.BlockCopy(array12, num2 * 12 + 6, array13, 0, 12);
								if (BitConverter.ToInt64(array13, 0) == 0)
								{
									break;
								}
								if (x_Nhiem_Vu_Loai.RwID == 1201)
								{
									x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanCanVatPham_.Add(num2, new()
									{
										QuaiVat_ID = ID_Quai,
										VatPham_ID = Item_Quest,
										TongSoVatPham = 100,
										ThucTe_CanSoLuong = 100
									});
								}
								else if (x_Nhiem_Vu_Loai.RwID == 1204)
								{
									x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanCanVatPham_.Add(num2, new()
									{
										QuaiVat_ID = 10007,
										VatPham_ID = 999000501,
										TongSoVatPham = 10,
										ThucTe_CanSoLuong = 10
									});
								}
								else if (x_Nhiem_Vu_Loai.RwID == 1202)
								{
									x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanCanVatPham_.Add(num2, new()
									{
										QuaiVat_ID = 15434,
										VatPham_ID = 999000297,
										TongSoVatPham = 100,
										ThucTe_CanSoLuong = 100
									});
								}
								else if (x_Nhiem_Vu_Loai.RwID == 1203)
								{
									x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanCanVatPham_.Add(num2, new()
									{
										QuaiVat_ID = 15900,
										VatPham_ID = BitConverter.ToInt32(array13, 4),
										TongSoVatPham = BitConverter.ToInt16(array13, 8),
										ThucTe_CanSoLuong = BitConverter.ToInt16(array13, 10)
									});
								}
								else
								{
									x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanCanVatPham_.Add(num2, new()
									{
										QuaiVat_ID = Getmonterbyqitem(BitConverter.ToInt32(array13, 4)),
										VatPham_ID = BitConverter.ToInt32(array13, 4),
										TongSoVatPham = BitConverter.ToInt16(array13, 8),
										ThucTe_CanSoLuong = BitConverter.ToInt16(array13, 10)
									});
								}
							}
							for (var num3 = 0; num3 < 10; num3++)
							{
								var array14 = new byte[8];
								Buffer.BlockCopy(array12, num3 * 8 + 126, array14, 0, 8);
								if (BitConverter.ToInt32(array14, 0) == 0)
								{
									break;
								}
								x_Nhiem_Vu_Giai_Doan_Loai2.GiaiDoanPhanThuongVatPham_.Add(num3, new()
								{
									VatPham_ID = BitConverter.ToInt32(array14, 0),
									VatPhamSoLuong = BitConverter.ToInt32(array14, 4)
								});
							}
							var array15 = new byte[14];
							Buffer.BlockCopy(array12, 206, array15, 0, 14);
							x_Nhiem_Vu_Giai_Doan_Loai2.NPCNAME = Encoding.Default.GetString(array15).Replace("\0", string.Empty).Trim();
							x_Nhiem_Vu_Giai_Doan_Loai2.NpcMAP_ID = BitConverter.ToInt16(array12, 220);
							x_Nhiem_Vu_Giai_Doan_Loai2.ToaDo_NPCX = BitConverter.ToInt32(array12, 222);
							x_Nhiem_Vu_Giai_Doan_Loai2.ToaDo_NPCY = BitConverter.ToInt32(array12, 226);
							var array16 = new byte[300];
							Buffer.BlockCopy(array12, 230, array16, 0, 300);
							x_Nhiem_Vu_Giai_Doan_Loai2.NhiemVu_GiaiDoanNoiDung = Encoding.Default.GetString(array16).Replace("\0", string.Empty).Trim();
							x_Nhiem_Vu_Loai.NhiemVu_GiaiDoan.Add(x_Nhiem_Vu_Giai_Doan_Loai2);
						}
					}
					NhiemVulist.Add(x_Nhiem_Vu_Loai.RwID, x_Nhiem_Vu_Loai);
				}
				LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu nhiệm vụ hoàn thành " + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi mới khi tải dữ liệu tác vụ, nhiệm vụ ID-" + num + "|" + ex.ToString());
		}
	}


	public void SetKiemTraVatPham()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  XWWL_JC", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải mục kiểm tra [Dữ liệu mặt hàng không được kiểm tra]");
		}
		else
		{
			VatPhamKiemTra.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				VatPhamKiemTra.Add(new()
				{
					VatPham_ID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					VatPhamLoaiHinh = (int)dBToDataTable.Rows[i]["FLD_TYPE"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC1_1 = dBToDataTable.Rows[i]["FLD_MAGIC1_1"].ToString(),
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC2_2 = dBToDataTable.Rows[i]["FLD_MAGIC2_2"].ToString(),
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC3_3 = dBToDataTable.Rows[i]["FLD_MAGIC3_3"].ToString(),
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					FLD_MAGIC4_4 = dBToDataTable.Rows[i]["FLD_MAGIC4_4"].ToString(),
					FLD_MAGIC5 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"],
					FLD_MAGIC5_5 = dBToDataTable.Rows[i]["FLD_MAGIC5_5"].ToString()
				});
			}
			LogHelper.WriteLine(LogLevel.Error, "Quá trình tải dữ liệu mục kiểm tra đã hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetCheDuocVatPham()
	{
		// var dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM CheDuocVatPhamDanhSach ORDER BY VatPham_ID", "PublicDb");
		// if (dBToDataTable == null)
		// {
		// 	return;
		// }
		// if (dBToDataTable.Rows.Count == 0)
		// {
		// 	LogHelper.WriteLine(LogLevel.Error, "Hoàn thành tải và chế tạo các vật phẩm [Không có dữ liệu vật phẩm được chế tạo]");
		// 	return;
		// }
		// CheDuocVatPhamDanhSach.Clear();
		// for (var i = 0; i < dBToDataTable.Rows.Count; i++)
		// {
		// 	X_Che_Duoc_Vat_Pham_Loai x_Che_Duoc_Vat_Pham_Loai = new();
		// 	try
		// 	{
		// 		x_Che_Duoc_Vat_Pham_Loai.VatPham_ID = (int)dBToDataTable.Rows[i]["VatPham_ID"];
		// 		x_Che_Duoc_Vat_Pham_Loai.VatPhamTen = dBToDataTable.Rows[i]["VatPhamTen"].ToString();
		// 		x_Che_Duoc_Vat_Pham_Loai.VatPhamSoLuong = (int)dBToDataTable.Rows[i]["VatPhamSoLuong"];
		// 		var value = dBToDataTable.Rows[i]["CanVatPham"].ToString();
		// 		x_Che_Duoc_Vat_Pham_Loai.CanVatPham = JsonConvert.DeserializeObject<List<X_Che_Duoc_Can_Vat_Pham_Loai>>(value);
		// 		if (!CheDuocVatPhamDanhSach.ContainsKey(x_Che_Duoc_Vat_Pham_Loai.VatPham_ID))
		// 		{
		// 			CheDuocVatPhamDanhSach.Add(x_Che_Duoc_Vat_Pham_Loai.VatPham_ID, x_Che_Duoc_Vat_Pham_Loai);
		// 		}
		// 	}
		// 	catch (Exception ex)
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "Đang tải các mặt hàng dược phẩm lỗi" + x_Che_Duoc_Vat_Pham_Loai.VatPham_ID + "  " + ex.Message);
		// 	}
		// }
		// LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu các mặt hàng dược phẩm hoàn thành " + dBToDataTable.Rows.Count);
	}


	public void SetMobile()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_MAP  WHERE  (X  IS  NOT  NULL)", "PublicDb");
		if (dBToDataTable != null)
		{
			if (dBToDataTable.Rows.Count == 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải thiết bị di động tùy chỉnh----Không có dữ liệu di động");
			}
			else
			{
				DiDong.Clear();
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					DiDong.Add(new()
					{
						Rxjh_name = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						Rxjh_Map = (int)dBToDataTable.Rows[i]["FLD_MID"],
						Rxjh_X = float.Parse(dBToDataTable.Rows[i]["X"].ToString()),
						Rxjh_Y = float.Parse(dBToDataTable.Rows[i]["Y"].ToString()),
						Rxjh_Z = 15f
					});
				}
				LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu di động tùy chỉnh đã hoàn thành " + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		var dBToDataTable2 = DBA.GetDBToDataTable($"SELECT  FLD_MID,FLD_NAME  FROM  TBL_XWWL_MAP", "PublicDb");
		if (dBToDataTable2 == null)
		{
			return;
		}
		if (dBToDataTable2.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải thiết bị di động tùy chỉnh [Không có dữ liệu di động]");
		}
		else
		{
			Maplist.Clear();
			for (var j = 0; j < dBToDataTable2.Rows.Count; j++)
			{
				var key = (int)dBToDataTable2.Rows[j]["FLD_MID"];
				if (!Maplist.ContainsKey(key))
				{
					Maplist.Add(key, dBToDataTable2.Rows[j]["FLD_NAME"].ToString());
				}
			}
		}
		dBToDataTable2.Dispose();
	}


	public void SetLever()
	{
		lever.Clear();
		for (var i = 0; i <= 255; i++)
		{
			if (i <= 0)
			{
				lever.Add(i, 300.0);
			}
			else if (i >= 1 && i < 10)
			{
				lever.Add(i, lever[i - 1] * 1.25);
			}
			else if (i >= 10 && i < 35)
			{
				lever.Add(i, lever[i - 1] * 1.185);
			}
			else if (i >= 35 && i < 60)
			{
				lever.Add(i, lever[i - 1] * 1.175);
			}
			else if (i >= 60 && i < 80)
			{
				lever.Add(i, lever[i - 1] * 1.165);
			}
			else if (i >= 80 && i < 110)
			{
				lever.Add(i, lever[i - 1] * 1.155);
			}
			else if (i >= 110 && i < 113)
			{
				lever.Add(i, lever[i - 1] * 1.145);
			}
			else if (i >= 113 && i < 120)
			{
				lever.Add(i, lever[i - 1] * 1.16);
			}
			else if (i >= 120 && i < 125)
			{
				lever.Add(i, lever[i - 1] * 1.17);
			}
			else if (i >= 125 && i < 126)
			{
				lever.Add(i, lever[i - 1] * 1.22);
			}
			else if (i >= 126 && i < 130)
			{
				lever.Add(i, lever[i - 1] * 1.25);
			}
			else if (i >= 130 && i < 135)
			{
				lever.Add(i, lever[i - 1] * 1.3);
			}
			else if (i >= 135 && i < 140)
			{
				lever.Add(i, lever[i - 1] * 1.35);
			}
			else if (i >= 140 && i < 200)
			{
				lever.Add(i, lever[i - 1] * 1.4);
			}
			else
			{
				lever.Add(i, lever[i - 1] * 1.5);
			}
		}
		LogHelper.WriteLine(LogLevel.Info, "Tải bảng kinh nghiệm thành công");
	}


	public void SetMover()
	{
		try
		{
			var context = HeroYulgang.Core.DatabaseManager.Instance.PublicDb;
			var movers = context.TblXwwlVomes.ToList();

			Mover.Clear();
			foreach (var item in movers)
			{
				Mover.Add(new()
				{
					MAP = (int)item.Map,
					X = (float)item.X,
					Y = (float)item.Y,
					Z = (float)item.Z,
					ToMAP = (int)item.Tomap,
					ToX = (float)item.Tox,
					ToY = (float)item.Toy,
					ToZ = (float)item.Toz
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu di chuyển đã hoàn thành " + movers.Count);


		}
		catch (System.Exception Ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải di chuyển " + Ex.Message);
		}

	}


	public void SetKONGFU()
	{
		// Use EFCore to get
		try
		{
			var context = HeroYulgang.Core.DatabaseManager.Instance.PublicDb;

			var kongfu = context.TblXwwlKongfus.ToList();

			MagicList.Clear();
			foreach (var item in kongfu)
			{
				X_Vo_Cong_Loai magic = new();
				magic.FLD_NAME = item.FldName;
				magic.FLD_AT = (int)item.FldAt;
				magic.FLD_EFFERT = (int)item.FldEffert;
				magic.FLD_INDEX = (int)item.FldIndex;
				magic.FLD_JOB = (int)item.FldJob;
				magic.FLD_JOBLEVEL = (int)item.FldJoblevel;
				magic.FLD_LEVEL = (int)item.FldLevel;
				magic.FLD_MP = (int)item.FldMp;
				magic.FLD_NEEDEXP = (int)item.FldNeedexp;
				magic.FLD_PID = item.FldPid;
				magic.FLD_TYPE = (int)item.FldType;
				magic.FLD_ZX = (int)item.FldZx;
				magic.FLD_CongKichSoLuong = (int)item.FldCongKichSoLuong;
				magic.FLD_VoCongLoaiHinh = (int)item.FldVoCongLoaiHinh;
				magic.FLD_MoiCapThemMP = (int)item.FldMoiCapThemMp;
				magic.FLD_MoiCapThemLichLuyen = (int)item.FldMoiCapThemLichLuyen;
				magic.FLD_MoiCapNguyHai = item.FldMoiCapNguyHai;
				magic.FLD_MoiCapThemNguyHai = (int)item.FldMoiCapThemNguyHai;
				magic.FLD_MoiCapVoCongDiemSo = (int)item.FldMoiCapVoCongDiemSo;
				magic.FLD_TIME = (int)item.FldTime;
				magic.FLD_DEATHTIME = (int)item.FldDeathtime;
				magic.FLD_CDTIME = (int)item.FldCdtime;
				magic.FLD_VoCongToiCaoDangCap = (int)item.FldVoCongToiCaoDangCap;
				magic.FLD_MoiCapThemTuLuyenDangCap = (int)item.FldMoiCapThemTuLuyenDangCap;
				magic.Time_Animation = (int)item.TimeAnimation;


				MagicList.Add(magic.FLD_PID, magic);
			}

			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu võ công hoàn thành " + MagicList.Count);

		}
		catch (System.Exception Ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải võ công " + Ex.Message);
		}
	}


	public static BbgSellClass QueryBachBaoCacItems(int int_0)
	{
		foreach (var value in BachBaoCac_SoLieu.Values)
		{
			if (value.PID == int_0)
			{
				return value;
			}
		}
		return null;
	}

	private void AutoCheckingMailCod()
	{
		try
		{
			var query = "SELECT * FROM MailCod WHERE STATUS = 3 AND EXPIRED_AT < GETDATE()";
			var dbTable = DBA.GetDBToDataTable(query, null, "BBG");
			LogHelper.WriteLine(LogLevel.Info, "Kiểm tra mail cod hết hạn nhận SL: " + dbTable.Rows.Count);
			if (dbTable.Rows.Count > 0)
			{
				var updateQuery = "UPDATE MailCod SET STATUS = 4 WHERE STATUS = 3 AND EXPIRED_AT < GETDATE()";
				var res = DBA.ExeSqlCommand(updateQuery, "BBG").GetAwaiter().GetResult();
				if (res == -1)
				{
					LogHelper.WriteLine(LogLevel.Error, "Không thể cập nhật database AUtoCheckingMailCOd");
					return;
				}
				LogHelper.WriteLine(LogLevel.Info, "Cập nhật mail cod hết hạn nhận");
			}
		}
		catch (Exception e)
		{
			LogHelper.WriteLine(LogLevel.Error, "AutoCheckingMailCod Error : " + e.Message);
		}
	}


	public void setBbgCategory()
	{
		try
		{
			var context = HeroYulgang.Core.DatabaseManager.Instance.BbgDbContext;
			var categories = context.Shopcategories.ToList();

			WebShopCategoryList?.Clear();
			foreach (var item in categories)
			{
				X_WebShop_Category webshopCategory = new()
				{
					ID = (int)item.Id,
					NAME = item.Name,
					PARENTID = (int)item.Parentid,
					DISPLAYORDER = (int)item.Displayorder
				};
				WebShopCategoryList.Add(webshopCategory.ID, webshopCategory);
			}

		}
		catch (Exception e)
		{
			LogHelper.WriteLine(LogLevel.Error, "setBbgCategoryError : " + e.Message);
		}
	}



	public void SetBbgItem()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM ITEM", "BBG");
		if (dBToDataTable == null) return;
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Đang tải các mặt hàng Baibaoge----Không có dữ liệu Baibaoge");
		}
		else
		{
			BachBaoCat_ThuocTinhVatPhamClassList.Clear();
			WebShopItemList?.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				X_Bach_Bao_Cac_Loai x_Bach_Bao_Cac_Loai = new()
				{
					ID = (int)dBToDataTable.Rows[i]["ID"],
					PRODUCT_CODE = dBToDataTable.Rows[i]["PRODUCT_CODE"].ToString(),
					PID = (int)dBToDataTable.Rows[i]["FLD_PID"],
					NAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
					PRICE = (int)dBToDataTable.Rows[i]["FLD_PRICE"],
					PRICE_OLD = (int)dBToDataTable.Rows[i]["FLD_PRICE_OLD"],
					DESC = dBToDataTable.Rows[i]["FLD_DESC"].ToString(),
					RETURN = (int)dBToDataTable.Rows[i]["FLD_RETURN"],
					NUMBER = (int)dBToDataTable.Rows[i]["FLD_NUMBER"],
					MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"],
					ThucTinh = (int)dBToDataTable.Rows[i]["FLD_SoCapPhuHon"],
					TrungCapHon = (int)dBToDataTable.Rows[i]["FLD_TrungCapPhuHon"],
					TienHoa = (int)dBToDataTable.Rows[i]["FLD_TienHoa"],
					KhoaLai = (int)dBToDataTable.Rows[i]["FLD_PhaiChangKhoaLai"],
					NgaySuDung = (int)dBToDataTable.Rows[i]["FLD_DAYS"],
					CATEGORY_ID = (int)dBToDataTable.Rows[i]["CATEGORY_ID"]
				};
				BachBaoCat_ThuocTinhVatPhamClassList.Add(x_Bach_Bao_Cac_Loai.PID, x_Bach_Bao_Cac_Loai);
				WebShopItemList.Add(x_Bach_Bao_Cac_Loai.ID.ToString(), x_Bach_Bao_Cac_Loai);
			}

			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu CashShop hoàn thành " + dBToDataTable.Rows.Count);
		}

		dBToDataTable.Dispose();
	}


	public void SetItme()
	{
		var num = 0;
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_ITEM", "PublicDb");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count == 0)
			{
				LogHelper.WriteLine(LogLevel.Error, "Lỗi khi tải mặt hàng----Không có dữ liệu mặt hàng");
			}
			else
			{
				PVP_TrangBi.Clear();
				PVP_TrangBi_16x_Chan.Clear();
				ItemList.Clear();
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					try
					{
						ItmeClass itmeClass = new();
						var num2 = (int)dBToDataTable.Rows[i]["FLD_NJ"];
						itmeClass.FLD_AT = (int)dBToDataTable.Rows[i]["FLD_AT1"];
						itmeClass.FLD_AT_Max = (int)dBToDataTable.Rows[i]["FLD_AT2"];
						itmeClass.FLD_DF = (int)dBToDataTable.Rows[i]["FLD_DF"];
						itmeClass.FLD_RESIDE1 = (int)dBToDataTable.Rows[i]["FLD_RESIDE1"];
						itmeClass.FLD_RESIDE2 = (int)dBToDataTable.Rows[i]["FLD_RESIDE2"];
						itmeClass.FLD_JOB_LEVEL = (int)dBToDataTable.Rows[i]["FLD_JOB_LEVEL"];
						itmeClass.FLD_LEVEL = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
						itmeClass.FLD_RECYCLE_MONEY = (int)dBToDataTable.Rows[i]["FLD_RECYCLE_MONEY"];
						itmeClass.FLD_SALE_MONEY = (int)dBToDataTable.Rows[i]["FLD_SALE_MONEY"];
						itmeClass.FLD_PID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
						itmeClass.FLD_SEX = (int)dBToDataTable.Rows[i]["FLD_SEX"];
						itmeClass.FLD_WEIGHT = (int)dBToDataTable.Rows[i]["FLD_WEIGHT"];
						itmeClass.FLD_ZX = (int)dBToDataTable.Rows[i]["FLD_ZX"];
						itmeClass.FLD_SIDE = (int)dBToDataTable.Rows[i]["FLD_SIDE"];
						itmeClass.FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"];
						itmeClass.FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"];
						itmeClass.FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"];
						itmeClass.FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"];
						itmeClass.FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC5"];
						itmeClass.FLD_UP_LEVEL = (int)dBToDataTable.Rows[i]["FLD_UP_LEVEL"];
						itmeClass.FLD_XW = (int)dBToDataTable.Rows[i]["FLD_WX"];
						itmeClass.FLD_XWJD = (int)dBToDataTable.Rows[i]["FLD_WXJD"];
						itmeClass.FLD_TYPE = (int)dBToDataTable.Rows[i]["FLD_TYPE"];
						itmeClass.FLD_QUESTITEM = (int)dBToDataTable.Rows[i]["FLD_QUESTITEM"];
						itmeClass.ItmeNAME = dBToDataTable.Rows[i]["FLD_NAME"].ToString();
						itmeClass.FLD_NJ = num2;
						itmeClass.FLD_LOCK = (int)dBToDataTable.Rows[i]["FLD_LOCK"];
						itmeClass.FLD_NEED_MONEY = (int)dBToDataTable.Rows[i]["FLD_NEED_MONEY"];
						itmeClass.FLD_NEED_FIGHTEXP = (int)dBToDataTable.Rows[i]["FLD_NEED_FIGHTEXP"];
						itmeClass.FLD_INTEGRATION = (int)dBToDataTable.Rows[i]["FLD_INTEGRATION"];
						itmeClass.FLD_SERIES = (int)dBToDataTable.Rows[i]["FLD_SERIES"];
						num = itmeClass.FLD_PID;
						ItemList.Add(itmeClass.FLD_PID, itmeClass);
						if (num2 == 1000)
						{
							PVPClass pVPClass = new();
							pVPClass.VatPham_ID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
							pVPClass.VatPhamLoaiHinh = (int)dBToDataTable.Rows[i]["FLD_RESIDE2"];
							pVPClass.VatPhamDangCap = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
							PVP_TrangBi.Add(pVPClass.VatPham_ID, pVPClass);
						}
						else if (itmeClass.FLD_LEVEL == 160 && itmeClass.FLD_JOB_LEVEL == 11 && (itmeClass.FLD_RESIDE2 == 1 || itmeClass.FLD_RESIDE2 == 2 || itmeClass.FLD_RESIDE2 == 5 || itmeClass.FLD_RESIDE2 == 6 || itmeClass.FLD_RESIDE2 == 8))
						{
							PVPClass pVPClass2 = new();
							pVPClass2.VatPham_ID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString());
							pVPClass2.VatPhamLoaiHinh = (int)dBToDataTable.Rows[i]["FLD_RESIDE2"];
							pVPClass2.VatPhamDangCap = (int)dBToDataTable.Rows[i]["FLD_LEVEL"];
							PVP_TrangBi_16x_Chan.Add(pVPClass2.VatPham_ID, pVPClass2);
						}
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, num + "|" + ex.Message);
					}
				}
				LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu Set Item đã hoàn thành " + dBToDataTable.Rows.Count);
			}
			dBToDataTable.Dispose();
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, num + "|" + ex2.Message);
		}
	}


	public void SetShop()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  TBL_XWWL_SELL  ORDER  BY  FLD_INDEX", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tải cửa hàng vật phẩm----Không có dữ liệu mặt hàng");
		}
		else
		{
			Shop.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				Shop.Add(new()
				{
					FLD_NID = int.Parse(dBToDataTable.Rows[i]["FLD_NID"].ToString()),
					FLD_INDEX = (int)dBToDataTable.Rows[i]["FLD_INDEX"],
					FLD_PID = int.Parse(dBToDataTable.Rows[i]["FLD_PID"].ToString()),
					FLD_MONEY = long.Parse(dBToDataTable.Rows[i]["FLD_MONEY"].ToString()),
					FLD_MAGIC0 = (int)dBToDataTable.Rows[i]["FLD_MAGIC0"],
					FLD_MAGIC1 = (int)dBToDataTable.Rows[i]["FLD_MAGIC1"],
					FLD_MAGIC2 = (int)dBToDataTable.Rows[i]["FLD_MAGIC2"],
					FLD_MAGIC3 = (int)dBToDataTable.Rows[i]["FLD_MAGIC3"],
					FLD_MAGIC4 = (int)dBToDataTable.Rows[i]["FLD_MAGIC4"],
					CanVoHuan = (int)dBToDataTable.Rows[i]["FLD_CanVoHuan"],
					FLD_DAYS = (int)dBToDataTable.Rows[i]["FLD_DAYS"],
					FLD_BD = (int)dBToDataTable.Rows[i]["FLD_BD"]
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu cửa hàng vật phẩm hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}

	public void SetCheTaoVatPham()
	{
		// var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  CheTacVatPhamDanhSach  ORDER  BY  VatPham_ID", "PublicDb");
		// if (dBToDataTable == null)
		// {
		// 	return;
		// }
		// if (dBToDataTable.Rows.Count == 0)
		// {
		// 	LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu chế tạo các vật phẩm----Không có dữ liệu vật phẩm được chế tạo");
		// 	return;
		// }
		// CheTacVatPhamDanhSach.Clear();
		// for (var i = 0; i < dBToDataTable.Rows.Count; i++)
		// {
		// 	X_Che_Tac_Vat_Pham_Loai x_Che_Tac_Vat_Pham_Loai = new();
		// 	try
		// 	{
		// 		x_Che_Tac_Vat_Pham_Loai.VatPham_ID = (int)dBToDataTable.Rows[i]["VatPham_ID"];
		// 		x_Che_Tac_Vat_Pham_Loai.VatPhamTen = dBToDataTable.Rows[i]["VatPhamTen"].ToString();
		// 		x_Che_Tac_Vat_Pham_Loai.VatPhamSoLuong = (int)dBToDataTable.Rows[i]["VatPhamSoLuong"];
		// 		x_Che_Tac_Vat_Pham_Loai.CheTaoLoaiHinh = (int)dBToDataTable.Rows[i]["CheTaoLoaiHinh"];
		// 		x_Che_Tac_Vat_Pham_Loai.CheTaoDangCap = (int)dBToDataTable.Rows[i]["CheTaoDangCap"];
		// 		var array = dBToDataTable.Rows[i]["CanVatPham"].ToString().Split('|');
		// 		x_Che_Tac_Vat_Pham_Loai.CanVatPham.Clear();
		// 		for (var j = 0; j < array.Length; j++)
		// 		{
		// 			var list = JsonConvert.DeserializeObject<List<X_Che_Tac_Can_Vat_Pham_Loai>>(array[0]);
		// 			foreach (var item in list)
		// 			{
		// 				x_Che_Tac_Vat_Pham_Loai.CanVatPham.Add(item);
		// 			}
		// 		}
		// 		if (!CheTacVatPhamDanhSach.ContainsKey(x_Che_Tac_Vat_Pham_Loai.VatPham_ID))
		// 		{
		// 			CheTacVatPhamDanhSach.Add(x_Che_Tac_Vat_Pham_Loai.VatPham_ID, x_Che_Tac_Vat_Pham_Loai);
		// 		}
		// 	}
		// 	catch (Exception ex)
		// 	{
		// 		LogHelper.WriteLine(LogLevel.Error, "Tải các vật phẩm chế tạo  lỗi | " + x_Che_Tac_Vat_Pham_Loai.VatPham_ID + "  |  " + ex.Message);
		// 	}
		// }
	}

	public static void LoadDuLieuThanhVienBangPhai()
	{
		var text = "";
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable($"SELECT * FROM TBL_XWWL_GuildMember", "GameServer");
			if (dBToDataTable == null)
			{
				return;
			}
			if (dBToDataTable.Rows.Count != 0)
			{
				GuildList.Clear();
				for (var i = 0; i < dBToDataTable.Rows.Count; i++)
				{
					GuildMember item = new()
					{
						ID = (int)dBToDataTable.Rows[i]["ID"],
						PlayerName = dBToDataTable.Rows[i]["FLD_NAME"].ToString(),
						GuildName = dBToDataTable.Rows[i]["G_Name"].ToString(),
						Guild_Score = long.Parse(dBToDataTable.Rows[i]["FLD_GuildPoint"].ToString()),
						Role = (int)dBToDataTable.Rows[i]["Leve"]
					};
					GuildList.Add(item.PlayerName, item);
					text = item.PlayerName;
				}
			}
			dBToDataTable.Dispose();
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi load thành viên Bang Phái tại num: ");
		}
	}
	/// <summary>
	/// Kiểm tra chủ guild hay không
	/// </summary>
	public static bool IsGuildMaster(Players player)
	{
		try
		{
			if (player == null || player.GuildId <= 0)
			{
				return false;
			}
			var member = GuildList[player.CharacterName];
			return member.PlayerName == player.CharacterName && member.Role == 6; // 6 là chủ guild
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "IsGuildMaster error " + ex.Message);
			return false;
		}

	}



	public void SetDrop()
	{
		try
		{
			var drops = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlDrops.ToList();
			if (drops.Count == 0)
			{
				Drop.Clear();
				LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu các vật phẩm bị rơi----Không có dữ liệu vật phẩm");
			}
			else
			{
				Drop.Clear();
				foreach (var item in drops)
				{
					DropClass dropClass = new();
					try
					{
						dropClass.FLD_LEVEL1 = item.FldLevel1;
						dropClass.FLD_LEVEL2 = item.FldLevel2;
						dropClass.FLD_PID = item.FldPid;
						dropClass.FLD_PIDNew = item.FldPid;
						dropClass.FLD_PP = item.FldPp;
						dropClass.FLD_NAME = item.FldName;
						dropClass.FLD_MAGIC0 = item.FldMagic0;
						dropClass.FLD_MAGIC1 = item.FldMagic1;
						dropClass.FLD_MAGIC2 = item.FldMagic2;
						dropClass.FLD_MAGIC3 = item.FldMagic3;
						dropClass.FLD_MAGIC4 = item.FldMagic4;
						dropClass.FLD_SoCapPhuHon = item.FldSoCapPhuHon;
						dropClass.FLD_TrungCapPhuHon = item.FldTrungCapPhuHon;
						dropClass.FLD_TienHoa = item.FldTienHoa;
						dropClass.FLD_KhoaLai = item.FldKhoaLai;
						dropClass.FLD_MAGICNew0 = item.FldMagic0;
						dropClass.FLD_MAGICNew1 = item.FldMagic1;
						dropClass.FLD_MAGICNew2 = item.FldMagic2;
						dropClass.FLD_MAGICNew3 = item.FldMagic3;
						dropClass.FLD_MAGICNew4 = item.FldMagic4;
						dropClass.CoMoThongBao = (int)item.CoMoThongBao;
						dropClass.FLD_DAYS = (int)item.FldDays;
						Drop.Add(dropClass);
					}
					catch (Exception ex)
					{
						LogHelper.WriteLine(LogLevel.Error, "Tải các vật phẩm bị rơi lỗi" + dropClass.FLD_NAME + "    " + ex.Message);
					}
				}
				LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu các vật phẩm bị rơi hoàn thành " + drops.Count);
			}
		}
		catch (Exception ex2)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu các vật phẩm bị rơi lỗi: " + ex2.Message);
		}
	}

	public static readonly Dictionary<long, ItemOptionClass> ListItemOption;


	public void SetItemOptionClass()
	{
		var context = HeroYulgang.Core.DatabaseManager.Instance.PublicDb;

		var itemsOption = context.TblItemOptions.ToList();
		ListItemOption.Clear();
		foreach (var item in itemsOption)
		{
			ListItemOption.Add(item.FldPid, new ItemOptionClass
			{
				ID = item.Id,
				FLD_PID = item.FldPid,
				FLD_NAME = item.FldName,
				Bonus_HP = item.BonusHp,
				Bonus_PercentHP = item.BonusPercentHp,
				Bonus_MP = item.BonusMp,
				Bonus_PercentMP = item.BonusPercentMp,
				Bonus_ATK = item.BonusAtk,
				Bonus_PercentATK = item.BonusPercentAtk,
				Bonus_DF = item.BonusDf,
				Bonus_PercentDF = item.BonusPercentDf,
				Bonus_PercentATKSkill = item.BonusPercentAtkskill,
				Bonus_DefSkill = item.BonusDefSkill,
				Bonus_Qigong = item.BonusQigong,
				Bonus_DropGold = item.BonusDropGold,
				Bonus_Exp = item.BonusExp,
				Bonus_Lucky = item.BonusLucky,
				Bonus_Accuracy = item.BonusAccuracy,
				Bonus_Evasion = item.BonusEvasion,
				Bonus_DiemHoangKim = item.BonusDiemHoangKim,
				Bonus_ATKMONSTER = item.BonusAtkmonster,
				Bonus_DEFMONSTER = item.BonusDefmonster
			});
		}
	}



	public void Set_DCHDrop()
	{
		var dchDrops = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlDropDches.ToList();
		if (dchDrops.Count == 0)
		{
			DCH_Drop.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải xong các vật phẩm do quái vật chủ đánh rơi ----Không có dữ liệu mặt hàng");
		}
		else
		{
			DCH_Drop.Clear();
			foreach (var item in dchDrops)
			{
				DropClass dropClass = new();
				try
				{
					dropClass.FLD_LEVEL1 = item.FldLevel1;
					dropClass.FLD_LEVEL2 = item.FldLevel2;
					dropClass.FLD_PID = item.FldPid;
					dropClass.FLD_PP = item.FldPp;
					dropClass.FLD_PIDNew = item.FldPid;
					dropClass.FLD_NAME = item.FldName;
					dropClass.FLD_MAGIC0 = item.FldMagic0;
					dropClass.FLD_MAGIC1 = item.FldMagic1;
					dropClass.FLD_MAGIC2 = item.FldMagic2;
					dropClass.FLD_MAGIC3 = item.FldMagic3;
					dropClass.FLD_MAGIC4 = item.FldMagic4;
					dropClass.FLD_SoCapPhuHon = item.FldSoCapPhuHon;
					dropClass.FLD_TrungCapPhuHon = item.FldTrungCapPhuHon;
					dropClass.FLD_TienHoa = item.FldTienHoa;
					dropClass.FLD_KhoaLai = item.FldKhoaLai;
					dropClass.FLD_MAGICNew0 = item.FldMagic0;
					dropClass.FLD_MAGICNew1 = item.FldMagic1;
					dropClass.FLD_MAGICNew2 = item.FldMagic2;
					dropClass.FLD_MAGICNew3 = item.FldMagic3;
					dropClass.FLD_MAGICNew4 = item.FldMagic4;
					dropClass.CoMoThongBao = (int)item.CoMoThongBao;
					dropClass.FLD_DAYS = (int)item.FldDays;
					DCH_Drop.Add(dropClass);
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Tải các vật phẩm bị rơi lỗi" + dropClass.FLD_NAME + "    " + ex.Message);
				}
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu các vật phẩm do quái vật chủ đánh rơi hoàn thành " + dchDrops.Count);
		}
	}


	public void Set_GSDrop()
	{

		var gsDrops = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlDropGs.ToList();

		if (gsDrops.Count == 0)
		{
			Drop_GS.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải xong các vật phẩm do quái vật chủ đánh rơi ----Không có dữ liệu mặt hàng");
		}
		else
		{
			Drop_GS.Clear();
			foreach (var item in gsDrops)
			{
				DropClass dropClass = new();
				try
				{
					dropClass.FLD_LEVEL1 = item.FldLevel1;
					dropClass.FLD_LEVEL2 = item.FldLevel2;
					dropClass.FLD_PID = item.FldPid;
					dropClass.FLD_PP = item.FldPp;
					dropClass.FLD_PIDNew = item.FldPid;
					dropClass.FLD_NAME = item.FldName;
					dropClass.FLD_MAGIC0 = item.FldMagic0;
					dropClass.FLD_MAGIC1 = item.FldMagic1;
					dropClass.FLD_MAGIC2 = item.FldMagic2;
					dropClass.FLD_MAGIC3 = item.FldMagic3;
					dropClass.FLD_MAGIC4 = item.FldMagic4;
					dropClass.FLD_SoCapPhuHon = item.FldSoCapPhuHon;
					dropClass.FLD_TrungCapPhuHon = item.FldTrungCapPhuHon;
					dropClass.FLD_TienHoa = item.FldTienHoa;
					dropClass.FLD_KhoaLai = item.FldKhoaLai;
					dropClass.FLD_MAGICNew0 = item.FldMagic0;
					dropClass.FLD_MAGICNew1 = item.FldMagic1;
					dropClass.FLD_MAGICNew2 = item.FldMagic2;
					dropClass.FLD_MAGICNew3 = item.FldMagic3;
					dropClass.FLD_MAGICNew4 = item.FldMagic4;
					dropClass.CoMoThongBao = (int)item.CoMoThongBao;
					Drop_GS.Add(dropClass);
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Tải các vật phẩm bị rơi lỗi" + dropClass.FLD_NAME + "    " + ex.Message);
				}
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu vật phẩm thả quái vật chủ " + gsDrops.Count);
		}
	}


	public void SetBossDrop()
	{
		var bossDrops = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlBossdrops.ToList();
		if (bossDrops.Count == 0)
		{
			BossDrop.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu các vật phẩm do BOSS rơi----Không có dữ liệu mặt hàng");
		}
		else
		{
			BossDrop.Clear();
			foreach (var item in bossDrops)
			{
				DropClass dropClass = new();
				try
				{
					dropClass.FLD_LEVEL1 = item.FldLevel1;
					dropClass.FLD_LEVEL2 = item.FldLevel2;
					dropClass.FLD_PID = item.FldPid;
					dropClass.FLD_PIDNew = item.FldPid;
					dropClass.FLD_PP = item.FldPp;
					dropClass.FLD_NAME = item.FldName;
					dropClass.FLD_MAGIC0 = item.FldMagic0;
					dropClass.FLD_MAGIC1 = item.FldMagic1;
					dropClass.FLD_MAGIC2 = item.FldMagic2;
					dropClass.FLD_MAGIC3 = item.FldMagic3;
					dropClass.FLD_MAGIC4 = item.FldMagic4;
					dropClass.FLD_SoCapPhuHon = item.FldSoCapPhuHon;
					dropClass.FLD_TrungCapPhuHon = item.FldTrungCapPhuHon;
					dropClass.FLD_TienHoa = item.FldTienHoa;
					dropClass.FLD_KhoaLai = item.FldKhoaLai;
					dropClass.FLD_MAGICNew0 = item.FldMagic0;
					dropClass.FLD_MAGICNew1 = item.FldMagic1;
					dropClass.FLD_MAGICNew2 = item.FldMagic2;
					dropClass.FLD_MAGICNew3 = item.FldMagic3;
					dropClass.FLD_MAGICNew4 = item.FldMagic4;
					dropClass.CoMoThongBao = (int)item.CoMoThongBao;
					dropClass.FLD_DAYS = (int)item.FldDays;
					BossDrop.Add(dropClass);
				}
				catch (Exception ex)
				{
					LogHelper.WriteLine(LogLevel.Error, "Tải các vật phẩm bị rơi lỗi" + dropClass.FLD_NAME + "    " + ex.Message);
				}
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu các vật phẩm do BOSS rơi hoàn thành " + bossDrops.Count);
		}
	}


	public void SetOpen()
	{
		var openDrops = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlOpens.ToList();
		if (openDrops.Count == 0)
		{
			Open.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu mở rương vật phẩm----không có dữ liệu");
		}
		else
		{
			Open.Clear();
			foreach (var item in openDrops)
			{
				Open.Add(new()
				{
					FLD_PID = item.FldPid,
					FLD_PIDX = item.FldPidx,
					FLD_NUMBER = (int)item.FldNumber,
					FLD_MAGIC1 = (int)item.FldMagic1,
					FLD_MAGIC2 = (int)item.FldMagic2,
					FLD_MAGIC3 = (int)item.FldMagic3,
					FLD_MAGIC4 = (int)item.FldMagic4,
					FLD_MAGIC5 = (int)item.FldMagic5,
					FLD_ThucTinh = (int)item.FldFjThucTinh,
					FLD_TienHoa = (int)item.FldFjTienHoa,
					FLD_TrungCapPhuHon = (int)item.FldFjTrungCapPhuHon,
					FLD_BD = (int)item.FldBd,
					FLD_DAYS = (int)item.FldDays,
					CoMoThongBao = (int)item.CoMoThongBao,
					FLD_PP = item.FldPp,
					FLD_NAME = item.FldName,
					FLD_NAMEX = item.FldNamex,
					STT_Hop_Event = (int)item.SttHopEvent
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu mở rương vật phẩm hoàn thành " + openDrops.Count);
		}

	}

	public void VinhDuBangPhaiXepHang()
	{
		var vinhDuBangPhaiXepHangs = HeroYulgang.Core.DatabaseManager.Instance
		.GameDb
		.VinhDuBangPhaiXepHangs
		.Where(x => x.FldFq == PartitionNumber)
		.Take(10).OrderByDescending(x => x.FldRy).ToList();

		if (vinhDuBangPhaiXepHangs.Count == 0)
		{
			BangPhaiXepHangSoLieu.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu xếp hạng bang phái----Không có dữ liệu");
		}
		else
		{
			BangPhaiXepHangSoLieu.Clear();
			foreach (var i in vinhDuBangPhaiXepHangs)
			{
				X_Mon_Phai_Xep_Hang item = new()
				{
					BangPhaiBangPhaiTen = i.FldBp,
					BangPhaiTenNhanVat = i.FldName,
					BangPhaiChinhTa = i.FldZx,
					BangPhaiNgheNghiep = i.FldJob,
					BangPhaiChuyenChuc = i.FldJobLevel,
					BangPhaiNhanVat_DangCap = i.FldLevel,
					BangPhaiVinhDu_DiemSo = i.FldRy,
					BangPhaiPhanKhuID = i.FldFq
				};
				BangPhaiXepHangSoLieu.Add(item);
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu xếp hạng bang phái hoàn thành " + vinhDuBangPhaiXepHangs.Count);
		}
	}


	public void Set_Set()
	{
		var sets = HeroYulgang.Core.DatabaseManager.Instance.BbgDbContext.Itmeclsses.ToList();
		if (sets.Count == 0)
		{
			Set_SoLieu.Clear();
			LogHelper.WriteLine(LogLevel.Error, "Tải dữ liệu các mục đã đặt----Không có dữ liệu mục nào");
		}
		else
		{
			Set_SoLieu.Clear();
			foreach (var item in sets)
			{
				Set_SoLieu.Add(new()
				{
					ID = item.Id,
					Type = (int)item.FldType,
					Reside = (int)item.FldReside,
					name = item.FldName,
					sql = item.FldSql,
					Magic0 = (int)item.FldMagic0,
					Magic1 = (int)item.FldMagic1,
					Magic2 = (int)item.FldMagic2,
					Magic3 = (int)item.FldMagic3,
					Magic4 = (int)item.FldMagic4,
					Magic5 = (int)item.FldMagic5,
					NJ = (int)item.FldFjNj,
					DAYS = (int)item.FldDays,
					TienHoa = (int)item.FldFjTienHoa,
					ThucTinh = (int)item.FldFjThucTinh,
					TrungCapPhuHon = (int)item.FldFjTrungCapPhuHon,
					BD = (int)item.FldBd
				});
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu các mục đã đặt hoàn thành " + sets.Count);
		}
	}


	public void SetThangThienKhiCong()
	{
		var dBToDataTable = DBA.GetDBToDataTable($"SELECT  *  FROM  ThangThienKhiCong  ORDER  BY  KhiCongID", "PublicDb");
		if (dBToDataTable == null)
		{
			return;
		}
		if (dBToDataTable.Rows.Count == 0)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tải khí công thăng thiên hoàn thành----Không có dữ liệu khí công thăng thiên");
		}
		else
		{
			ThangThienKhiCongList.Clear();
			for (var i = 0; i < dBToDataTable.Rows.Count; i++)
			{
				X_Thang_Thien_Khi_Cong_Tong_Loai x_Thang_Thien_Khi_Cong_Tong_Loai = new();
				x_Thang_Thien_Khi_Cong_Tong_Loai.KhiCongID = (int)dBToDataTable.Rows[i]["KhiCongID"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.KhiCongTen = dBToDataTable.Rows[i]["KhiCongTen"].ToString();
				x_Thang_Thien_Khi_Cong_Tong_Loai.VatPham_ID = (int)dBToDataTable.Rows[i]["VatPham_ID"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep1 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep1"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep2 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep2"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep3 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep3"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep4 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep4"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep5 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep5"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep6 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep6"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep7 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep7"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep8 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep8"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep9 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep9"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep10 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep10"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep11 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep11"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep12 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep12"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.NhanVatNgheNghiep13 = (int)dBToDataTable.Rows[i]["NhanVatNgheNghiep13"];
				x_Thang_Thien_Khi_Cong_Tong_Loai.FLD_BonusRateValuePerPoint = (double)dBToDataTable.Rows[i]["FLD_BonusRateValuePerPoint"];
				ThangThienKhiCongList.Add(x_Thang_Thien_Khi_Cong_Tong_Loai.KhiCongID, x_Thang_Thien_Khi_Cong_Tong_Loai);
			}
			LogHelper.WriteLine(LogLevel.Info, "Tải dữ liệu khí công thăng thiên hoàn thành " + dBToDataTable.Rows.Count);
		}
		dBToDataTable.Dispose();
	}


	public static void MonsterTemplate()
	{

		var monsterTemplates = HeroYulgang.Core.DatabaseManager.Instance.PublicDb.TblXwwlMonsters.ToList();

		MonsterTemplateList = monsterTemplates.Select(m => new MonSterClss
		{
			FLD_PID = m.FldPid,
			FLD_AT = m.FldAt ?? 0,
			FLD_AUTO = m.FldAuto ?? 0,
			FLD_BOSS = m.FldBoss ?? 0,
			FLD_DF = m.FldDf ?? 0,
			Level = m.FldLevel,
			Name = m.FldName,
			Rxjh_Exp = m.FldExp ?? 0,
			Rxjh_HP = m.FldHp,
			FLD_NPC = m.FldNpc ?? 0,
			FLD_QUEST = m.FldQuest ?? 0,
			FLD_QUESTID = m.FldQuestid ?? 0,
			FLD_STAGES = m.FldStages ?? 0,
			FLD_QUESTITEM = m.FldQuestitem ?? 0,
			FLD_PP = m.FldPp ?? 0
		}).ToDictionary(m => m.FLD_PID);
		LogHelper.WriteLine(LogLevel.Info, "MonsterTemplate " + MonsterTemplateList.Count);
	}

	public static readonly Dictionary<(int, int), X_Toa_Do_Class> SpotMap = [];
	public static Dictionary<int, int> PortalCountPerMap = [];

	public static void MonsterSetBase()
	{
		try
		{
			var dBToDataTable = DBA.GetDBToDataTable("SELECT * FROM TBL_XWWL_MONSTER_SET_BASE", "PublicDb");
			if (dBToDataTable == null) return;
			if (dBToDataTable.Rows.Count == 0)
			{
				LogHelper.WriteLine(LogLevel.Info, "Quá trình tải dữ liệu quái vật Neo đã hoàn thành----Không có dữ liệu quái vật");
				return;
			}
			MapList.Clear();
			NpcList.Clear();
			SpotMap.Clear();
			foreach (DataRow item in dBToDataTable.Rows)
			{
				var npc = (int)item["FLD_PID"];
				//LogHelper.WriteLine(LogLevel.Info, "NPCID " + npc);
				int amount = (int)item["FLD_AMOUNT"];
				int aoe = (int)item["FLD_AOE"];
				int level = (int)item["FLD_LEVEL"];
				int map = (int)item["FLD_MID"];
				float baseX = Convert.ToSingle(item["FLD_X"]);
				float baseY = Convert.ToSingle(item["FLD_Y"]);
				if ((int)item["FLD_ACTIVE"] != 1)
				{
					continue;
				}

				if (npc < 10000)
				{
					LoadANpc((int)item["FLD_PID"], item["FLD_NAME"].ToString(), (int)item["FLD_MID"], float.Parse(item["FLD_FACE0"].ToString()), float.Parse(item["FLD_FACE"].ToString()), float.Parse(item["FLD_X"].ToString()), float.Parse(item["FLD_Y"].ToString()), float.Parse(item["FLD_Z"].ToString()));
				}
				else
				{
					LoadMonster(item);
					float density = (float)amount / (float)aoe;
					if (aoe > 0 && amount > 1 && density >= 0.3)
					{
						if (!PortalCountPerMap.TryGetValue(level, out var currentCount))
						{
							currentCount = 0;
						}

						PortalCountPerMap[level] = currentCount + 1;

						X_Toa_Do_Class coord = new X_Toa_Do_Class
						{
							Rxjh_Map = map,
							Rxjh_X = baseX,
							Rxjh_Y = baseY,
							Rxjh_name = map + "" + (currentCount + 1)
						};
						AddPortalToMap(level, currentCount + 1, coord);
					}

				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Load SetNpc error Neo " + ex.Message);
		}

	}

	public static void AddPortalToMap(int level, int portalId, X_Toa_Do_Class coords)
	{
		if (SpotMap.TryGetValue((level, portalId), out var portals))
		{
			portals = coords;
		}
		else
		{
			SpotMap.Add((level, portalId), coords);
		}
	}


	private static void LoadMonster(DataRow item)
	{
		try
		{

			float baseX = Convert.ToSingle(item["FLD_X"]);
			float baseY = Convert.ToSingle(item["FLD_Y"]);
			int amount = (int)item["FLD_AMOUNT"];
			int aoe = (int)item["FLD_AOE"];
			for (int i = 0; i < amount; i++)
			{

				float offsetX = RNG.Next(-aoe, aoe);
				float offsetY = RNG.Next(-aoe, aoe);
				float x = baseX + offsetX;
				float y = baseY + offsetY;
				float z = 15;  // Assuming Z is a constant value

				// Call LoadAMonster with all necessary parameters extracted and calculated from the DataRow
				LoadAMonster(
					Convert.ToInt32(item["FLD_INDEX"]),
					Convert.ToInt32(item["FLD_PID"]),
					item["FLD_NAME"].ToString(),
					Convert.ToInt32(item["FLD_LEVEL"]),
					Convert.ToInt32(item["FLD_EXP"]),
					Convert.ToInt32(item["FLD_GOLD"]),
					Convert.ToInt32(item["FLD_NPC"]),
					Convert.ToInt32(item["FLD_HP"]) * NPC_HP_AMP / 100,  // Adjusting HP with the amplifier
					Convert.ToInt32(item["FLD_AT"]) * NPC_ATK_AMP / 100,  // Adjusting Attack with the amplifier
					Convert.ToInt32(item["FLD_DF"]) * NPC_DEF_AMP / 100,  // Adjusting Defense with the amplifier
					Convert.ToInt32(item["FLD_Accuracy"]) * NPC_ACC_AMP / 100,  // Adjusting Accuracy with the amplifier
					Convert.ToInt32(item["FLD_Evasion"]) * NPC_EVA_AMP / 100,  // Adjusting Evasion with the amplifier
					Convert.ToInt32(item["FLD_AUTO"]),
					Convert.ToInt32(item["FLD_BOSS"]),
					Convert.ToInt32(item["FLD_QItemDrop"]),
					Convert.ToInt32(item["FLD_QDropPP"]),
					Convert.ToInt32(item["FLD_NEWTIME"]),
					Convert.ToInt32(item["FLD_MID"]),
					float.Parse(item["FLD_FACE0"].ToString()),  // Assuming FLD_FACE0 is stored as a string
					float.Parse(item["FLD_FACE"].ToString()),  // Assuming FLD_FACE is stored as a string
					x,
					y,
					z,
					Convert.ToInt32(item["FLD_FreeDrop"])
				);
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "LoadMonster error Neo " + ex.Message);
		}

	}



	public static void LoadAMonster(int index, int pid, string name, int level, int exp, int gold, int npc,
							 int maxhp, int at, int df, int acc, int eva, int auto, int boss,
							 int qdrop, int qdroppp, int newtime, int map, float face1, float face2,
							 float x, float y, float z, int drop, bool insert = false)
	{
		NpcClass npcClass = new NpcClass
		{
			NPC_SessionID = index,
			FLD_PID = pid,
			Name = name,
			Level = level,
			Rxjh_Exp = exp,
			Rxjh_Gold = gold,
			IsNpc = npc,

			//Max_Rxjh_HP = maxhp * World.NPC_HP_AMP / 100,
			//Rxjh_HP = maxhp * World.NPC_HP_AMP / 100, // Assuming the Rxjh_HP is initially set to Max_Rxjh_HP
			FLD_AT = at * NPC_ATK_AMP / 100,
			FLD_DF = df * NPC_DEF_AMP / 100,
			Rxjh_Accuracy = acc * NPC_ACC_AMP / 100,
			Rxjh_Evasion = eva * NPC_EVA_AMP / 100,
			FLD_AUTO = auto,
			FLD_BOSS = boss,
			FLD_QItemDrop = qdrop,
			FLD_QDropPP = qdroppp,
			FLD_NEWTIME = newtime,
			Rxjh_Map = map,
			FLD_FACE1 = face1,
			FLD_FACE2 = face2,
			Rxjh_X = x,
			Rxjh_Y = y,
			Rxjh_Z = z,
			Rxjh_cs_X = x, // Assuming current spawn x is initial x
			Rxjh_cs_Y = y, // Assuming current spawn y is initial y
			Rxjh_cs_Z = z, // Assuming current spawn z is initial z
			FLD_FreeDrop = drop
		};
		npcClass.SetMaxHP(maxhp * NPC_HP_AMP / 100);
		npcClass.SetHp(maxhp * NPC_HP_AMP / 100);

		// Assuming Map and NpcList are some collections defined elsewhere in your class
		if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
		{
			value2.AddNpcToMapClass(npcClass);
		}
		else
		{
			value2 = new MapClass
			{
				MapID = npcClass.Rxjh_Map
			};
			value2.AddNpcToMapClass(npcClass);
			MapList.Add(value2.MapID, value2);
		}

		if (npcClass.FLD_PID >= 10000)
		{
			if (!NpcList.ContainsKey(npcClass.FLD_PID))
				NpcList.Add(npcClass.FLD_PID, npcClass);
		}
		else if (npcClass.IsNpc == 1 && npcClass.FLD_PID != 5)
		{
			// Handle NPC actions
			HandleNpcActions(npcClass);
		}
		npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
	}
	private static void HandleNpcActions(NpcClass npc)
	{

		if (npc.AutomaticMove != null)
		{
			npc.AutomaticMove.Enabled = false;
			npc.AutomaticMove.Close();
			npc.AutomaticMove.Dispose();
			npc.AutomaticMove = null;
		}

		if (npc.AutomaticAttack != null)
		{
			npc.AutomaticAttack.Enabled = false;
			npc.AutomaticAttack.Close();
			npc.AutomaticAttack.Dispose();
			npc.AutomaticAttack = null;
		}
		if (npc.TuDongHoiSinh != null)
			npc.TuDongHoiSinh.Enabled = false;
	}

	public static void LoadANpc(int pid, string name, int map, float face, float face2, float x, float y, float z, bool insert = false)
	{
		NpcClass npcClass = new()
		{
			FLD_PID = pid
		};
		npcClass.Name = name;
		npcClass.Level = 0;
		npcClass.Rxjh_Exp = 0;
		npcClass.IsNpc = 1;
		npcClass.SetMaxHP(32000);
		npcClass.SetHp(32000);
		//npcClass.Max_Rxjh_HP = 32000;
		//npcClass.Rxjh_HP = 32000;
		npcClass.FLD_AT = 0.0;
		npcClass.FLD_DF = 0.0;
		npcClass.FLD_AUTO = 0;
		npcClass.FLD_BOSS = 0;
		npcClass.FLD_NEWTIME = 0;
		npcClass.Rxjh_Map = map;
		npcClass.FLD_FACE1 = face;
		npcClass.FLD_FACE2 = face2;
		npcClass.Rxjh_X = x;
		npcClass.Rxjh_Y = y;
		npcClass.Rxjh_Z = z;
		npcClass.Rxjh_cs_X = x;
		npcClass.Rxjh_cs_Y = y;
		npcClass.Rxjh_cs_Z = z;
		if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value3))
		{
			value3.AddNpcToMapClass(npcClass);
		}
		else
		{
			value3 = new MapClass
			{
				MapID = npcClass.Rxjh_Map
			};
			value3.AddNpcToMapClass(npcClass);
			MapList.Add(value3.MapID, value3);
		}

		if (npcClass.FLD_PID >= 10000)
			try
			{
				if (!NpcList.ContainsKey(npcClass.FLD_PID)) NpcList.Add(npcClass.FLD_PID, npcClass);
			}
			catch
			{
				return;
			}

		if (npcClass.IsNpc == 1 && npcClass.FLD_PID != 5)
		{

			npcClass.AutomaticMove.Enabled = false;
			npcClass.AutomaticMove.Close();
			npcClass.AutomaticMove.Dispose();
			npcClass.AutomaticMove = null;
			npcClass.AutomaticAttack.Enabled = false;
			npcClass.AutomaticAttack.Close();
			npcClass.AutomaticAttack.Dispose();
			npcClass.AutomaticAttack = null;
			if (npcClass.TuDongHoiSinh != null) npcClass.TuDongHoiSinh.Enabled = false;
		}
		if (insert)
		{
			npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
			DBA.ExeSqlCommand(
				$"INSERT  INTO  TBL_XWWL_MONSTER_SET_BASE(FLD_PID,FLD_X,FLD_Y,FLD_Z,FLD_FACE0,FLD_FACE,FLD_MID,FLD_NAME,FLD_HP,FLD_AT,FLD_DF,FLD_NPC,FLD_NEWTIME,FLD_LEVEL,FLD_EXP,FLD_AUTO,FLD_BOSS) VALUES ({npcClass.FLD_PID},{x},{y},{15},{face},{face2},{npcClass.Rxjh_Map},'{npcClass.Name}',{npcClass.Max_Rxjh_HP},{npcClass.FLD_AT},{npcClass.FLD_DF},{1},{0},{npcClass.Level},{npcClass.Rxjh_Exp},{npcClass.FLD_AUTO},{npcClass.FLD_BOSS})",
				"PublicDb").GetAwaiter().GetResult();
		}
	}


	public static void delBoss(int int_0, int int_1)
	{
		try
		{
			List<NpcClass> list = new();
			foreach (var value in MapClass.GetnpcTemplate(int_0).Values)
			{
				if (value.FLD_PID == int_1)
				{
					list.Add(value);
				}
			}
			if (list == null)
			{
				return;
			}
			foreach (var item in list)
			{
				item.GuiDuLieu_TuVong_MotLanCuaQuaiVat();
			}
			list.Clear();
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Xóa lỗi 22 [" + int_1 + "]error：" + ex);
		}
	}

	/// <summary>
	/// Xóa một client ra khỏi World list khi một nhân vật ngắt kết nối
	/// </summary>
	/// <param name="worldId">ID của client cần xóa</param>
	public static void delWorldId(int worldId)
	{
		try
		{
			// Kiểm tra xem client có tồn tại trong list không
			if (list.TryGetValue(worldId, out var client))
			{
				// Giải phóng SessionID nếu có player
				if (client.Player != null)
				{
					HeroYulgang.Core.Managers.SessionIdManager.Instance.ReleaseSessionId(client.Player.SessionID);
					LogHelper.WriteLine(LogLevel.Debug, $"Đã giải phóng SessionID {client.Player.SessionID}");
				}

				// Xóa client khỏi list
				list.Remove(worldId);
				LogHelper.WriteLine(LogLevel.Info, $"Đã xóa client có WorldId {worldId} khỏi World.list");

				// Nếu client có player, cũng xóa player khỏi allConnectedChars
				if (client.Player != null && allConnectedChars.ContainsKey(client.Player.SessionID))
				{
					allConnectedChars.Remove(client.Player.SessionID);
					LogHelper.WriteLine(LogLevel.Info, $"Đã xóa player có SessionID {client.Player.SessionID} khỏi World.allConnectedChars");
				}
			}
			else
			{
				LogHelper.WriteLine(LogLevel.Debug, $"Không tìm thấy client có WorldId {worldId} trong World.list");
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Lỗi khi xóa client có WorldId {worldId} khỏi World.list: {ex.Message}");
		}
	}


	public static void AddNpc_Boss(int int_0, float float_0, float float_1, int int_1)
	{
		var num = 0;
		try
		{
			num = 1;
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				num = 2;
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				var num2 = 0;
				var num3 = 0;
				if (int_1 == 101 && int_0 == 16278)
				{
					num2 = 169;
					num3 = 99999999;
				}
				else
				{
					num2 = value.Level;
					num3 = value.Rxjh_HP;
				}
				npcClass.Level = num2;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = num3;
				npcClass.Rxjh_HP = num3;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = ((int_1 != 101 || int_1 != 0) ? 1 : value.FLD_BOSS);
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				num = 3;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					num = 4;
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					num = 6;
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				num = 7;
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Boss - lỗi 11 - num:[" + num + "] - [" + int_0 + "] error：" + ex.ToString());
		}
	}

	public static void AddNpc_Boss_Tuan_Loc(int int_0, Players playe, float float_0, float float_1, int int_1)
	{
		var num = 0;
		try
		{
			num = 1;
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				num = 2;
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				var num2 = 0.0;
				num2 = playe.Player_Level * 0.01 * 100000.0;
				npcClass.Level = 159;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = (int)num2;
				npcClass.Rxjh_HP = (int)num2;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = ((int_1 != 101 || int_1 != 0) ? 1 : value.FLD_BOSS);
				npcClass.FLD_NEWTIME = 1000000;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				num = 3;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					num = 4;
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					num = 6;
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				num = 7;
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Boss - lỗi 11 - num:[" + num + "] - [" + int_0 + "] error：" + ex.ToString());
		}
	}

	public static void AddNpc_Boss_Quy_Trung(int int_0, float float_0, float float_1, int int_1)
	{
		try
		{
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = 1000000;
				npcClass.Rxjh_HP = 1000000;
				npcClass.FLD_AT = 1.0;
				npcClass.FLD_DF = 1000.0;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = ((int_1 != 101 || int_1 != 0) ? 1 : value.FLD_BOSS);
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Boss - lỗi 11 - [" + int_0 + "] error：" + ex.ToString());
		}
	}

	public static NpcClass AddNpc_CongThanhChien(int FLD_PID, float Rxjh_cs_X, float Rxjh_cs_Y, int Rxjh_Map, bool QuaiXuatHien_DuyNhatMotLan, int Change_New_Time)
	{
		try
		{
			if (MonsterTemplateList.TryGetValue(FLD_PID, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = Rxjh_cs_X;
				npcClass.Rxjh_Y = Rxjh_cs_Y;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = Rxjh_cs_X;
				npcClass.Rxjh_cs_Y = Rxjh_cs_Y;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = Rxjh_Map;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = value.FLD_BOSS;
				npcClass.QuaiXuatHien_DuyNhatMotLan = QuaiXuatHien_DuyNhatMotLan;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				npcClass.FLD_NEWTIME = Change_New_Time;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				return npcClass;
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Boss CTC - lỗi 22 [" + FLD_PID + "] Lỗi：[" + ex.ToString());
		}
		return null;
	}



	public static void AddNpc(int int_0, float float_0, float float_1, int int_1)
	{
		try
		{
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = 0f;
				npcClass.FLD_FACE2 = 0f;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = value.FLD_BOSS;
				npcClass.FLD_NEWTIME = 10;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				if (Event_Noel_Progress != 0 && npcClass.FLD_PID == 278)
				{
					NpcEvent_GiangSinh.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC - lỗi 33 [" + int_0 + "]error：" + ex);
		}
	}

	public static void AddNpc_Nhieu(int int_0, float float_0, float float_1, int int_1, int soluong)
	{
		try
		{
			for (var i = 0; i < soluong; i++)
			{
				var num = RNG.Next((int)float_0 - 100, (int)float_0 + 100);
				var num2 = RNG.Next((int)float_1 - 100, (int)float_1 + 100);
				if (MonsterTemplateList.TryGetValue(int_0, out var value))
				{
					NpcClass npcClass = new();
					npcClass.FLD_PID = value.FLD_PID;
					npcClass.Name = value.Name;
					npcClass.Level = value.Level;
					npcClass.Rxjh_Exp = value.Rxjh_Exp;
					npcClass.Rxjh_X = num;
					npcClass.Rxjh_Y = num2;
					npcClass.Rxjh_Z = 15f;
					npcClass.Rxjh_cs_X = num;
					npcClass.Rxjh_cs_Y = num2;
					npcClass.Rxjh_cs_Z = 15f;
					npcClass.Rxjh_Map = int_1;
					npcClass.IsNpc = 0;
					npcClass.FLD_FACE1 = 0f;
					npcClass.FLD_FACE2 = 0f;
					npcClass.Max_Rxjh_HP = value.Rxjh_HP;
					npcClass.Rxjh_HP = value.Rxjh_HP;
					npcClass.FLD_AT = value.FLD_AT;
					npcClass.FLD_DF = value.FLD_DF;
					npcClass.FLD_AUTO = value.FLD_AUTO;
					npcClass.FLD_BOSS = 0;
					npcClass.FLD_NEWTIME = 10;
					npcClass.QuaiXuatHien_DuyNhatMotLan = false;
					npcClass.timeNpc_HoiSinh = DateTime.MinValue;
					if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
					{
						value2.AddNpcToMapClass(npcClass);
					}
					else
					{
						MapClass mapClass = new();
						mapClass.MapID = npcClass.Rxjh_Map;
						mapClass.AddNpcToMapClass(npcClass);
						MapList.Add(mapClass.MapID, mapClass);
					}
					npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
					NpcEvent_GiangSinh.Add(npcClass.NPC_SessionID, npcClass);
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC số lượng - lỗi 44 [" + int_0 + "]error：" + ex);
		}
	}


	public static void Backup_Database()
	{
		try
		{
			var arg = DateTime.Now.ToString("hh") + "h";
			var arg2 = DateTime.Now.ToString("mm") + "p";
			if (!Directory.Exists("Backup_DB_bak"))
			{
				Directory.CreateDirectory("Backup_DB_bak");
			}
			var text = $"game_v24-{DateTime.Now:dd-MM}_{arg}{arg2}.bak";
			var disk = Environment.CurrentDirectory + "\\Backup_DB_bak\\" + text;
			RxjhClass.BackupDatabase("game_v24", disk);
			text = $"web_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.bak";
			disk = Environment.CurrentDirectory + "\\Backup_DB_bak\\" + text;
			RxjhClass.BackupDatabase("web_v24", disk);
			text = $"bbg_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.bak";
			disk = Environment.CurrentDirectory + "\\Backup_DB_bak\\" + text;
			RxjhClass.BackupDatabase("bbg_v24", disk);
			text = $"pub_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.bak";
			disk = Environment.CurrentDirectory + "\\Backup_DB_bak\\" + text;
			RxjhClass.BackupDatabase("pub_v24", disk);
			text = $"acc_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.bak";
			disk = Environment.CurrentDirectory + "\\Backup_DB_bak\\" + text;
			RxjhClass.BackupDatabase("acc_v24", disk);
			if (!Directory.Exists("Backup_DB_rar"))
			{
				Directory.CreateDirectory("Backup_DB_rar");
			}
			text = $"game_v24-{DateTime.Now:dd-MM}_{arg}{arg2}.rar";
			disk = Environment.CurrentDirectory + "\\Backup_DB_rar\\" + text;
			RxjhClass.BackupDatabase("game_v24", disk);
			text = $"web_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.rar";
			disk = Environment.CurrentDirectory + "\\Backup_DB_rar\\" + text;
			RxjhClass.BackupDatabase("web_v24", disk);
			text = $"bbg_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.rar";
			disk = Environment.CurrentDirectory + "\\Backup_DB_rar\\" + text;
			RxjhClass.BackupDatabase("bbg_v24", disk);
			text = $"pub_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.rar";
			disk = Environment.CurrentDirectory + "\\Backup_DB_rar\\" + text;
			RxjhClass.BackupDatabase("pub_v24", disk);
			text = $"acc_v24_{DateTime.Now:dd-MM}_{arg}{arg2}.rar";
			disk = Environment.CurrentDirectory + "\\Backup_DB_rar\\" + text;
			RxjhClass.BackupDatabase("acc_v24", disk);
			LogHelper.WriteLine(LogLevel.Error, "------------------- Backup DB thành công -------------------");
			LogHelper.WriteLine(LogLevel.Error, "Đã Backup Database Auto trong GSconfig thành công !!!");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Backup DB lỗi !!! - " + ex.ToString());
		}
	}

	public static void Send_Hoa_Hong_Thong_Bao(string msg)
	{
		foreach (var value in allConnectedChars.Values)
		{
			if (!value.Client.TreoMay)
			{
				value.Tang_Hoa_Thong_Bao(msg);
			}
		}
	}

	public static void TangThemThienMa_QuaiVat(int int_0, float float_0, float float_1, int int_1, int int_2)
	{
		try
		{
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				if (int_0 == 16431)
				{
					npcClass.FLD_FACE1 = -1f;
					npcClass.FLD_FACE2 = 0f;
				}
				else
				{
					npcClass.FLD_FACE1 = 0f;
					npcClass.FLD_FACE2 = 0f;
				}
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = int_2;
				npcClass.FLD_BOSS = value.FLD_BOSS;
				npcClass.QuaiXuatHien_DuyNhatMotLan = true;
				npcClass.timeNpc_HoiSinh = DateTime.MinValue;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Thien Ma - lỗi 55 [" + int_0 + "]error：" + ex);
		}
	}

	public static void ThienMaThanCungPhoTuongKichSat_TangThemQuaiVat()
	{
		ThienMaThanCungDaiMon_PhaiChangTuVong = 0;
		ThienMaThanCungDongMon_PhaiChangTuVong = 0;
		delNpc(42001, 16431);
		Thread.Sleep(500);
		delNpc(42001, 16430);
		Thread.Sleep(1000);
		TangThemThienMa_QuaiVat(16430, -430f, -393f, 42001, 0);
		Thread.Sleep(1000);
		TangThemThienMa_QuaiVat(16431, 57f, 470f, 42001, 0);
		Thread.Sleep(1000);
		TangThemThienMa_QuaiVat(16435, -435f, 523f, 42001, 0);
	}


	public static void SetNpc(int int_0, float float_0, float float_1, int int_1)
	{
		try
		{
			float num = RNG.Next(-1, 1);
			float num2 = RNG.Next(-1, 1);
			if (MonsterTemplateList.TryGetValue(int_0, out var value))
			{
				NpcClass npcClass = new();
				npcClass.FLD_PID = value.FLD_PID;
				npcClass.Name = value.Name;
				npcClass.Level = value.Level;
				npcClass.Rxjh_Exp = value.Rxjh_Exp;
				npcClass.Rxjh_X = float_0;
				npcClass.Rxjh_Y = float_1;
				npcClass.Rxjh_Z = 15f;
				npcClass.Rxjh_cs_X = float_0;
				npcClass.Rxjh_cs_Y = float_1;
				npcClass.Rxjh_cs_Z = 15f;
				npcClass.Rxjh_Map = int_1;
				npcClass.IsNpc = 0;
				npcClass.FLD_FACE1 = num;
				npcClass.FLD_FACE2 = num2;
				npcClass.Max_Rxjh_HP = value.Rxjh_HP;
				npcClass.Rxjh_HP = value.Rxjh_HP;
				npcClass.FLD_AT = value.FLD_AT;
				npcClass.FLD_DF = value.FLD_DF;
				npcClass.FLD_AUTO = value.FLD_AUTO;
				npcClass.FLD_BOSS = value.FLD_BOSS;
				npcClass.FLD_NEWTIME = 8;
				if (MapList.TryGetValue(npcClass.Rxjh_Map, out var value2))
				{
					value2.AddNpcToMapClass(npcClass);
				}
				else
				{
					MapClass mapClass = new();
					mapClass.MapID = npcClass.Rxjh_Map;
					mapClass.AddNpcToMapClass(npcClass);
					MapList.Add(mapClass.MapID, mapClass);
				}
				npcClass.GetRange_PlayersSendIncreaseQuantityPackage();
				var string_ = "select max(FLD_INDEX) from TBL_XWWL_NPC";
				var num3 = DBA.ExeSqlCommand(string_, "PublicDb").GetAwaiter().GetResult();
				if (num3 > 0)
				{
					DBA.ExeSqlCommand($"INSERT  INTO  TBL_XWWL_NPC(FLD_PID,FLD_X,FLD_Y,FLD_Z,FLD_FACE0,FLD_FACE,FLD_MID,FLD_NAME,FLD_HP,FLD_AT,FLD_DF,FLD_NPC,FLD_NEWTIME,FLD_LEVEL,FLD_EXP,FLD_AUTO,FLD_BOSS,FLD_INDEX)VALUES  ({npcClass.FLD_PID},{float_0},{float_1},{15},{num},{num2},{npcClass.Rxjh_Map},'{npcClass.Name}',{npcClass.Max_Rxjh_HP},{npcClass.FLD_AT},{npcClass.FLD_DF},{0},{10},{npcClass.Level},{npcClass.Rxjh_Exp},{npcClass.FLD_AUTO},{npcClass.FLD_BOSS}, {num3 + 1})", "PublicDb").GetAwaiter().GetResult();
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Add NPC Ser - lỗi 66 [" + int_0 + "]error：" + ex);
		}
	}

	public static bool InConnectList(int int_0)
	{
		try
		{
			if (list.TryGetValue(int_0, out var _))
			{
				return true;
			}
		}
		catch
		{
		}
		return false;
	}

	public static void ProcessNpcMove()
	{
		try
		{
			foreach (var value in MapList.Values)
			{
				foreach (var value2 in value.npcTemplate.Values)
				{
					if (value2.IsNpc == 1 && value2.FLD_PID != 5)
					{
						continue;
					}
					if (value2._playlist.Count == 0)
					{
						if (value2.AutomaticMove.Enabled)
						{
							value2.AutomaticMove.Enabled = false;
						}
						if (value2.AutomaticAttack.Enabled)
						{
							value2.AutomaticAttack.Enabled = false;
						}
					}
					else if (!value2.NPCDeath && !value2.AutomaticMove.Enabled && !value2.AutomaticAttack.Enabled)
					{
						value2.AutomaticMove.Enabled = true;
					}
				}
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Process Npc Move() error " + ex);
		}
	}

	// public static void ProcessSqlQueue()
	// {
	// 	try
	// 	{
	// 		while (SqlPool.Count > 0)
	// 		{
	// 			var dbPoolClass = (DbPoolClass)SqlPool.Dequeue();
	// 			try
	// 			{
	// 				if (DbPoolClass.DbPoolClassRun(dbPoolClass.Conn, dbPoolClass.Sql, dbPoolClass.Prams, dbPoolClass.Type) == -1)
	// 				{
	// 					SqlPool.Enqueue(dbPoolClass);
	// 					LogHelper.WriteLine(LogLevel.Error, "Xử lý hàng đợi Sql 2 SoLieu Kết nối thư viện error " + SqlPool.Count);
	// 					Thread.Sleep(500);
	// 				}
	// 			}
	// 			catch (Exception ex)
	// 			{
	// 				LogHelper.WriteLine(LogLevel.Error, "Xử lý hàng đợi Sql 1 error - " + ex.ToString());
	// 				Thread.Sleep(1);
	// 			}
	// 		}
	// 	}
	// 	catch (Exception ex2)
	// 	{
	// 		LogHelper.WriteLine(LogLevel.Error, "Process Sql Queue() error - " + ex2.ToString());
	// 	}
	// }

	public static string pWord(string string_0, int int_0)
	{
		if (string_0 == null)
		{
			return null;
		}
		if (string_0.Equals(string.Empty))
		{
			return string.Empty;
		}
		var text = string.Empty;
		if (int_0 == 2)
		{
			var length = string_0.Length;
			text = string.Empty;
			var num = Convert.ToInt32(string_0.ToCharArray(0, 1)[0]) % 10;
			for (var i = 2; i < length; i += 2)
			{
				var num2 = Convert.ToInt32(string_0.ToCharArray(i, 1)[0]);
				var text2 = ((Convert.ToInt32(string_0.ToCharArray(i - 1, 1)[0]) % 2 != 0) ? ((char)(num2 - (i - 1) / 2 - num)).ToString() : ((char)(num2 + (i - 1) / 2 + num)).ToString());
				var text3 = text2;
				text += text3;
			}
		}
		return text;
	}

	public static int GetRandomSeed()
	{
		var array = new byte[4];
		RandomNumberGenerator.Fill(array);
		return BitConverter.ToInt32(array, 0);
	}

	public static int GetValue(int int_0, int int_1)
	{
		var result = 0;
		try
		{
			if (int_0 <= 800000002)
			{
				if (int_0 != 800000001)
				{
					if (int_0 != 800000002)
					{
						return result;
					}
					goto IL_31e6;
				}
				goto IL_2ec2;
			}
			switch (int_0)
			{
				case 800000062:
					{
						var num14 = RNG.Next(0, 110);
						var num15 = 1;
						var text12 = ((int_1 != 2) ? ((num14 >= 0 && num14 < 40) ? "2" : ((num14 >= 40 && num14 < 50) ? "3" : ((num14 >= 50 && num14 < 60) ? "4" : ((num14 < 60 || num14 >= 70) ? "11" : "6")))) : ((num14 < 0 || num14 >= 40) ? "11" : "2"));
						switch (int_1)
						{
							case 1:
								switch (text12)
								{
									case "11":
										num15 = RNG.Next(50, 90);
										if (num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "6":
										num15 = RNG.Next(2, 8);
										break;
									case "4":
										num15 = RNG.Next(34, 70);
										if (num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "3":
										num15 = RNG.Next(60, 90);
										if (num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "2":
										num15 = RNG.Next(30, 35);
										break;
								}
								break;
							case 2:
								{
									var text13 = text12;
									var text14 = text13;
									if (!(text14 == "11"))
									{
										if (text14 == "2")
										{
											num15 = RNG.Next(11, 15);
										}
										break;
									}
									num15 = RNG.Next(80, 100);
									if (num15 >= 80 && num15 < 83)
									{
										num15 = 80;
									}
									else if (num15 >= 83 && num15 < 86)
									{
										num15 = 80;
									}
									else if (num15 >= 86 && num15 < 89)
									{
										num15 = 85;
									}
									else if (num15 >= 89 && num15 < 92)
									{
										num15 = 85;
									}
									else if (num15 >= 92 && num15 < 95)
									{
										num15 = 90;
									}
									else if (num15 >= 95 && num15 < 98)
									{
										num15 = 95;
									}
									else if (num15 >= 98 && num15 <= 100)
									{
										num15 = 100;
									}
									if (num15 != 85 && num15 != 95 && num15 % 2 != 0)
									{
										num15--;
									}
									break;
								}
							case 3:
							case 4:
							case 5:
							case 6:
								switch (text12)
								{
									case "11":
										num15 = RNG.Next(76, 100);
										if (num15 > 80 && num15 <= 83)
										{
											num15 = 80;
										}
										else if (num15 > 83 && num15 <= 86)
										{
											num15 = 80;
										}
										else if (num15 > 86 && num15 <= 89)
										{
											num15 = 85;
										}
										else if (num15 > 89 && num15 <= 92)
										{
											num15 = 90;
										}
										else if (num15 > 92 && num15 <= 95)
										{
											num15 = 90;
										}
										else if (num15 > 95 && num15 <= 98)
										{
											num15 = 95;
										}
										else if (num15 > 98 && num15 <= 100)
										{
											num15 = 95;
										}
										if (num15 != 85 && num15 != 95 && num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "6":
										num15 = RNG.Next(3, 10);
										break;
									case "4":
										num15 = RNG.Next(35, 50);
										if (num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "3":
										num15 = RNG.Next(50, 80);
										if (num15 % 2 != 0)
										{
											num15--;
										}
										break;
									case "2":
										num15 = RNG.Next(10, 14);
										break;
								}
								break;
						}
						return int.Parse(text12) * 100000 + num15;
					}
				case 1000001651:
					{
						var num3 = RNG.Next(0, 110);
						var num4 = 1;
						var text4 = ((int_1 != 2) ? ((num3 >= 0 && num3 < 40) ? "2" : ((num3 >= 40 && num3 < 50) ? "3" : ((num3 >= 50 && num3 < 60) ? "4" : ((num3 < 60 || num3 >= 70) ? "11" : "6")))) : ((num3 < 0 || num3 >= 40) ? "11" : "2"));
						switch (int_1)
						{
							case 1:
								switch (text4)
								{
									case "11":
										num4 = RNG.Next(50, 90);
										if (num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "6":
										num4 = RNG.Next(10, 20);
										break;
									case "4":
										num4 = RNG.Next(34, 70);
										if (num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "3":
										num4 = RNG.Next(60, 90);
										if (num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "2":
										num4 = RNG.Next(30, 35);
										break;
								}
								break;
							case 2:
								{
									var text5 = text4;
									var text6 = text5;
									if (!(text6 == "11"))
									{
										if (text6 == "2")
										{
											num4 = RNG.Next(11, 15);
										}
										break;
									}
									num4 = RNG.Next(80, 100);
									if (num4 >= 80 && num4 < 83)
									{
										num4 = 80;
									}
									else if (num4 >= 83 && num4 < 86)
									{
										num4 = 80;
									}
									else if (num4 >= 86 && num4 < 89)
									{
										num4 = 85;
									}
									else if (num4 >= 89 && num4 < 92)
									{
										num4 = 85;
									}
									else if (num4 >= 92 && num4 < 95)
									{
										num4 = 90;
									}
									else if (num4 >= 95 && num4 < 98)
									{
										num4 = 95;
									}
									else if (num4 >= 98 && num4 <= 100)
									{
										num4 = 100;
									}
									if (num4 != 85 && num4 != 95 && num4 % 2 != 0)
									{
										num4--;
									}
									break;
								}
							case 3:
							case 4:
							case 5:
							case 6:
								switch (text4)
								{
									case "11":
										num4 = RNG.Next(76, 100);
										if (num4 > 80 && num4 <= 83)
										{
											num4 = 80;
										}
										else if (num4 > 83 && num4 <= 86)
										{
											num4 = 80;
										}
										else if (num4 > 86 && num4 <= 89)
										{
											num4 = 85;
										}
										else if (num4 > 89 && num4 <= 92)
										{
											num4 = 90;
										}
										else if (num4 > 92 && num4 <= 95)
										{
											num4 = 90;
										}
										else if (num4 > 95 && num4 <= 98)
										{
											num4 = 95;
										}
										else if (num4 > 98 && num4 <= 100)
										{
											num4 = 95;
										}
										if (num4 != 85 && num4 != 95 && num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "6":
										num4 = RNG.Next(3, 10);
										break;
									case "4":
										num4 = RNG.Next(35, 50);
										if (num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "3":
										num4 = RNG.Next(50, 80);
										if (num4 % 2 != 0)
										{
											num4--;
										}
										break;
									case "2":
										num4 = RNG.Next(10, 14);
										break;
								}
								break;
						}
						return int.Parse(text4) * 100000 + num4;
					}
				case 800000061:
					{
						var num16 = RNG.Next(0, 120);
						var num17 = 1;
						var text15 = ((int_1 != 2) ? ((num16 >= 0 && num16 < 30) ? "1" : ((num16 >= 30 && num16 < 40) ? "3" : ((num16 >= 40 && num16 < 45) ? "4" : ((num16 >= 45 && num16 < 50) ? "5" : ((num16 >= 50 && num16 < 110) ? "7" : ((num16 < 110 || num16 >= 115) ? "10" : "8")))))) : ((num16 >= 0 && num16 < 55) ? "1" : ((num16 < 55 || num16 >= 110) ? "8" : "7")));
						switch (int_1)
						{
							case 1:
								switch (text15)
								{
									case "4":
										{
											num17 = RNG.Next(40, 50);
											if (num17 % 2 != 0)
											{
												num17--;
											}

											break;
										}
									case "5":
										num17 = RNG.Next(10, 20);
										break;
									case "10":
									case "1":
										num17 = RNG.Next(30, 50);
										break;
									case "7":
										num17 = RNG.Next(30, 45);
										break;
									case "8":
										num17 = 1;
										break;
									case "3":
										{
											num17 = RNG.Next(50, 70);
											if (num17 % 2 != 0) num17--;
											break;
										}
								}

								break;
							case 2:
								num17 = text15 switch
								{
									"8" => 2,
									"7" => RNG.Next(30, 35),
									"1" => RNG.Next(20, 25),
									_ => num17
								};
								break;
							case 3:
							case 4:
							case 5:
							case 6:
								switch (text15)
								{
									case "4":
										{
											num17 = RNG.Next(25, 50);
											if (num17 % 2 != 0)
											{
												num17--;
											}

											break;
										}
									case "5":
										num17 = RNG.Next(5, 10);
										break;
									case "10":
										num17 = RNG.Next(15, 25);
										break;
									case "1":
										num17 = RNG.Next(15, 24);
										break;
									case "7":
										num17 = RNG.Next(15, 34);
										break;
									case "8":
										num17 = RNG.Next(1, 2);
										break;
									case "3":
										{
											num17 = RNG.Next(50, 80);
											if (num17 % 2 != 0)
											{
												num17--;
											}

											break;
										}
								}
								break;
						}
						return int.Parse(text15) * 100000 + num17;
					}
				case 1000001650:
					{
						var num12 = RNG.Next(0, 120);
						var num13 = 1;
						var text11 = ((int_1 != 2) ? ((num12 >= 0 && num12 < 30) ? "1" : ((num12 >= 30 && num12 < 40) ? "3" : ((num12 >= 40 && num12 < 45) ? "4" : ((num12 >= 45 && num12 < 50) ? "5" : ((num12 >= 50 && num12 < 110) ? "7" : ((num12 < 110 || num12 >= 115) ? "10" : "8")))))) : ((num12 >= 0 && num12 < 55) ? "1" : ((num12 < 55 || num12 >= 110) ? "8" : "7")));
						switch (int_1)
						{
							case 1:
								switch (text11)
								{
									case "4":
										{
											num13 = RNG.Next(40, 50);
											if (num13 % 2 != 0)
											{
												num13--;
											}

											break;
										}
									case "5":
										num13 = RNG.Next(10, 20);
										break;
									case "10":
									case "1":
										num13 = RNG.Next(30, 50);
										break;
									case "7":
										num13 = RNG.Next(30, 45);
										break;
									case "8":
										num13 = 1;
										break;
									case "3":
										{
											num13 = RNG.Next(50, 70);
											if (num13 % 2 != 0)
											{
												num13--;
											}

											break;
										}
								}

								break;
							case 2:
								num13 = text11 switch
								{
									"8" => 2,
									"7" => RNG.Next(30, 35),
									"1" => RNG.Next(20, 25),
									_ => num13
								};
								break;
							case 3:
							case 4:
							case 5:
							case 6:
								switch (text11)
								{
									case "4":
										{
											num13 = RNG.Next(25, 50);
											if (num13 % 2 != 0)
											{
												num13--;
											}

											break;
										}
									case "5":
										num13 = RNG.Next(5, 10);
										break;
									case "10":
										num13 = RNG.Next(15, 25);
										break;
									case "1":
										num13 = RNG.Next(15, 24);
										break;
									case "7":
										num13 = RNG.Next(15, 34);
										break;
									case "8":
										num13 = RNG.Next(1, 2);
										break;
									case "3":
										{
											num13 = RNG.Next(50, 80);
											if (num13 % 2 != 0)
											{
												num13--;
											}

											break;
										}
								}

								break;
						}
						return int.Parse(text11) * 100000 + num13;
					}
				case 1000001620:
					{
						var num5 = RNG.Next(0, 120);
						var num6 = 1;
						var text7 = ((int_1 != 2) ? ((num5 >= 0 && num5 < 30) ? "1" : ((num5 >= 30 && num5 < 40) ? "3" : ((num5 >= 40 && num5 < 45) ? "4" : ((num5 >= 45 && num5 < 50) ? "5" : ((num5 >= 50 && num5 < 110) ? "7" : ((num5 < 110 || num5 >= 115) ? "10" : "8")))))) : ((num5 >= 0 && num5 < 55) ? "1" : ((num5 < 55 || num5 >= 110) ? "8" : "7")));
						switch (int_1)
						{
							case 1:
								switch (text7)
								{
									case "4":
										{
											num6 = RNG.Next(40, 50);
											if (num6 % 2 != 0)
											{
												num6--;
											}

											break;
										}
									case "5":
										num6 = RNG.Next(10, 20);
										break;
									case "10":
									case "1":
										num6 = RNG.Next(30, 50);
										break;
									case "7":
										num6 = RNG.Next(30, 45);
										break;
									case "8":
										num6 = 1;
										break;
									case "3":
										{
											num6 = RNG.Next(50, 70);
											if (num6 % 2 != 0)
											{
												num6--;
											}

											break;
										}
								}

								break;

							case 2:
								num6 = text7 switch
								{
									"8" => 2,
									"7" => RNG.Next(30, 35),
									"1" => RNG.Next(20, 25),
									_ => num6
								};
								break;
							case 3:
							case 4:
							case 5:
							case 6:
								if (text7 == "4")
								{
									num6 = RNG.Next(25, 50);
									if (num6 % 2 != 0)
									{
										num6--;
									}
								}
								if (text7 == "5")
								{
									num6 = RNG.Next(5, 10);
								}
								if (text7 == "10")
								{
									num6 = RNG.Next(15, 25);
								}
								if (text7 == "1")
								{
									num6 = RNG.Next(15, 24);
								}
								if (text7 == "7")
								{
									num6 = RNG.Next(15, 34);
								}
								if (text7 == "8")
								{
									num6 = RNG.Next(1, 2);
								}
								if (text7 == "3")
								{
									num6 = RNG.Next(50, 80);
									if (num6 % 2 != 0)
									{
										num6--;
									}
								}
								break;
						}
						return int.Parse(text7) * 100000 + num6;
					}
				case 800000013:
					{
						var num9 = 1;
						var num10 = 8;
						var num11 = RNG.Next(0, 252);
						var s = string.Concat(str1: (new string[253]
						{
					"0100", "0110", "0120", "0130", "0140", "0150", "0160", "0170", "0180", "0190",
					"0200", "0210", "0220", "0230", "0240", "0250", "0260", "0270", "0280", "0290",
					"0300", "0310", "0320", "0330", "0340", "0350", "0360", "0370", "0380", "0390",
					"0400", "0410", "0420", "0430", "0440", "0450", "0460", "0470", "0480", "0490",
					"0500", "0510", "0520", "0530", "0540", "0550", "0560", "0570", "0580", "0590",
					"0700", "0710", "0720", "0730", "0740", "0750", "0760", "0770", "0780", "0790",
					"0800", "0810", "0820", "0830", "0840", "0850", "0860", "0870", "0880", "0890",
					"1100", "1200", "1300", "1400", "1500", "1700", "1800", "1810", "1820", "1830",
					"1840", "1850", "1860", "1870", "1880", "1890", "1900", "1910", "1920", "1930",
					"2500", "2510", "2520", "2530", "2540", "2550", "2560", "2570", "2580", "2590",
					"2600", "2700", "2710", "2720", "2730", "2740", "2750", "2760", "2770", "2780",
					"2790", "2800", "2810", "2820", "2830", "2840", "2850", "2860", "2870", "2880",
					"2890", "2900", "2910", "3100", "3110", "3120", "3130", "3140", "3150", "3160",
					"3180", "3200", "3210", "3220", "3230", "3240", "3250", "3260", "3270", "3300",
					"3310", "3320", "3330", "3340", "3400", "3410", "3420", "3430", "3440", "3500",
					"3510", "3520", "3530", "3540", "3700", "3710", "3720", "3730", "3740", "3800",
					"3810", "3820", "3830", "3840", "3850", "3860", "3870", "3900", "3910", "3920",
					"3930", "3940", "4500", "4510", "4520", "4530", "4540", "4550", "4560", "4570",
					"4580", "4590", "4600", "5500", "5510", "5520", "5530", "5540", "5550", "5560",
					"5570", "5580", "5590", "5600", "5610", "5620", "5630", "5640", "5650", "6000",
					"6010", "6020", "6030", "6040", "6100", "6110", "6120", "6130", "6140", "6150",
					"6160", "6500", "6510", "6530", "6540", "6560", "6570", "6580", "6590", "6600",
					"6610", "6620", "6630", "6640", "6650", "6660", "6670", "6680", "6690", "6700",
					"6710", "6720", "6730", "6740", "6750", "6760", "6770", "6780", "6790", "6800",
					"6810", "6820", "6830", "6840", "6850", "6860", "6870", "6880", "6890", "6900",
					"7000", "7010", "7020"
						})[num11], str0: num10.ToString(), str2: num9.ToString());
						return int.Parse(s);
					}
				case 800000023:
					{
						var num7 = RNG.Next(0, 120);
						var num8 = 1;
						var text8 = ((int_1 != 2) ? ((num7 >= 0 && num7 < 20) ? "1" : ((num7 >= 20 && num7 < 30) ? "3" : ((num7 >= 30 && num7 < 35) ? "4" : ((num7 >= 35 && num7 < 40) ? "5" : ((num7 >= 40 && num7 < 110) ? "7" : ((num7 < 110 || num7 >= 115) ? "10" : "8")))))) : ((num7 < 0 || num7 >= 30) ? "7" : "1"));
						switch (int_1)
						{
							case 1:
								switch (text8)
								{
									case "4":
										{
											num8 = RNG.Next(40, 50);
											if (num8 % 2 != 0)
											{
												num8--;
											}

											break;
										}
									case "5":
										num8 = RNG.Next(10, 20);
										break;
									case "10":
										num8 = RNG.Next(40, 50);
										break;
									case "1":
										num8 = RNG.Next(25, 30);
										break;
									case "7":
										num8 = RNG.Next(25, 31);
										break;
									case "8":
										num8 = 1;
										break;
									case "3":
										{
											num8 = RNG.Next(80, 90);
											if (num8 % 2 != 0)
											{
												num8--;
											}

											break;
										}
								}

								break;
							case 2:
								{
									var text9 = text8;
									var text10 = text9;
									if (!(text10 == "7"))
									{
										if (text10 == "1")
										{
											num8 = RNG.Next(30, 34);
										}
									}
									else
									{
										num8 = RNG.Next(30, 35);
									}
									break;
								}
							case 3:
							case 4:
							case 5:
							case 6:
								if (text8 == "4")
								{
									num8 = RNG.Next(15, 50);
									if (num8 % 2 != 0)
									{
										num8--;
									}
								}
								if (text8 == "5")
								{
									num8 = RNG.Next(5, 10);
								}
								if (text8 == "10")
								{
									num8 = RNG.Next(10, 25);
								}
								if (text8 == "1")
								{
									num8 = RNG.Next(13, 20);
								}
								if (text8 == "7")
								{
									num8 = RNG.Next(24, 31);
								}
								if (text8 == "8")
								{
									num8 = RNG.Next(1, 2);
								}
								if (text8 == "3")
								{
									num8 = RNG.Next(25, 80);
									if (num8 % 2 != 0)
									{
										num8--;
									}
								}
								break;
						}
						return int.Parse(text8) * 100000 + num8;
					}
				case 800000024:
					{
						var num = RNG.Next(0, 100);
						var num2 = 1;
						var text = ((int_1 != 2) ? ((num >= 0 && num < 45) ? "2" : ((num >= 45 && num < 50) ? "3" : ((num >= 50 && num < 55) ? "4" : ((num < 55 || num >= 60) ? "11" : "6")))) : ((num < 0 || num >= 50) ? "11" : "2"));
						switch (int_1)
						{
							case 1:
								switch (text)
								{
									case "11":
										num2 = RNG.Next(85, 90);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "6":
										num2 = RNG.Next(10, 20);
										break;
									case "4":
										num2 = RNG.Next(40, 70);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "3":
										num2 = RNG.Next(40, 70);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "2":
										num2 = RNG.Next(25, 35);
										break;
								}
								break;
							case 2:
								{
									var text2 = text;
									var text3 = text2;
									if (!(text3 == "11"))
									{
										if (text3 == "2")
										{
											num2 = RNG.Next(25, 30);
										}
										break;
									}
									num2 = RNG.Next(80, 90);
									if (num2 % 2 != 0)
									{
										num2--;
									}
									break;
								}
							case 3:
							case 4:
							case 5:
							case 6:
								switch (text)
								{
									case "11":
										num2 = RNG.Next(64, 80);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "6":
										num2 = RNG.Next(3, 10);
										break;
									case "4":
										num2 = RNG.Next(15, 30);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "3":
										num2 = RNG.Next(30, 50);
										if (num2 % 2 != 0)
										{
											num2--;
										}
										break;
									case "2":
										num2 = RNG.Next(30, 35);
										break;
								}
								break;
						}
						return int.Parse(text) * 100000 + num2;
					}
				case 800000025:
					break;
				case 800000026:
					goto IL_2cda;
				case 800000028:
					goto IL_2d4a;
				default:
					return result;
				case 800000014:
				case 800000015:
				case 800000016:
				case 800000017:
				case 800000018:
				case 800000019:
				case 800000020:
				case 800000021:
				case 800000022:
				case 800000027:
				case 800000029:
					return result;
				case 800000011:
					goto IL_2ec2;
				case 800000033:
				case 800000037:
					goto IL_3186;
				case 800000012:
					goto IL_31e6;
				case 800000031:
				case 800000035:
					goto IL_3472;
				case 800000032:
				case 800000036:
					goto IL_3615;
				case 800000030:
					goto IL_3675;
				case 800000034:
					goto IL_3d3a;
			}
			var num18 = RNG.Next(1, 100);
			if (num18 < 60)
			{
				return 1000000 + RNG.Next(1, 13);
			}
			if (num18 > 61 && num18 < 90)
			{
				return 1000000 + RNG.Next(14, 17);
			}
			return 1000000 + RNG.Next(18, 20);
		IL_31e6:
			var num19 = RNG.Next(0, 100);
			var num20 = 1;
			var text16 = ((num19 >= 0 && num19 < 75) ? "2" : ((num19 >= 75 && num19 < 80) ? "3" : ((num19 >= 80 && num19 < 85) ? "4" : ((num19 < 85 || num19 >= 90) ? "11" : "6"))));
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					switch (text16)
					{
						case "11":
							num20 = RNG.Next(35, 45);
							if (num20 % 2 != 0)
							{
								num20--;
							}
							break;
						case "6":
							num20 = RNG.Next(5, 10);
							break;
						case "4":
							num20 = RNG.Next(25, 50);
							if (num20 % 2 != 0)
							{
								num20--;
							}
							break;
						case "3":
							num20 = RNG.Next(25, 50);
							if (num20 % 2 != 0)
							{
								num20--;
							}
							break;
						case "2":
							num20 = RNG.Next(6, 10);
							break;
					}
					break;
				case 1:
					{
						var num21 = RNG.Next(1, 100);
						if (num21 < 60)
						{
							return 200000 + RNG.Next(3, 4);
						}
						if (num21 > 59 && num21 < 85)
						{
							return 200000 + RNG.Next(5, 6);
						}
						if (num21 > 84 && num21 < 95)
						{
							return 200000 + RNG.Next(7, 7);
						}
						return 200000 + RNG.Next(8, 8);
					}
			}
			return int.Parse(text16) * 100000 + num20;
		IL_2cda:
			var num22 = RNG.Next(1, 100);
			if (num22 < 60)
			{
				return 700000 + RNG.Next(15, 18);
			}
			if (num22 > 61 && num22 < 90)
			{
				return 700000 + RNG.Next(19, 22);
			}
			return 700000 + RNG.Next(23, 25);
		IL_3472:
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					{
						var num24 = RNG.Next(0, 100);
						return (num24 >= 0 && num24 < 20) ? RNG.Next(100005, 100025) : ((num24 >= 20 && num24 < 40) ? RNG.Next(700008, 700035) : ((num24 >= 40 && num24 < 60) ? RNG.Next(1000008, 1000025) : ((num24 < 60 || num24 >= 80) ? RNG.Next(1500001, 1500002) : RNG.Next(200001, 200020))));
					}
				case 1:
					{
						var num23 = RNG.Next(0, 100);
						return (num23 >= 0 && num23 < 20) ? RNG.Next(100005, 100015) : ((num23 >= 20 && num23 < 40) ? RNG.Next(700008, 700020) : ((num23 >= 40 && num23 < 60) ? RNG.Next(1000008, 1000020) : ((num23 < 60 || num23 >= 80) ? 1500001 : RNG.Next(200001, 200010))));
					}
				default:
					return result;
			}
		IL_2ec2:
			var num25 = RNG.Next(0, 120);
			var num26 = 1;
			var text17 = ((num25 >= 0 && num25 < 50) ? "1" : ((num25 >= 50 && num25 < 55) ? "3" : ((num25 >= 55 && num25 < 60) ? "4" : ((num25 >= 60 && num25 < 65) ? "5" : ((num25 >= 65 && num25 < 100) ? "7" : ((num25 < 100 || num25 >= 110) ? "10" : "8"))))));
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					if (text17 == "4")
					{
						num26 = RNG.Next(25, 36);
						if (num26 % 2 != 0)
						{
							num26--;
						}
					}
					if (text17 == "5")
					{
						num26 = RNG.Next(5, 10);
					}
					if (text17 == "10")
					{
						num26 = RNG.Next(10, 25);
					}
					if (text17 == "1")
					{
						num26 = RNG.Next(8, 15);
					}
					if (text17 == "7")
					{
						num26 = RNG.Next(20, 25);
					}
					if (text17 == "8")
					{
						num26 = RNG.Next(1, 2);
					}
					if (text17 == "3")
					{
						num26 = RNG.Next(20, 40);
						if (num26 % 2 != 0)
						{
							num26--;
						}
					}
					break;
				case 1:
					if (text17 != null)
					{
						var num27 = RNG.Next(1, 100);
						if (num27 < 60)
						{
							return 100000 + RNG.Next(1, 8);
						}
						if (num27 > 61 && num27 < 90)
						{
							return 100000 + RNG.Next(9, 13);
						}
						return 100000 + RNG.Next(14, 15);
					}
					break;
			}
			return int.Parse(text17) * 100000 + num26;
		IL_3675:
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					{
						var num37 = RNG.Next(0, 100);
						return (num37 >= 0 && num37 < 20) ? RNG.Next(100005, 100015) : ((num37 >= 20 && num37 < 40) ? RNG.Next(700008, 700025) : ((num37 >= 40 && num37 < 60) ? RNG.Next(1000008, 1000025) : ((num37 < 60 || num37 >= 80) ? 1500001 : RNG.Next(200001, 200020))));
					}
				case 1:
					{
						var num28 = RNG.Next(1, 140);
						if (num28 < 30)
						{
							var num29 = RNG.Next(101, 200);
							num28 = ((num29 > 100 && num29 <= 150) ? 100006 : ((num29 <= 150 || num29 > 175) ? 100010 : 100008));
						}
						else if (num28 >= 30 && num28 < 50)
						{
							var num30 = RNG.Next(101, 200);
							num28 = ((num30 > 100 && num30 <= 150) ? 200006 : ((num30 <= 150 || num30 > 175) ? 200010 : 200008));
						}
						else if (num28 == 50)
						{
							num28 = 700001;
						}
						else if (num28 >= 51 && num28 < 60)
						{
							var num31 = RNG.Next(101, 200);
							num28 = ((num31 > 100 && num31 <= 150) ? 300030 : ((num31 <= 150 || num31 > 175) ? 300050 : 300040));
						}
						else if (num28 >= 60 && num28 < 80)
						{
							var num32 = RNG.Next(1, 100);
							num28 = ((num32 <= 40) ? 500006 : ((num32 <= 40 || num32 > 80) ? 500010 : 500008));
						}
						else if (num28 >= 80 && num28 < 100)
						{
							var num33 = RNG.Next(1, 100);
							num28 = ((num33 <= 40) ? 600006 : ((num33 <= 40 || num33 > 80) ? 600010 : 600008));
						}
						else if (num28 == 100)
						{
							num28 = 700001;
						}
						else if (num28 >= 101 && num28 < 120)
						{
							var num34 = RNG.Next(101, 200);
							num28 = ((num34 > 100 && num34 <= 150) ? 1100030 : ((num34 <= 150 || num34 > 175) ? 1100050 : 1100040));
						}
						else if (num28 == 120)
						{
							var num35 = RNG.Next(1, 100);
							num28 = ((num35 < 60) ? 100008 : ((num35 <= 60 || num35 > 90) ? 1200001 : 100010));
						}
						else
						{
							var num36 = RNG.Next(1, 100);
							num28 = ((num36 < 40) ? 1000030 : ((num36 < 40 || num36 >= 80) ? 1000050 : 1000040));
						}
						return num28;
					}
				default:
					return result;
			}
		IL_3615:
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					return RNG.Next(200005, 200020);
				case 1:
					return RNG.Next(200001, 200005);
				default:
					return result;
			}
		IL_2d4a:
			var num38 = RNG.Next(1, 100);
			if (num38 < 25)
			{
				return int.Parse("200" + 3 + "000");
			}
			if (num38 >= 25 && num38 < 35)
			{
				return int.Parse("200" + 2 + "000");
			}
			if (num38 >= 35 && num38 < 55)
			{
				return int.Parse("200" + 5 + "000");
			}
			if (num38 >= 55 && num38 < 75)
			{
				return int.Parse("200" + 6 + "000");
			}
			if (num38 >= 75 && num38 < 94)
			{
				return int.Parse("200" + 4 + "000");
			}
			return int.Parse("200" + 1 + "000");
		IL_3d3a:
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					{
						var num40 = RNG.Next(0, 100);
						return (num40 >= 0 && num40 < 20) ? RNG.Next(100005, 100015) : ((num40 >= 20 && num40 < 40) ? RNG.Next(700008, 700025) : ((num40 >= 40 && num40 < 60) ? RNG.Next(1000008, 1000025) : ((num40 < 60 || num40 >= 80) ? 1500001 : RNG.Next(200001, 200020))));
					}
				case 1:
					{
						var num39 = RNG.Next(1, 140);
						return (num39 < 30) ? 100010 : ((num39 >= 30 && num39 < 50) ? 200010 : ((num39 >= 50 && num39 < 60) ? 300050 : ((num39 >= 60 && num39 < 80) ? 500010 : ((num39 >= 80 && num39 < 100) ? 600010 : ((num39 == 100) ? 700001 : ((num39 >= 101 && num39 < 120) ? 1100050 : ((num39 != 120) ? 1000050 : 1200001)))))));
					}
				default:
					return result;
			}
		IL_3186:
			switch (int_1)
			{
				case 2:
				case 3:
				case 4:
				case 5:
				case 6:
					return RNG.Next(200005, 200025);
				case 1:
					return RNG.Next(200001, 200005);
				default:
					return result;
			}
		}
		catch
		{
			return 1;
		}
	}

	public static int GetStoneValue_Fix(int value1, int value2, float lucky = 1f, int[] table = null)
	{
		if (table == null)
		{
			return GetStoneValue_Fix_OldVersion(value1, value2);
		}
		return GetStoneValue_Fix_NewVersion(value1, value2, lucky, table);
	}

	public static int GetStoneValue_Fix_OldVersion(int input_min, int input_max)
	{
		try
		{
			Random random = new(GetRandomSeed());
			var num = Math.Min(input_min, input_max);
			var num2 = Math.Max(input_min, input_max);
			if (num2 <= num + 1)
			{
				return num;
			}
			var array = new int[5] { 2, 4, 6, 8, 10 };
			double num3 = num2 - num;
			var array2 = new double[6];
			var num4 = 0;
			array2[num4] = num3 * 2.5 / 10.0;
			for (var i = 1; i < 5; i++)
			{
				array2[i] = array2[i - 1] + num3 * 2.5 / 10.0;
			}
			var num5 = random.Next(0, 11);
			int j;
			for (j = 0; num5 >= array[j]; j++)
			{
			}
			if (j != 0)
			{
				j--;
			}
			var num6 = random.Next(0, (int)array2[j] + 1);
			if (num + num6 >= num2)
			{
				return GetStoneValue_Fix_OldVersion(num, num2);
			}
			return num + num6;
		}
		catch
		{
			return Math.Min(input_min, input_max);
		}
	}

	public static int GetStoneValue_Fix_NewVersion(int min, int max, float lucky = 1f, int[] table = null)
	{
		try
		{
			table = table ?? new int[10] { 100, 90, 80, 70, 60, 50, 40, 30, 20, 10 };
			var num = min;
			var num2 = (max - min) * 1f / table.Length;
			var num3 = 0;
			for (var i = 0; i < table.Length; i++)
			{
				num3 += table[i];
			}
			num3 = (int)(new Random(GetRandomSeed()).Next(0, num3) * lucky);
			var num4 = 0;
			for (var j = 0; j < table.Length; j++)
			{
				num4 += table[j];
				if (num3 < num4)
				{
					num = min + (int)(num2 * j + new Random(GetRandomSeed()).Next(0, (int)(num2 + 1f)));
					break;
				}
			}
			if (num < min)
			{
				num = min;
			}
			if (num >= max)
			{
				num = max - 1;
			}
			return num;
		}
		catch
		{
			return Math.Min(min, max);
		}
	}

	public static int Check_VuKhi_Hero(long idItem)
	{
		if ((idItem >= 100204001 && idItem <= 100204010) || (idItem >= 100204026 && idItem <= 100204027) || (idItem >= 100204033 && idItem <= 100204034) || (idItem >= 100204035 && idItem <= 100204038) || (idItem >= 100204039 && idItem <= 100204042))
		{
			return 8;
		}
		if ((idItem >= 200204001 && idItem <= 200204010) || (idItem >= 200204026 && idItem <= 200204027) || (idItem >= 200204033 && idItem <= 200204034) || (idItem >= 200204035 && idItem <= 200204038) || (idItem >= 200204039 && idItem <= 200204042))
		{
			return 9;
		}
		if ((idItem >= 300204001 && idItem <= 300204010) || (idItem >= 300204026 && idItem <= 300204027) || (idItem >= 300204033 && idItem <= 300204034) || (idItem >= 300204035 && idItem <= 300204038) || (idItem >= 300204039 && idItem <= 300204042))
		{
			return 12;
		}
		if ((idItem >= 400204001 && idItem <= 400204010) || (idItem >= 400204026 && idItem <= 400204027) || (idItem >= 400204033 && idItem <= 400204034) || (idItem >= 400204035 && idItem <= 400204038) || (idItem >= 400204039 && idItem <= 400204042))
		{
			return 11;
		}
		if ((idItem >= 500204001 && idItem <= 500204010) || (idItem >= 500204026 && idItem <= 500204027) || (idItem >= 500204033 && idItem <= 500204034) || (idItem >= 500204035 && idItem <= 500204038) || (idItem >= 500204039 && idItem <= 500204042))
		{
			return 13;
		}
		return 0;
	}

	private void 发奖励()
	{
		try
		{
			伤害排行1 = 0;
			伤害排行2 = 0;
			伤害排行3 = 0;
			伤害排行4 = 0;
			伤害排行5 = 0;
			伤害排行6 = 0;
			伤害排行7 = 0;
			伤害排行8 = 0;
			伤害排行9 = 0;
			伤害排行10 = 0;
			伤害排行11 = 0;
			伤害排行12 = 0;
			伤害排行13 = 0;
			伤害排行14 = 0;
			伤害排行15 = 0;
			伤害排行16 = 0;
			伤害排行17 = 0;
			伤害排行18 = 0;
			伤害排行19 = 0;
			伤害排行20 = 0;
			账号排行1 = "";
			账号排行2 = "";
			账号排行3 = "";
			账号排行4 = "";
			账号排行5 = "";
			账号排行6 = "";
			账号排行7 = "";
			账号排行8 = "";
			账号排行9 = "";
			账号排行10 = "";
			账号排行11 = "";
			账号排行12 = "";
			账号排行13 = "";
			账号排行14 = "";
			账号排行15 = "";
			账号排行16 = "";
			账号排行17 = "";
			账号排行18 = "";
			账号排行19 = "";
			账号排行20 = "";
			var num = 0;
			foreach (var value in allConnectedChars.Values)
			{
				if (value.VucSau_BOSS_TinhGopTonThuong >= 1)
				{
					num++;
					value.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					value.CheckTheIngotPointData(5, 1);
					value.Save_NguyenBaoData();
					value.HeThongNhacNho("『Tham dự ban thưởng』→ 5 Điểm tích lũy ←!", 20, "Vực Sâu BOSS");
				}
			}
			foreach (var value2 in allConnectedChars.Values)
			{
				value2.是否排行 = 0;
			}
			if (num >= 1)
			{
				ToanCucNhacNho("BOSS", 6, " Năm nay Vực sâu BOSS Đại chiến số người tham gia: " + num + " Người");
				var num2 = 0;
				for (num2 = 0; num2 < 21; num2++)
				{
					if (num2 == 1)
					{
						foreach (var value3 in allConnectedChars.Values)
						{
							if (value3.VucSau_BOSS_TinhGopTonThuong >= 伤害排行1 && value3.VucSau_BOSS_TinhGopTonThuong > 0 && value3.是否排行 == 0)
							{
								伤害排行1 = value3.VucSau_BOSS_TinhGopTonThuong;
								账号排行1 = value3.AccountID;
							}
						}
						foreach (var value4 in allConnectedChars.Values)
						{
							if (账号排行1 == value4.AccountID)
							{
								value4.是否排行 = 1;
							}
						}
					}
					if (num2 == 2)
					{
						foreach (var value5 in allConnectedChars.Values)
						{
							if (value5.VucSau_BOSS_TinhGopTonThuong >= 伤害排行2 && value5.VucSau_BOSS_TinhGopTonThuong > 0 && value5.是否排行 == 0)
							{
								伤害排行2 = value5.VucSau_BOSS_TinhGopTonThuong;
								账号排行2 = value5.AccountID;
							}
						}
						foreach (var value6 in allConnectedChars.Values)
						{
							if (账号排行2 == value6.AccountID)
							{
								value6.是否排行 = 1;
							}
						}
					}
					if (num2 == 3)
					{
						foreach (var value7 in allConnectedChars.Values)
						{
							if (value7.VucSau_BOSS_TinhGopTonThuong >= 伤害排行3 && value7.VucSau_BOSS_TinhGopTonThuong > 0 && value7.是否排行 == 0)
							{
								伤害排行3 = value7.VucSau_BOSS_TinhGopTonThuong;
								账号排行3 = value7.AccountID;
							}
						}
						foreach (var value8 in allConnectedChars.Values)
						{
							if (账号排行3 == value8.AccountID)
							{
								value8.是否排行 = 1;
							}
						}
					}
					if (num2 == 4)
					{
						foreach (var value9 in allConnectedChars.Values)
						{
							if (value9.VucSau_BOSS_TinhGopTonThuong >= 伤害排行4 && value9.VucSau_BOSS_TinhGopTonThuong > 0 && value9.是否排行 == 0)
							{
								伤害排行4 = value9.VucSau_BOSS_TinhGopTonThuong;
								账号排行4 = value9.AccountID;
							}
						}
						foreach (var value10 in allConnectedChars.Values)
						{
							if (账号排行4 == value10.AccountID)
							{
								value10.是否排行 = 1;
							}
						}
					}
					if (num2 == 5)
					{
						foreach (var value11 in allConnectedChars.Values)
						{
							if (value11.VucSau_BOSS_TinhGopTonThuong >= 伤害排行5 && value11.VucSau_BOSS_TinhGopTonThuong > 0 && value11.是否排行 == 0)
							{
								伤害排行5 = value11.VucSau_BOSS_TinhGopTonThuong;
								账号排行5 = value11.AccountID;
							}
						}
						foreach (var value12 in allConnectedChars.Values)
						{
							if (账号排行5 == value12.AccountID)
							{
								value12.是否排行 = 1;
							}
						}
					}
					if (num2 == 6)
					{
						foreach (var value13 in allConnectedChars.Values)
						{
							if (value13.VucSau_BOSS_TinhGopTonThuong >= 伤害排行6 && value13.VucSau_BOSS_TinhGopTonThuong > 0 && value13.是否排行 == 0)
							{
								伤害排行6 = value13.VucSau_BOSS_TinhGopTonThuong;
								账号排行6 = value13.AccountID;
							}
						}
						foreach (var value14 in allConnectedChars.Values)
						{
							if (账号排行6 == value14.AccountID)
							{
								value14.是否排行 = 1;
							}
						}
					}
					if (num2 == 7)
					{
						foreach (var value15 in allConnectedChars.Values)
						{
							if (value15.VucSau_BOSS_TinhGopTonThuong >= 伤害排行7 && value15.VucSau_BOSS_TinhGopTonThuong > 0 && value15.是否排行 == 0)
							{
								伤害排行7 = value15.VucSau_BOSS_TinhGopTonThuong;
								账号排行7 = value15.AccountID;
							}
						}
						foreach (var value16 in allConnectedChars.Values)
						{
							if (账号排行7 == value16.AccountID)
							{
								value16.是否排行 = 1;
							}
						}
					}
					if (num2 == 8)
					{
						foreach (var value17 in allConnectedChars.Values)
						{
							if (value17.VucSau_BOSS_TinhGopTonThuong >= 伤害排行8 && value17.VucSau_BOSS_TinhGopTonThuong > 0 && value17.是否排行 == 0)
							{
								伤害排行8 = value17.VucSau_BOSS_TinhGopTonThuong;
								账号排行8 = value17.AccountID;
							}
						}
						foreach (var value18 in allConnectedChars.Values)
						{
							if (账号排行8 == value18.AccountID)
							{
								value18.是否排行 = 1;
							}
						}
					}
					if (num2 == 9)
					{
						foreach (var value19 in allConnectedChars.Values)
						{
							if (value19.VucSau_BOSS_TinhGopTonThuong >= 伤害排行9 && value19.VucSau_BOSS_TinhGopTonThuong > 0 && value19.是否排行 == 0)
							{
								伤害排行9 = value19.VucSau_BOSS_TinhGopTonThuong;
								账号排行9 = value19.AccountID;
							}
						}
						foreach (var value20 in allConnectedChars.Values)
						{
							if (账号排行9 == value20.AccountID)
							{
								value20.是否排行 = 1;
							}
						}
					}
					if (num2 != 10)
					{
						continue;
					}
					foreach (var value21 in allConnectedChars.Values)
					{
						if (value21.VucSau_BOSS_TinhGopTonThuong >= 伤害排行10 && value21.VucSau_BOSS_TinhGopTonThuong > 0 && value21.是否排行 == 0)
						{
							伤害排行10 = value21.VucSau_BOSS_TinhGopTonThuong;
							账号排行10 = value21.AccountID;
						}
					}
					foreach (var value22 in allConnectedChars.Values)
					{
						if (账号排行10 == value22.AccountID)
						{
							value22.是否排行 = 1;
						}
					}
				}
			}
			if (账号排行1.Length >= 1)
			{
				var players = FindPlayerByName(账号排行1);
				if (players != null)
				{
					players.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					players.CheckTheIngotPointData(50, 1);
					players.Save_NguyenBaoData();
					players.AddItem_ThuocTinh_int(**********, players.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players.CharacterName + "]赢得今日世界BOSS之战第一名，获得50积分及宝盒");
				}
			}
			if (账号排行2.Length >= 1)
			{
				var players2 = FindPlayerByName(账号排行2);
				if (players2 != null)
				{
					players2.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					players2.CheckTheIngotPointData(30, 1);
					players2.Save_NguyenBaoData();
					players2.AddItem_ThuocTinh_int(**********, players2.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players2.CharacterName + "]赢得今日世界BOSS之战第二名，获得30积分及宝盒");
				}
			}
			if (账号排行3.Length >= 1)
			{
				var players3 = FindPlayerByName(账号排行3);
				if (players3 != null)
				{
					players3.KiemTra_FLD_RXPIONT_and_FLD_RXPIONTX_CashShop();
					players3.CheckTheIngotPointData(20, 1);
					players3.Save_NguyenBaoData();
					players3.AddItem_ThuocTinh_int(**********, players3.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players3.CharacterName + "]赢得今日世界BOSS之战第三名，获得20积分及宝盒");
				}
			}
			if (账号排行4.Length >= 1)
			{
				var players4 = FindPlayerByName(账号排行4);
				if (players4 != null)
				{
					players4.AddItem_ThuocTinh_int(**********, players4.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players4.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行5.Length >= 1)
			{
				var players5 = FindPlayerByName(账号排行5);
				if (players5 != null)
				{
					players5.AddItem_ThuocTinh_int(**********, players5.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players5.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行6.Length >= 1)
			{
				var players6 = FindPlayerByName(账号排行6);
				if (players6 != null)
				{
					players6.AddItem_ThuocTinh_int(**********, players6.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players6.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行7.Length >= 1)
			{
				var players7 = FindPlayerByName(账号排行7);
				if (players7 != null)
				{
					players7.AddItem_ThuocTinh_int(**********, players7.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players7.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行8.Length >= 1)
			{
				var players8 = FindPlayerByName(账号排行8);
				if (players8 != null)
				{
					players8.AddItem_ThuocTinh_int(**********, players8.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players8.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行9.Length >= 1)
			{
				var players9 = FindPlayerByName(账号排行9);
				if (players9 != null)
				{
					players9.AddItem_ThuocTinh_int(**********, players9.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players9.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
			if (账号排行10.Length >= 1)
			{
				var players10 = FindPlayerByName(账号排行10);
				if (players10 != null)
				{
					players10.AddItem_ThuocTinh_int(**********, players10.GetParcelVacancyPosition(), 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					ToanCucNhacNho("Vực sâu BOSS", 6, "Chúc mừng người chơi[" + players10.CharacterName + "]Thắng được hôm nay thế giới BOSS Chi chiến thứ 4-10名，Thu hoạch được bảo hạp");
				}
			}
		}
		catch (Exception)
		{
			LogHelper.WriteLine(LogLevel.Error, "Phát thưởng lệ sai lầm ");
		}
	}

	public static void ThaoPhatPhanThuong()
	{
		try
		{
			讨伐伤害排行1 = 0;
			讨伐伤害排行2 = 0;
			讨伐伤害排行3 = 0;
			讨伐账号排行1 = "";
			讨伐账号排行2 = "";
			讨伐账号排行3 = "";
			var num = 0;
			foreach (var value in allConnectedChars.Values)
			{
				if (value.ThaoPhat_TinhGop_TonThuong >= 1)
				{
					num++;
				}
			}
			foreach (var value2 in allConnectedChars.Values)
			{
				value2.是否排行 = 0;
			}
			if (num >= 1)
			{
				ToanCucNhacNho("Thiên cơ các", 20, "Năm nay phó bản chiến số người tham gia: " + num + " Người");
				var num2 = 0;
				for (num2 = 0; num2 < 31; num2++)
				{
					if (num2 == 1)
					{
						foreach (var value3 in allConnectedChars.Values)
						{
							if (value3.TinhGop_TonThuong >= 讨伐伤害排行1 && value3.ThaoPhat_TinhGop_TonThuong > 0 && value3.是否排行 == 0)
							{
								讨伐伤害排行1 = value3.ThaoPhat_TinhGop_TonThuong;
								讨伐账号排行1 = value3.AccountID;
							}
						}
						foreach (var value4 in allConnectedChars.Values)
						{
							if (讨伐账号排行1 == value4.AccountID)
							{
								value4.是否排行 = 1;
							}
						}
					}
					if (num2 == 2)
					{
						foreach (var value5 in allConnectedChars.Values)
						{
							if (value5.ThaoPhat_TinhGop_TonThuong >= 讨伐伤害排行2 && value5.ThaoPhat_TinhGop_TonThuong > 0 && value5.是否排行 == 0)
							{
								讨伐伤害排行2 = value5.ThaoPhat_TinhGop_TonThuong;
								讨伐账号排行2 = value5.AccountID;
							}
						}
						foreach (var value6 in allConnectedChars.Values)
						{
							if (讨伐账号排行2 == value6.AccountID)
							{
								value6.是否排行 = 1;
							}
						}
					}
					if (num2 != 3)
					{
						continue;
					}
					foreach (var value7 in allConnectedChars.Values)
					{
						if (value7.TinhGop_TonThuong >= 讨伐伤害排行3 && value7.ThaoPhat_TinhGop_TonThuong > 0 && value7.是否排行 == 0)
						{
							讨伐伤害排行3 = value7.ThaoPhat_TinhGop_TonThuong;
							讨伐账号排行3 = value7.AccountID;
						}
					}
					foreach (var value8 in allConnectedChars.Values)
					{
						if (讨伐账号排行3 == value8.AccountID)
						{
							value8.是否排行 = 1;
						}
					}
				}
			}
			if (讨伐账号排行1.Length >= 1)
			{
				var players = FindPlayerByName(讨伐账号排行1);
				if (players != null)
				{
					var parcelVacancyPosition = players.GetParcelVacancyPosition();
					if (parcelVacancyPosition >= 0)
					{
						ToanCucNhacNho("Thiên cơ các", 20, " Chúc mừng người chơi [" + players.CharacterName + "] Thắng được kết thúc ván phó bản chi chiến hạng nhất");
						players.AddItem_ThuocTinh_int(**********, parcelVacancyPosition, 300, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
					}
					else
					{
						players.HeThongNhacNho("Không còn chỗ trống, lần này không thu hoạch được phần thưởng!", 10, "Thiên cơ các");
					}
				}
			}
			if (讨伐账号排行2.Length >= 1)
			{
				var players2 = FindPlayerByName(讨伐账号排行2);
				if (players2 != null)
				{
					var parcelVacancyPosition2 = players2.GetParcelVacancyPosition();
					if (parcelVacancyPosition2 >= 0)
					{
						players2.AddItem_ThuocTinh_int(**********, parcelVacancyPosition2, 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
						ToanCucNhacNho("Thiên cơ các", 20, " Chúc mừng người chơi [" + players2.CharacterName + "] Thắng được kết thúc ván phó bản chi chiến thứ hai");
					}
					else
					{
						players2.HeThongNhacNho("Không còn chỗ trống, lần này không thu hoạch được phần thưởng!", 10, "Thiên cơ các");
					}
				}
			}
			if (讨伐账号排行3.Length < 1)
			{
				return;
			}
			var players3 = FindPlayerByName(讨伐账号排行3);
			if (players3 != null)
			{
				var parcelVacancyPosition3 = players3.GetParcelVacancyPosition();
				if (parcelVacancyPosition3 >= 0)
				{
					ToanCucNhacNho("Thiên cơ các", 20, " Chúc mừng người chơi [" + players3.CharacterName + "]Thắng được kết thúc ván phó bản chi chiến tên thứ ba");
					players3.AddItem_ThuocTinh_int(**********, parcelVacancyPosition3, 100, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0);
				}
				else
				{
					players3.HeThongNhacNho("Không còn chỗ trống, lần này không thu hoạch được phần thưởng!", 10, "Thiên cơ các");
				}
			}
		}
		catch
		{
			LogHelper.WriteLine(LogLevel.Error, "Phát thưởng lệ sai lầm ");
		}
	}

	public static bool 是否讨伐副本危险区域(Players player)
	{
		if (player.NhanVatToaDo_BanDo == 43001 && player.NhanVatToaDo_X < 270f && player.NhanVatToaDo_X > -270f && player.NhanVatToaDo_Y < 110f && player.NhanVatToaDo_Y > -450f)
		{
			return true;
		}
		return false;
	}

	public static void 讨伐副本添加怪物()
	{
		Random random = new();
		var num = random.Next(-230, 200);
		var num2 = random.Next(-380, 60);
		Thread.Sleep(1000);
		AddNpc(16555, num, num2, 43001);
	}

	internal void StartTimer()
	{
		try
		{
			Task.Run(async () =>
			{
				while (true)
				{
					await Task.Delay(60000);
					AutoCheckingMailCod();
				}
			});

			Task.Run(() => ProcessAttackQueue());
			LogHelper.WriteLine(LogLevel.Info, "Start World Timer");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi StartTimer: " + ex.Message);
		}
	}

	public static readonly ConcurrentQueue<(byte[] Data, int Length, int Type)> _attackQueue = new();

	public static void SendMessageToAllUser(string message, int type, string prefix)
	{
		foreach (var player in allConnectedChars.Values)
		{
			player.HeThongNhacNho(message, type, prefix);
		}
	}

	public static int ThoiGian_GioiHan_PhysicalAttack;

	private async void ProcessAttackQueue()
	{
		while (true)
		{
			while (_attackQueue.TryDequeue(out var attack))
			{
				var (data, length, type) = attack;
				DateTime now = DateTime.UtcNow;

				foreach (var player in allConnectedChars.Values)
				{
					lock (player._attackLock)
					{
						if ((now - player._lastAttackTime).TotalMilliseconds >= ThoiGian_GioiHan_PhysicalAttack)
						{
							player._lastAttackTime = now; // Update the last attack time
							player.Attack(data, length, type); // Perform the attack
						}
						else
						{
							// Optionally, log skipped attacks
							LogHelper.WriteLine(LogLevel.Info, $"Attack skipped {player.CharacterName} - {(now - player._lastAttackTime).TotalMilliseconds}ms");
							player.HeThongNhacNho("CẢNH BÁO!! Bạn đang tấn công quá nhanh", 1, "Thiên cơ các");

						}
					}

				}
			}
			// Add a small delay to reduce CPU usage
			await Task.Delay(50);
		}
	}

	private void SetupWorldBoss()
	{
		try
		{
			// Đảm bảo WorldBossManager được khởi tạo đúng cách
			var manager = WorldBossManager.Instance;
			WorldBossEvent = new HeroWorldBossClass();

			LogHelper.WriteLine(LogLevel.Info, "Thiên cơ các Boss đã được khởi tạo trong World");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Lỗi khởi tạo WorldBossManager trong World: {ex.Message}");
		}
	}

	public static int Check_Upgrade_Item(int itemid, int job = 0)
	{
		var upgradeItemClass = List_UpgradeItem[itemid];
		foreach (var value2 in List_UpgradeItem.Values)
			if (upgradeItemClass.ItemType == value2.ItemType && value2.ItemLevel == upgradeItemClass.ItemLevel + 1 &&
				ItemList.TryGetValue(value2.ItemID, out var _))
				return value2.ItemID;
		return 0;
	}

	public void InitializeGroupQuestSystem()
	{
		GroupQuestEvent.Instance.LoadAllQuestDefinitions();
	}

	public static string FromBase64(string base64)
	{
		return Encoding.UTF8.GetString(Convert.FromBase64String(base64));
	}

	public static string Unitoccp1258(string nguon, bool revert = false)
	{
		var text = "ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỴỵỶỷỸỹ";
		var array = new string[134]
		{
				"AÌ", "Aì", "Â", "AÞ", "EÌ", "Eì", "Ê", "IÌ", "Iì", "OÌ",
				"Oì", "Ô", "OÞ", "UÌ", "Uì", "Yì", "aÌ", "aì", "â", "aÞ",
				"eÌ", "eì", "ê", "iÌ", "iì", "oÌ", "oì", "ô", "oÞ", "uÌ",
				"uì", "yì", "Ã", "ã", "Ð", "ð", "IÞ", "iÞ", "UÞ", "uÞ",
				"Õ", "õ", "Ý", "ý", "Aò", "aò", "AÒ", "aÒ", "Âì", "âì",
				"ÂÌ", "âÌ", "ÂÒ", "âÒ", "ÂÞ", "âÞ", "Âò", "âò", "Ãì", "ãì",
				"ÃÌ", "ãÌ", "ÃÒ", "ãÒ", "ÃÞ", "ãÞ", "Ãò", "ãò", "Eò", "eò",
				"EÒ", "eÒ", "EÞ", "eÞ", "Êì", "êì", "ÊÌ", "êÌ", "ÊÒ", "êÒ",
				"ÊÞ", "êÞ", "Êò", "êò", "IÒ", "iÒ", "Iò", "iò", "Oò", "oò",
				"OÒ", "oÒ", "Ôì", "ôì", "ÔÌ", "ôÌ", "ÔÒ", "ôÒ", "ÔÞ", "ôÞ",
				"Ôò", "ôò", "Õì", "õì", "ÕÌ", "õÌ", "ÕÒ", "õÒ", "ÕÞ", "õÞ",
				"Õò", "õò", "Uò", "uò", "UÒ", "uÒ", "Ýì", "ýì", "ÝÌ", "ýÌ",
				"ÝÒ", "ýÒ", "ÝÞ", "ýÞ", "Ýò", "ýò", "YÌ", "yÌ", "Yò", "yò",
				"YÒ", "yÒ", "YÞ", "yÞ"
		};
		if (!revert)
		{
			// Chuyển từ Unicode sang TCVN3 (logic hiện tại)
			var text2 = "";
			for (var i = 0; i < nguon.Length; i++)
			{
				var num = text.IndexOf(nguon[i]);
				text2 = (num < 0) ? (text2 + nguon[i]) : (text2 + array[num]);
			}
			return text2;
		}
		else
		{
			// Chuyển từ TCVN3 về Unicode
			var result = nguon;
			for (int i = 0; i < array.Length; i++)
			{
				// Thay thế từng chuỗi TCVN3 bằng ký tự Unicode tương ứng
				if (result.Contains(array[i]))
				{
					result = result.Replace(array[i], text[i].ToString());
				}
			}
			return result;
		}
	}

	internal static int GetNewPlayerID()
	{
		// Sử dụng SessionIdManager để cấp phát SessionID cho player
		return HeroYulgang.Core.Managers.SessionIdManager.Instance.AllocatePlayerSessionId();
	}

	/// <summary>
	/// Tạo SessionID duy nhất cho offline player
	/// Offline player sử dụng chung pool với online player
	/// </summary>
	/// <returns>SessionID duy nhất</returns>
	public static int GetNextOfflineSessionId()
	{
		// Offline player sử dụng chung pool với online player
		return HeroYulgang.Core.Managers.SessionIdManager.Instance.AllocatePlayerSessionId();
	}

	public class X_WebShop_Category
	{
		public int DISPLAYORDER;
		public int ID;
		public string NAME;
		public int PARENTID;
	}

	// Các phương thức từ Core.World
	public async Task<bool> StartAsync()
	{
		if (State != WorldState.Stopped)
		{
			Logger.Instance.Warning("Không thể khởi động World: đã đang chạy hoặc đang khởi động/dừng");
			return false;
		}

		try
		{
			State = WorldState.Starting;
			ServerStatus.Instance.SetStarting();
			Logger.Instance.Info("Đang khởi động World...");

			// Khởi động hệ thống Actor
			try
			{
				_actorSystemManager.Initialize();
				_actorSystemManager.TcpManagerActor.Tell(new StartServer());
			}
			catch (Exception ex)
			{
				Logger.Instance.Error($"Không thể khởi động hệ thống mạng: {ex.Message}");
				State = WorldState.Stopped;
				ServerStatus.Instance.SetOffline();
				return false;
			}

			// Kết nối đến LoginServer
			var loginServerConnected = await _loginServerClient.ConnectAsync();
			if (loginServerConnected)
			{
				Logger.Instance.Info("Đã kết nối thành công đến LoginServer 1");

				// Đăng ký GameServer với LoginServer
				var registered = await _loginServerClient.RegisterGameServerAsync();
				if (!registered)
				{
					Logger.Instance.Warning("Không thể đăng ký GameServer với LoginServer, nhưng vẫn tiếp tục khởi động");
				}

				// Bắt đầu lắng nghe tin nhắn từ LoginServer
				_loginServerClient.StartReceiveMessagesTask();
				Logger.Instance.Info("Đã bắt đầu lắng nghe tin nhắn từ LoginServer");
			}
			else
			{
				Logger.Instance.Warning("Không thể kết nối đến LoginServer, nhưng vẫn tiếp tục khởi động");
			}

			// Bắt đầu vòng lặp cập nhật
			_cancellationTokenSource = new CancellationTokenSource();
			_updateTask = Task.Run(() => UpdateLoopAsync(_cancellationTokenSource.Token));

			InitWorld();

			StartTime = DateTime.Now;
			State = WorldState.Running;
			ServerStatus.Instance.SetOnline();
			Logger.Instance.Info("World đã khởi động thành công");

			return true;
		}
		catch (Exception ex)
		{
			Logger.Instance.Error($"Lỗi khi khởi động World: {ex.Message}");
			State = WorldState.Stopped;
			ServerStatus.Instance.SetOffline();
			return false;
		}
	}

	private void InitWorld()
	{
		try
		{
			SetConfig();
			SetConfig2();
			LoadDuLieuThanhVienBangPhai();
            MonsterTemplate();
            MonsterSetBase();
			SetDrop();
			Set_GSDrop();
			Set_DCHDrop();
			SetItemOptionClass();
			SetBossDrop();
			SetOpen();
			Set_Set();
			setBbgCategory();
			SetBbgItem();
			SetLever();
			SetWxLever();
			SetKONGFU();
			SetItme();
			SetShop();
			SetMover();
			SetThongBao();
			SetDangCapBanThuong();
			SetVatPhamTraoDoi();
			SetMobile();
			SetCheDuocVatPham();
			SetKhuAnToan();
			SetKill();
			SetJianc();
			SetThangThienKhiCong();
			SetCheTaoVatPham();
			SetScript();
			SetNhiemVuSoLieuMoi();
			SetQG();
			SetDaThuocTinh();
			VinhDuBangPhaiXepHang();
			UpdateAllRankingData();
			SetGiftCode();
			SetGiftCodeRewards();
			SetUpgradeItem();
			conn = new();
			Timer.DelayCall(TimeSpan.FromMilliseconds(360000.0), TimeSpan.FromMilliseconds(360000.0), TuDong_ThongBaoEvent);
			Timer.DelayCall(TimeSpan.FromMilliseconds(360000.0), TimeSpan.FromMilliseconds(360000.0), AutomaticUpdateHonor);
			SuTuHong = new(21000.0);
			SuTuHong.Elapsed += SuTuHongEvent;
			SuTuHong.AutoReset = true;
			SuTuHong.Enabled = true;
			UpdateGuildRank = new(44280000.0);
			UpdateGuildRank.Elapsed += UpdateGuildRankEvent;
			UpdateGuildRank.AutoReset = true;
			UpdateGuildRank.Enabled = true;
			RxjhClass.SetUserHoldRefresh();
			InitializeGroupQuestSystem();
			//conn.SendCrossServerAction(0, "CROSS_SERVER_ZONE_REQUEST", 0, 0, 0, 0, 0);
			Logger.Instance.Info("World đã khởi động thành công");

		}
		catch (System.Exception Ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Lỗi khi khởi động World " + Ex.Message);
		}
	}
	public void TuDong_ThongBaoEvent()
	{

		try
		{
			if (allConnectedChars.Count > list.Count)
			{
				var queue = Queue.Synchronized(new());
				foreach (var value2 in allConnectedChars.Values)
				{
					queue.Enqueue(value2);
				}
				while (queue.Count > 0)
				{
					var players = (Players)queue.Dequeue();
					if (!list.TryGetValue(players.SessionID, out var _))
					{
						allConnectedChars.Remove(players.SessionID);
					}
				}
				if (allConnectedChars.Count > list.Count)
				{
					conn.Dispose();
					AutomaticArchive = 0;
				}
			}
			if (PhaiChangMoRa_CuuTuyenBanDo == 1.0)
			{
				foreach (var value3 in allConnectedChars.Values)
				{
					if (value3.NhanVatToaDo_BanDo >= 23001 && value3.NhanVatToaDo_BanDo <= 24000)
					{
						var array = Converter.HexStringToByte("AA5516002C01121708******************************558D55AA");
						Buffer.BlockCopy(BitConverter.GetBytes(VuongLong_GoldCoin), 0, array, 10, 8);
						if (value3.Client != null)
						{
							value3.Client.Send_Map_Data(array, array.Length);
						}
					}
				}
			}
			if (ItemRecord == 1)
			{
				DBA.ExeSqlCommand("DELETE  FROM  ItemRecord  WHERE DateDiff(dd,ThoiGian,getdate())>" + World.RecordKeepingDays).GetAwaiter().GetResult();
			}
			if (World.LoginRecord == 1)
			{
				DBA.ExeSqlCommand("DELETE  FROM  LoginRecord  WHERE            DateDiff(dd,ThoiGian,getdate())>" + World.RecordKeepingDays).GetAwaiter().GetResult();
			}
		}
		catch
		{
		}
	}
	public void AutomaticUpdateHonor()
	{

		UpdateAllRankingData();
		VinhDuBangPhaiXepHang();
		LogHelper.WriteLine(LogLevel.Info, "Làm mới dữ liệu xếp hạng danh dự đã hoàn thành");
	}

	private void SuTuHongEvent(object sender, ElapsedEventArgs e)
	{
		ProcessLionRoarQueue();
	}
	private void UpdateGuildRankEvent(object sender, ElapsedEventArgs e)
	{
		checkupdateguildLevel();
	}
	private System.Timers.Timer SuTuHong = new(21000.0);
	private System.Timers.Timer UpdateGuildRank = new(1800000.0);
	private System.Timers.Timer AutoConnect;

	public async Task<bool> StopAsync()
	{
		if (State != WorldState.Running)
		{
			Logger.Instance.Warning("Không thể dừng World: không đang chạy");
			return false;
		}

		try
		{
			State = WorldState.Stopping;
			ServerStatus.Instance.SetStopping();
			Logger.Instance.Info("Đang dừng World...");

			// Dừng vòng lặp cập nhật
			_cancellationTokenSource?.Cancel();
			if (_updateTask != null)
			{
				await _updateTask;
			}

			// Dừng hệ thống Actor
			try
			{
				_actorSystemManager.TcpManagerActor.Tell(new StopServer());
			}
			catch (Exception ex)
			{
				Logger.Instance.Error($"Lỗi khi dừng hệ thống mạng: {ex.Message}");
			}

			// Cập nhật trạng thái offline cho LoginServer và dừng task nhận tin nhắn
			try
			{
				if (_loginServerClient.IsConnected)
				{
					await _loginServerClient.UpdateGameServerStatusAsync(0, false);
					await _loginServerClient.DisconnectAsync();
					Logger.Instance.Info("Đã dừng task nhận tin nhắn và ngắt kết nối LoginServer");
				}
			}
			catch (Exception ex)
			{
				Logger.Instance.Error($"Lỗi khi ngắt kết nối LoginServer: {ex.Message}");
			}

			State = WorldState.Stopped;
			ServerStatus.Instance.SetOffline();
			Logger.Instance.Info("World đã dừng thành công");

			return true;
		}
		catch (Exception ex)
		{
			Logger.Instance.Error($"Lỗi khi dừng World: {ex.Message}");
			return false;
		}
	}

	private async Task UpdateLoopAsync(CancellationToken cancellationToken)
	{
		_lastUpdateTime = DateTime.Now;
		_totalUpdates = 0;
		_totalUpdateTimeMs = 0;

		try
		{
			while (!cancellationToken.IsCancellationRequested)
			{
				var startTime = DateTime.Now;

				// Thực hiện cập nhật
				await UpdateAsync();

				var endTime = DateTime.Now;
				var updateTime = (endTime - startTime).TotalMilliseconds;

				_totalUpdates++;
				_totalUpdateTimeMs += (long)updateTime;

				// Tính toán thời gian chờ để duy trì tốc độ cập nhật ổn định
				var elapsed = (endTime - _lastUpdateTime).TotalMilliseconds;
				var remainingTime = _updateInterval.TotalMilliseconds - elapsed;

				if (remainingTime > 0)
				{
					await Task.Delay((int)remainingTime, cancellationToken);
				}

				_lastUpdateTime = DateTime.Now;
			}
		}
		catch (OperationCanceledException)
		{
			// Bình thường khi hủy
		}
		catch (Exception ex)
		{
			Logger.Instance.Error($"Lỗi trong vòng lặp cập nhật: {ex.Message}");
		}
	}

	private async Task UpdateAsync()
	{
		try
		{
			// Cập nhật logic game ở đây
			// Ví dụ: cập nhật vị trí người chơi, quái vật, xử lý chiến đấu, v.v.

			// Cập nhật thống kê
			if (_totalUpdates % 100 == 0) // Cập nhật mỗi 100 lần cập nhật
			{
				// Cập nhật trạng thái máy chủ
				int connectionCount = 0;
				try
				{
					var response = await _actorSystemManager.TcpManagerActor.Ask<SessionCountResponse>(new GetSessionCount());
					connectionCount = response.Count;
				}
				catch (Exception ex)
				{
					Logger.Instance.Error($"Lỗi khi lấy số lượng kết nối: {ex.Message}");
				}

				ServerStatus.Instance.UpdateServerStats(
					Uptime,
					AverageUpdateTimeMs,
					connectionCount,
					_loginServerClient.IsConnected
				);

				// Cập nhật trạng thái với LoginServer mỗi 10 giây (100 lần cập nhật * 100ms)
				try
				{
					if (!_loginServerClient.IsConnected)
					{
						var reconnected = await _loginServerClient.ConnectAsync();
						if (reconnected)
						{
							// Bắt đầu lại task nhận tin nhắn sau khi kết nối lại
							_loginServerClient.StartReceiveMessagesTask();
							Logger.Instance.Info("Đã kết nối lại và bắt đầu lắng nghe tin nhắn từ LoginServer");
						}
					}

					if (_loginServerClient.IsConnected)
					{
						await _loginServerClient.UpdateGameServerStatusAsync(connectionCount, true);
					}
				}
				catch (Exception ex)
				{
					Logger.Instance.Error($"Lỗi khi cập nhật trạng thái với LoginServer: {ex.Message}");
				}

				// Vẫn ghi log nhưng ở mức Debug để không hiển thị quá nhiều trong giao diện
				// Logger.Instance.Debug($"Thống kê World: Uptime={Uptime}, Avg Update Time={AverageUpdateTimeMs:F2}ms, Connections={connectionCount}");
			}

			await Task.CompletedTask;
		}
		catch (Exception ex)
		{
			Logger.Instance.Error($"Lỗi khi cập nhật World: {ex.Message}");
		}
	}

	public async Task RestartAsync()
	{
		Logger.Instance.Info("Đang khởi động lại World...");
		await StopAsync();
		await Task.Delay(1000); // Chờ 1 giây
		await StartAsync();
	}

	/// <summary>
	/// Test method để kiểm tra OfflineActorNetState
	/// </summary>
	public static void TestOfflinePlayer()
	{
		try
		{
			LogHelper.WriteLine(LogLevel.Info, "Bắt đầu test OfflineActorNetState...");

			// Test tạo OfflineActorNetState
			var sessionId = GetNextOfflineSessionId();
			var endPoint = new IPEndPoint(System.Net.IPAddress.Parse("127.0.0.1"), 8080);
			var offlineActorNetState = new HeroYulgang.Core.Network.OfflineActorNetState(sessionId, endPoint);

			LogHelper.WriteLine(LogLevel.Info, $"Đã tạo OfflineActorNetState với SessionID: {offlineActorNetState.SessionID}");
			LogHelper.WriteLine(LogLevel.Info, $"Online: {offlineActorNetState.Online}, Login: {offlineActorNetState.Login}, Running: {offlineActorNetState.Running}");

			// Test các phương thức Send
			offlineActorNetState.Send(new byte[10], 10);
			offlineActorNetState.SendSinglePackage(new byte[10], 10);
			offlineActorNetState.Send_Map_Data(new byte[10], 10);
			offlineActorNetState.SendMultiplePackage(new byte[10], 10);

			LogHelper.WriteLine(LogLevel.Info, "Đã test các phương thức Send thành công");

			// Test Offline method
			offlineActorNetState.Offline();
			LogHelper.WriteLine(LogLevel.Info, $"Sau khi gọi Offline() - ThoatGame: {offlineActorNetState.ThoatGame}, TreoMay: {offlineActorNetState.TreoMay}");

			// Test Dispose
			offlineActorNetState.Dispose();
			LogHelper.WriteLine(LogLevel.Info, $"Sau khi gọi Dispose() - Running: {offlineActorNetState.Running}");

			LogHelper.WriteLine(LogLevel.Info, "Test OfflineActorNetState hoàn thành thành công!");
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, $"Lỗi trong test OfflineActorNetState: {ex.Message}");
		}
	}


}
