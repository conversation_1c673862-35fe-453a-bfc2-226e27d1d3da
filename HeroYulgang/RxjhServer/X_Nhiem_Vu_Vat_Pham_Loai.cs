using System;

namespace RxjhServer;

public class X_Nhiem_Vu_Vat_Pham_Loai
{
	public byte[] VatPham_byte;

	public int VatPham_ID
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 0, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 0, 4);
		}
	}

	public int VatPhamSoLuong
	{
		get
		{
			var array = new byte[4];
			System.Buffer.BlockCopy(VatPham_byte, 4, array, 0, 4);
			return BitConverter.ToInt32(array, 0);
		}
		set
		{
			System.Buffer.BlockCopy(BitConverter.GetBytes(value), 0, VatPham_byte, 4, 4);
		}
	}

	public X_Nhiem_Vu_Vat_Pham_Loai(byte[] byte_0)
	{
		VatPham_byte = byte_0;
	}
}
