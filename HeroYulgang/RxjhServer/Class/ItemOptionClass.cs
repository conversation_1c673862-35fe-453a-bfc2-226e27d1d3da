﻿using RxjhServer;

namespace RxjhServer;

public class ItemOptionClass
{
    public int ID { get; set; }

    public int FLD_PID { get; set; }

    public int Bonus_HP { get; set; }

    public int Bonus_PercentHP { get; set; }

    public int Bonus_MP { get; set; }

    public int Bonus_PercentMP { get; set; }

    public string FLD_NAME { get; set; }

    public int Bonus_ATK { get; set; }

    public int Bonus_PercentATK { get; set; }

    public int Bonus_DF { get; set; }

    public int Bonus_PercentDF { get; set; }

    public int Bonus_PercentATKSkill { get; set; }

    public int Bonus_DefSkill { get; set; }

    public int Bonus_Qigong { get; set; }

    public int Bonus_DropGold { get; set; }

    public int Bonus_Exp { get; set; }

    public int Bonus_Lucky { get; set; }

    public int Bonus_Accuracy { get; set; }

    public int Bonus_Evasion { get; set; }

    public int Bonus_DiemHoangKim { get; set; }

    public int Bonus_ATKMONSTER { get; set; }

    public int Bonus_DEFMONSTER { get; set; }

    public static ItemOptionClass GetItemOption(long pid)
    {
        if (World.ListItemOption.TryGetValue(pid, out var value)) return value;
        return null;
    }
}
