namespace RxjhServer;

public class UpgradeItemClass
{
    public int ID { get; set; }

    public int ItemID { get; set; }

    public int ItemLevel { get; set; }

    public int ItemType { get; set; }

    public int Upgrade_PP { get; set; }

    public string ItemName { get; set; }

    public int NguyenLieu_ID { get; set; }

    public int GiamCuongHoa { get; set; }

    public int YeuCauCuongHoa { get; set; }

    public static UpgradeItemClass GetUpgradeItem(int PID)
    {
        if (World.List_UpgradeItem.TryGetValue(PID, out var value)) return value;
        return null;
    }
}
