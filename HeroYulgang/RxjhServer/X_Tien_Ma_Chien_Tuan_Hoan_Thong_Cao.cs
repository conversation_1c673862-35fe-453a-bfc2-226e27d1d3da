using System;
using System.Timers;
using HeroYulgang.Helpers;

namespace RxjhServer;

public class X_Tien_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_Thong_Cao : IDisposable
{
	private System.Timers.Timer ThoiGian1;

	private DateTime kssj;

	private int kssjint;

	public X_Tien_<PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON><PERSON>_<PERSON>hong_<PERSON>()
	{
		try
		{
			World.GuiThongBao("Thế L<PERSON> (Random) sắp bắt đầu. Phe ch<PERSON>h <PERSON> choàng trắng - Phe tà áo choàng <PERSON>");
			World.tmc_flag = true;
			kssj = DateTime.Now.AddMinutes(5.0);
			ThoiGian1 = new(5000.0);
			ThoiGian1.Elapsed += ThoiGianKetThucSuKien1;
			ThoiGian1.Enabled = true;
			ThoiGian1.AutoReset = true;
			ThoiGianKetThucSuKien1(null, null);
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "<PERSON>i<PERSON><PERSON> chiến tu<PERSON>n ho<PERSON> error：" + ex);
		}
	}

	public void ThoiGianKetThucSuKien1(object sender, ElapsedEventArgs e)
	{
		try
		{
			var num = (int)kssj.Subtract(DateTime.Now).TotalSeconds;
			if (num <= 0)
			{
				num = 0;
			}
			kssjint = num;
			foreach (var value in World.allConnectedChars.Values)
			{
				if (value.NhanVatToaDo_BanDo == 801)
				{
					value.GuiDi_TheLucChien_TinTuc_TranChienSapBatDau(kssjint);
					value.GuiDi_TheLucChien_DemNguoc(kssjint);
				}
				else if (value.Player_Job_level >= 2 && value.NhanVatToaDo_BanDo != 801)
				{
					value.Send_LoiMoi_TLC_Packet(1, 1, 2);
				}
			}
			if (kssjint <= 0)
			{
				Dispose();
			}
		}
		catch (Exception ex)
		{
			LogHelper.WriteLine(LogLevel.Error, "Tiên Ma chiến tuần hoàn ThongBao ThoiGianKetThucSuKien1 error：" + ex);
		}
	}

	public void Dispose()
	{
		try
		{
			if (ThoiGian1 != null)
			{
				ThoiGian1.Enabled = false;
				ThoiGian1.Close();
				ThoiGian1.Dispose();
				ThoiGian1 = null;
			}
			if (World.TienMaChienThongBao != null)
			{
				World.TienMaChienThongBao = null;
			}
		}
		catch
		{
		}
	}
}
