﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblXwwlCw
{
    public int Id { get; set; }

    public string? ZrName { get; set; }

    public int ItmeId { get; set; }

    public string? Name { get; set; }

    public int? FldZcd { get; set; }

    public string? FldExp { get; set; }

    public int? FldLevel { get; set; }

    public int? FldBs { get; set; }

    public int? FldJob { get; set; }

    public int? FldJobLevel { get; set; }

    public int? FldHp { get; set; }

    public int? FldMp { get; set; }

    public byte[]? FldKongfu { get; set; }

    public byte[]? FldWearitem { get; set; }

    public byte[]? FldItem { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldSxbl { get; set; }
}
