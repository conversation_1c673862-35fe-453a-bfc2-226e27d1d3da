﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblGroupQuest
{
    public int Id { get; set; }

    public string QuestName { get; set; } = null!;

    public string QuestDesc { get; set; } = null!;

    public int QuestType { get; set; }

    public int TargetType { get; set; }

    public int? TargetId { get; set; }

    public int TargetCount { get; set; }

    public int RequiredLevel { get; set; }

    public int RewardExp { get; set; }

    public long RewardMoney { get; set; }

    public int? RewardItem { get; set; }

    public int? RewardItemCount { get; set; }

    public int ResetType { get; set; }

    public bool IsActive { get; set; }

    public DateTime CreateDate { get; set; }

    public virtual ICollection<TblFactionQuestProgress> TblFactionQuestProgresses { get; set; } = new List<TblFactionQuestProgress>();

    public virtual ICollection<TblGroupQuestContribution> TblGroupQuestContributions { get; set; } = new List<TblGroupQuestContribution>();

    public virtual ICollection<TblGroupQuestHistory> TblGroupQuestHistories { get; set; } = new List<TblGroupQuestHistory>();

    public virtual ICollection<TblGuildQuestProgress> TblGuildQuestProgresses { get; set; } = new List<TblGuildQuestProgress>();
}
