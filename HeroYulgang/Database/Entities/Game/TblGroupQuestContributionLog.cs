﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblGroupQuestContributionLog
{
    public int Id { get; set; }

    public int ContributionId { get; set; }

    public int ProgressId { get; set; }

    public int PlayerId { get; set; }

    public string PlayerName { get; set; } = null!;

    public int ActionType { get; set; }

    public int? TargetId { get; set; }

    public string? TargetName { get; set; }

    public int ContributionCount { get; set; }

    public DateTime ContributionTime { get; set; }

    public virtual TblGroupQuestContribution Contribution { get; set; } = null!;
}
