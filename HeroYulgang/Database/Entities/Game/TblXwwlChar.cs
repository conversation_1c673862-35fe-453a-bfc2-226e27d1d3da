﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Game;

public partial class TblXwwlChar
{
    public int Id { get; set; }

    public string FldId { get; set; } = null!;

    public string FldName { get; set; } = null!;

    public int FldIndex { get; set; }

    public int? FldLevel { get; set; }

    public byte[] FldFace { get; set; } = null!;

    public int FldJob { get; set; }

    public string? FldExp { get; set; }

    public int? FldZx { get; set; }

    public int? FldJobLevel { get; set; }

    public double? FldX { get; set; }

    public double? FldY { get; set; }

    public double? FldZ { get; set; }

    public int? FldMenow { get; set; }

    public string? FldMoney { get; set; }

    public int? FldHp { get; set; }

    public int? FldMp { get; set; }

    public int? FldSp { get; set; }

    public int? FldWx { get; set; }

    public int? FldSe { get; set; }

    public int? FldPoint { get; set; }

    public byte[]? FldSkills { get; set; }

    public byte[]? FldWearitem { get; set; }

    public byte[]? FldItem { get; set; }

    public byte[]? FldQitem { get; set; }

    public byte[]? FldNtcitem { get; set; }

    public byte[]? FldCoatitem { get; set; }

    public byte[]? FldKongfu { get; set; }

    public byte[]? FldHits { get; set; }

    public byte[]? FldDoors { get; set; }

    public byte[]? FldQuest { get; set; }

    public int? FldLumpid { get; set; }

    public int? FldFightExp { get; set; }

    public int? FldJ9 { get; set; }

    public int? FldJq { get; set; }

    public string? FldJl { get; set; }

    public byte[]? FldNametype { get; set; }

    public int? FldZbver { get; set; }

    public int? FldZzType { get; set; }

    public int? FldZzSl { get; set; }

    public byte[]? FldCtime { get; set; }

    public byte[]? FldCtimenew { get; set; }

    public byte[]? FldStime { get; set; }

    public string? FldQlName { get; set; }

    public string? FldQlJzname { get; set; }

    public int? FldQlDu { get; set; }

    public int? FldQlDuMax { get; set; }

    public int? FldQlRank { get; set; }

    public byte[]? FldThangThienKhiCong { get; set; }

    public byte[]? FldThangThienVoCong { get; set; }

    public int? FldThangThienLichLuyen { get; set; }

    public int? FldThangThienVoCongDiemSo { get; set; }

    public int FldAddHp { get; set; }

    public int FldAddAt { get; set; }

    public int FldAddDf { get; set; }

    public int FldAddHb { get; set; }

    public int FldAddMp { get; set; }

    public int FldAddMz { get; set; }

    public int FldZs { get; set; }

    public int FldOnline { get; set; }

    public int? FldGetWx { get; set; }

    public int FldTongkim { get; set; }

    public int FldTaisinh { get; set; }

    public int FldVipdj { get; set; }

    public byte[] 在线时间 { get; set; } = null!;

    public int? Fld七彩 { get; set; }

    public int FldVipAt { get; set; }

    public int FldVipDf { get; set; }

    public int FldVipHp { get; set; }

    public int FldVipLevel { get; set; }

    public int FldZscs { get; set; }

    public int FldSjjl { get; set; }

    public double Fld在线时间 { get; set; }

    public int Fld在线等级 { get; set; }

    public int Fld领奖标志 { get; set; }

    public int _ { get; set; }

    public int Fld签名类型 { get; set; }

    public int Fld任务等级4 { get; set; }

    public string Fld师傅 { get; set; } = null!;

    public string Fld徒弟1 { get; set; } = null!;

    public string Fld徒弟2 { get; set; } = null!;

    public string Fld徒弟3 { get; set; } = null!;

    public int Fld师徒武功11 { get; set; }

    public int Fld师徒武功12 { get; set; }

    public int Fld师徒武功13 { get; set; }

    public string FldDayQuest { get; set; } = null!;

    public int? FldTlc { get; set; }

    public string? FldFqid { get; set; }

    public long? SoLanGietNguoi { get; set; }

    public long? BiGietSoLan { get; set; }

    public byte[]? FldSuPhu { get; set; }

    public byte[]? FldDeTu { get; set; }

    public byte[]? FldSuDoVoCong { get; set; }

    public string? FldGiaiTruThoiGian { get; set; }

    public int? FldTitlePoints { get; set; }

    public DateTime? CongThanhChienThoiGian { get; set; }

    public int? FldXb { get; set; }

    public int? FldRoseTitlePoints { get; set; }

    public int? FldSpeakingType { get; set; }

    public byte[]? FldNszitem { get; set; }

    public byte[]? FldLjkongfu { get; set; }

    public int? BangPhaiDoCongHien { get; set; }

    public int? FldMlz { get; set; }

    public int? FldPvpPiont { get; set; }

    public int? FldThanNuVoCongDiemSo { get; set; }

    public string? FldLoveWord { get; set; }

    public int? FldMaritalStatus { get; set; }

    public int? FldMarried { get; set; }

    public DateTime? FldJhDate { get; set; }

    public int? FldFbTime { get; set; }

    public int? FldLostWx { get; set; }

    public int? FldHdTime { get; set; }

    public byte[]? FldKieuToc { get; set; }

    public byte[]? FldKhuonMat { get; set; }

    public int? FldWhtb { get; set; }

    public byte[]? FldChtime { get; set; }

    public string? FldConfig { get; set; }

    public byte[]? FldFashionItem { get; set; }

    public byte[]? FldQuestFinish { get; set; }

    public int? FldAddClvc { get; set; }

    public int? FldAddPtvc { get; set; }

    public int? FldAddKc { get; set; }

    public int? Version { get; set; }

    public bool? Nhanqualandau { get; set; }

    public string? TlcRandomPhe { get; set; }

    public int? VohuanGioihanTheongay { get; set; }

    public string? VohuanTime { get; set; }

    public int? FldMoneyextralevel { get; set; }

    public byte[]? FldPinkbagItem { get; set; }

    public byte[]? FldAsc7AntiQigong { get; set; }
}
