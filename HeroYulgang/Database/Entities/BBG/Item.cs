﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.BBG;

public partial class Item
{
    public int Id { get; set; }

    public string ProductCode { get; set; }

    public int FldPid { get; set; }

    public string FldName { get; set; }

    public int? FldPrice { get; set; }

    public int? FldPriceOld { get; set; }

    public string FldDesc { get; set; }

    public int? FldReturn { get; set; }

    public int? FldNumber { get; set; }

    public int? FldLock { get; set; }

    public int? FldDays { get; set; }

    public int? Order { get; set; }

    public int? CategoryId { get; set; }

    public int? FldMagic1 { get; set; }

    public int? FldMagic2 { get; set; }

    public int? FldMagic3 { get; set; }

    public int? FldMagic4 { get; set; }

    public int? FldMagic5 { get; set; }

    public int? FldPhaiChangKhoaLai { get; set; }

    public int? FldSoCapPhuHon { get; set; }

    public int? FldTienHoa { get; set; }

    public int? FldTrungCapPhuHon { get; set; }

    public virtual Shopcategory Category { get; set; }
}
