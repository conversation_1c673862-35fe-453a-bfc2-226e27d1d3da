using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Account;

public partial class Napthe
{
    public string <PERSON><PERSON><PERSON> { get; set; } = null!;

    public string Pin { get; set; } = null!;

    public string Serial { get; set; } = null!;

    public string <PERSON><PERSON><PERSON> { get; set; }

    public string <PERSON><PERSON><PERSON>an { get; set; }

    public byte? AdminCheck { get; set; }

    public string <PERSON>aithe { get; set; }

    public int? Thoigian1 { get; set; }

    public int Stt { get; set; }

    public string Tienduoccong { get; set; }

    public string Laygi { get; set; }

    public string <PERSON><PERSON><PERSON> { get; set; }
}
