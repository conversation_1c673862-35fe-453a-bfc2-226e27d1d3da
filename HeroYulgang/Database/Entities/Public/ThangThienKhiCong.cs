﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class ThangThienKhiCong
{
    public int Id { get; set; }

    public int KhiCongId { get; set; }

    public double? FldBonusRateValuePerPoint { get; set; }

    public int? VatPhamId { get; set; }

    public string? KhiCongTen { get; set; }

    public int? NhanVatNgheNghiep1 { get; set; }

    public int? NhanVatNgheNghiep2 { get; set; }

    public int? NhanVatNgheNghiep3 { get; set; }

    public int? NhanVatNgheNghiep4 { get; set; }

    public int? NhanVatNgheNghiep5 { get; set; }

    public int? NhanVatNgheNghiep6 { get; set; }

    public int? NhanVatNgheNghiep7 { get; set; }

    public int? NhanVatNgheNghiep8 { get; set; }

    public int? NhanVatNgheNghiep9 { get; set; }

    public int? NhanVatNgheNghiep10 { get; set; }

    public int? NhanVatNgheNghiep11 { get; set; }

    public int? NhanVatNgheNghiep12 { get; set; }

    public int? NhanVatNgheNghiep13 { get; set; }
}
