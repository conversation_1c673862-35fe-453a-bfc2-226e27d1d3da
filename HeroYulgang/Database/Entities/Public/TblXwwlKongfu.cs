﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblXwwlKongfu
{
    public int FldPid { get; set; }

    public string? FldName { get; set; }

    public int FldSourceAt { get; set; }

    public int? FldAt { get; set; }

    public int? FldMp { get; set; }

    public int? FldLevel { get; set; }

    public int? FldNeedexp { get; set; }

    public int? FldJob { get; set; }

    public int? FldZx { get; set; }

    public int? FldJoblevel { get; set; }

    public int? FldType { get; set; }

    public int? FldEffert { get; set; }

    public int? FldIndex { get; set; }

    public int? FldCongKichSoLuong { get; set; }

    public int? FldVoCongLoaiHinh { get; set; }

    public string? FldMoiCapNguyHai { get; set; }

    public int? FldMoiCapThemNguyHai { get; set; }

    public int? FldMoiCapThemMp { get; set; }

    public int? FldMoiCapThemLichLuyen { get; set; }

    public int? FldMoiCapThemTuLuyenDangCap { get; set; }

    public int? FldMoiCapVoCongDiemSo { get; set; }

    public int? FldVoCongToiCaoDangCap { get; set; }

    public int? FldTime { get; set; }

    public int? FldDeathtime { get; set; }

    public int? FldCdtime { get; set; }

    public int? TimeAnimation { get; set; }
}
