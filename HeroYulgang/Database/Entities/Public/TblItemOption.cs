﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class TblItemOption
{
    public int Id { get; set; }

    public int FldPid { get; set; }

    public string? FldName { get; set; }

    public int BonusHp { get; set; }

    public int BonusPercentHp { get; set; }

    public int BonusMp { get; set; }

    public int BonusPercentMp { get; set; }

    public int BonusAtk { get; set; }

    public int BonusPercentAtk { get; set; }

    public int BonusPercentDf { get; set; }

    public int BonusDf { get; set; }

    public int BonusPercentAtkskill { get; set; }

    public int BonusDefSkill { get; set; }

    public int BonusQigong { get; set; }

    public int BonusDropGold { get; set; }

    public int BonusExp { get; set; }

    public int BonusLucky { get; set; }

    public int BonusAccuracy { get; set; }

    public int BonusEvasion { get; set; }

    public int BonusDiemHoangKim { get; set; }

    public int BonusAtkmonster { get; set; }

    public int BonusDefmonster { get; set; }
}
