﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class 挂机控制
{
    public int? 挂机时间 { get; set; }

    public int? 挂机地图 { get; set; }

    public double? 挂机坐标x { get; set; }

    public double? 挂机坐标y { get; set; }

    public int? 挂机元宝数量 { get; set; }

    public int? 挂机积分数量 { get; set; }

    public int? 挂机最小等级 { get; set; }

    public int? 挂机最高等级 { get; set; }

    public int? 挂机坐标范围 { get; set; }

    public string? 挂机提示 { get; set; }
}
