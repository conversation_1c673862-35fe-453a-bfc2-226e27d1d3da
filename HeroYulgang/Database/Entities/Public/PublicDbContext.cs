﻿using System;
using System.Collections.Generic;
using HeroYulgang.Core;
using Microsoft.EntityFrameworkCore;

namespace HeroYulgang.Database.Entities.Public;

public partial class PublicDbContext : DbContext
{
    public PublicDbContext()
    {
    }

    public PublicDbContext(DbContextOptions<PublicDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<CheDuocVatPhamDanhSach> CheDuocVatPhamDanhSaches { get; set; }

    public virtual DbSet<CheTacVatPhamDanhSach> CheTacVatPhamDanhSaches { get; set; }

    public virtual DbSet<DangCapBanThuong> DangCapBanThuongs { get; set; }

    public virtual DbSet<GiftCode> GiftCodes { get; set; }

    public virtual DbSet<GiftCodeReward> GiftCodeRewards { get; set; }

    public virtual DbSet<KiemTraThietBi> KiemTraThietBis { get; set; }

    public virtual DbSet<TblItemOption> TblItemOptions { get; set; }

    public virtual DbSet<TblUpgradeItem> TblUpgradeItems { get; set; }

    public virtual DbSet<TblXwwlBossdrop> TblXwwlBossdrops { get; set; }

    public virtual DbSet<TblXwwlDrop> TblXwwlDrops { get; set; }

    public virtual DbSet<TblXwwlDropDch> TblXwwlDropDches { get; set; }

    public virtual DbSet<TblXwwlDropG> TblXwwlDropGs { get; set; }

    public virtual DbSet<TblXwwlGg> TblXwwlGgs { get; set; }

    public virtual DbSet<TblXwwlItem> TblXwwlItems { get; set; }

    public virtual DbSet<TblXwwlKongfu> TblXwwlKongfus { get; set; }

    public virtual DbSet<TblXwwlMap> TblXwwlMaps { get; set; }

    public virtual DbSet<TblXwwlMission> TblXwwlMissions { get; set; }

    public virtual DbSet<TblXwwlMonster> TblXwwlMonsters { get; set; }

    public virtual DbSet<TblXwwlMonsterSetBase> TblXwwlMonsterSetBases { get; set; }

    public virtual DbSet<TblXwwlNpc> TblXwwlNpcs { get; set; }

    public virtual DbSet<TblXwwlOpen> TblXwwlOpens { get; set; }

    public virtual DbSet<TblXwwlOpn> TblXwwlOpns { get; set; }

    public virtual DbSet<TblXwwlSell> TblXwwlSells { get; set; }

    public virtual DbSet<TblXwwlSkill> TblXwwlSkills { get; set; }

    public virtual DbSet<TblXwwlStone> TblXwwlStones { get; set; }

    public virtual DbSet<TblXwwlVome> TblXwwlVomes { get; set; }

    public virtual DbSet<Tbl制作物品> Tbl制作物品s { get; set; }

    public virtual DbSet<ThangThienKhiCong> ThangThienKhiCongs { get; set; }

    public virtual DbSet<VatPhamTraoDoi> VatPhamTraoDois { get; set; }

    public virtual DbSet<XwwlKill> XwwlKills { get; set; }

    public virtual DbSet<任务数据> 任务数据s { get; set; }

    public virtual DbSet<挂机控制> 挂机控制s { get; set; }

    public virtual DbSet<武勋buff增幅> 武勋buff增幅s { get; set; }

    public virtual DbSet<消耗提示> 消耗提示s { get; set; }

    public virtual DbSet<物品回收> 物品回收s { get; set; }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        // Sử dụng connection string từ cấu hình nếu chưa được cấu hình
        if (!optionsBuilder.IsConfigured)
        {
            var configManager = ConfigManager.Instance;
            optionsBuilder.UseSqlServer(configManager.ConnectionStrings.PublicDb);
        }
    }
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CheDuocVatPhamDanhSach>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CheDuocVatPhamDanhSach");

            entity.Property(e => e.CanVatPham).HasMaxLength(500);
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.VatPhamId).HasColumnName("VatPham_ID");
            entity.Property(e => e.VatPhamTen).HasMaxLength(50);
        });

        modelBuilder.Entity<CheTacVatPhamDanhSach>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("CheTacVatPhamDanhSach");

            entity.Property(e => e.CanVatPham).HasMaxLength(500);
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.VatPhamId).HasColumnName("VatPham_ID");
            entity.Property(e => e.VatPhamTen).HasMaxLength(50);
        });

        modelBuilder.Entity<DangCapBanThuong>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("DangCapBanThuong");

            entity.Property(e => e.GoiVatPham).IsUnicode(false);
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.SetId).HasColumnName("Set_ID");
        });

        modelBuilder.Entity<GiftCode>(entity =>
        {
            entity.HasKey(e => e.GiftCode1);

            entity.ToTable("GiftCode");

            entity.Property(e => e.GiftCode1)
                .HasMaxLength(50)
                .HasDefaultValue("XXXXXXXXXX")
                .UseCollation("Vietnamese_CI_AS")
                .HasColumnName("GiftCode");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<GiftCodeReward>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("GiftCode_Rewards");

            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.Note).UseCollation("Vietnamese_CI_AS");
            entity.Property(e => e.Rewards).UseCollation("Vietnamese_CI_AS");
        });

        modelBuilder.Entity<KiemTraThietBi>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("KiemTraThietBi");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.VatPhamCaoNhatHpgiaTri).HasColumnName("VatPhamCaoNhatHPGiaTri");
        });

        modelBuilder.Entity<TblItemOption>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_ItemOption");

            entity.Property(e => e.BonusAccuracy).HasColumnName("Bonus_Accuracy");
            entity.Property(e => e.BonusAtk).HasColumnName("Bonus_ATK");
            entity.Property(e => e.BonusAtkmonster).HasColumnName("Bonus_ATKMONSTER");
            entity.Property(e => e.BonusDefSkill).HasColumnName("Bonus_DefSkill");
            entity.Property(e => e.BonusDefmonster).HasColumnName("Bonus_DEFMONSTER");
            entity.Property(e => e.BonusDf).HasColumnName("Bonus_DF");
            entity.Property(e => e.BonusDiemHoangKim).HasColumnName("Bonus_DiemHoangKim");
            entity.Property(e => e.BonusDropGold).HasColumnName("Bonus_DropGold");
            entity.Property(e => e.BonusEvasion).HasColumnName("Bonus_Evasion");
            entity.Property(e => e.BonusExp).HasColumnName("Bonus_Exp");
            entity.Property(e => e.BonusHp).HasColumnName("Bonus_HP");
            entity.Property(e => e.BonusLucky).HasColumnName("Bonus_Lucky");
            entity.Property(e => e.BonusMp).HasColumnName("Bonus_MP");
            entity.Property(e => e.BonusPercentAtk).HasColumnName("Bonus_PercentATK");
            entity.Property(e => e.BonusPercentAtkskill).HasColumnName("Bonus_PercentATKSkill");
            entity.Property(e => e.BonusPercentDf).HasColumnName("Bonus_PercentDF");
            entity.Property(e => e.BonusPercentHp).HasColumnName("Bonus_PercentHP");
            entity.Property(e => e.BonusPercentMp).HasColumnName("Bonus_PercentMP");
            entity.Property(e => e.BonusQigong).HasColumnName("Bonus_Qigong");
            entity.Property(e => e.FldName)
                .HasMaxLength(100)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<TblUpgradeItem>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_Upgrade_Item");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.ItemId).HasColumnName("ItemID");
            entity.Property(e => e.ItemName)
                .HasMaxLength(100)
                .IsUnicode(false);
            entity.Property(e => e.NguyenLieuId).HasColumnName("NguyenLieu_ID");
            entity.Property(e => e.UpgradePp).HasColumnName("Upgrade_PP");
        });

        modelBuilder.Entity<TblXwwlBossdrop>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_BOSSDROP");

            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldKhoaLai).HasColumnName("FLD_KhoaLai");
            entity.Property(e => e.FldLevel1).HasColumnName("FLD_LEVEL1");
            entity.Property(e => e.FldLevel2).HasColumnName("FLD_LEVEL2");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
            entity.Property(e => e.FldSoCapPhuHon).HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldSunx)
                .HasMaxLength(500)
                .HasColumnName("FLD_SUNX");
            entity.Property(e => e.FldTienHoa).HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon).HasColumnName("FLD_TrungCapPhuHon");
        });

        modelBuilder.Entity<TblXwwlDrop>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_DROP");

            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldKhoaLai).HasColumnName("FLD_KhoaLai");
            entity.Property(e => e.FldLevel1).HasColumnName("FLD_LEVEL1");
            entity.Property(e => e.FldLevel2).HasColumnName("FLD_LEVEL2");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
            entity.Property(e => e.FldSoCapPhuHon).HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldSunx)
                .HasMaxLength(500)
                .HasColumnName("FLD_SUNX");
            entity.Property(e => e.FldTienHoa).HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon).HasColumnName("FLD_TrungCapPhuHon");
        });

        modelBuilder.Entity<TblXwwlDropDch>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_DROP_DCH");

            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldKhoaLai).HasColumnName("FLD_KhoaLai");
            entity.Property(e => e.FldLevel1).HasColumnName("FLD_LEVEL1");
            entity.Property(e => e.FldLevel2).HasColumnName("FLD_LEVEL2");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
            entity.Property(e => e.FldSoCapPhuHon).HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldSunx)
                .HasMaxLength(500)
                .HasColumnName("FLD_SUNX");
            entity.Property(e => e.FldTienHoa).HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon).HasColumnName("FLD_TrungCapPhuHon");
        });

        modelBuilder.Entity<TblXwwlDropG>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_DROP_GS");

            entity.Property(e => e.FldKhoaLai).HasColumnName("FLD_KhoaLai");
            entity.Property(e => e.FldLevel1).HasColumnName("FLD_LEVEL1");
            entity.Property(e => e.FldLevel2).HasColumnName("FLD_LEVEL2");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
            entity.Property(e => e.FldSoCapPhuHon).HasColumnName("FLD_SoCapPhuHon");
            entity.Property(e => e.FldSunx)
                .HasMaxLength(500)
                .HasColumnName("FLD_SUNX");
            entity.Property(e => e.FldTienHoa).HasColumnName("FLD_TienHoa");
            entity.Property(e => e.FldTrungCapPhuHon).HasColumnName("FLD_TrungCapPhuHon");
        });

        modelBuilder.Entity<TblXwwlGg>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_Gg");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Txt)
                .HasMaxLength(255)
                .HasColumnName("txt");
            entity.Property(e => e.Type).HasColumnName("type");
        });

        modelBuilder.Entity<TblXwwlItem>(entity =>
        {
            entity.HasKey(e => e.FldPid).HasName("TBL_XWWL_ITEM_PK");

            entity.ToTable("TBL_XWWL_ITEM");

            entity.Property(e => e.FldPid)
                .ValueGeneratedNever()
                .HasColumnName("FLD_PID");
            entity.Property(e => e.FldAp)
                .HasDefaultValue(0)
                .HasColumnName("FLD_AP");
            entity.Property(e => e.FldAt1).HasColumnName("FLD_AT1");
            entity.Property(e => e.FldAt2).HasColumnName("FLD_AT2");
            entity.Property(e => e.FldDes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("FLD_DES");
            entity.Property(e => e.FldDf).HasColumnName("FLD_DF");
            entity.Property(e => e.FldEl).HasColumnName("FLD_EL");
            entity.Property(e => e.FldHeadWear)
                .HasDefaultValue(0)
                .HasColumnName("FLD_HEAD_WEAR");
            entity.Property(e => e.FldIntegration).HasColumnName("FLD_INTEGRATION");
            entity.Property(e => e.FldJobLevel).HasColumnName("FLD_JOB_LEVEL");
            entity.Property(e => e.FldLevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldLock).HasColumnName("FLD_LOCK");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5).HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldMoney)
                .HasDefaultValue(0)
                .HasColumnName("FLD_MONEY");
            entity.Property(e => e.FldName)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNeedFightexp).HasColumnName("FLD_NEED_FIGHTEXP");
            entity.Property(e => e.FldNeedMoney).HasColumnName("FLD_NEED_MONEY");
            entity.Property(e => e.FldNj).HasColumnName("FLD_NJ");
            entity.Property(e => e.FldQuestitem).HasColumnName("FLD_QUESTITEM");
            entity.Property(e => e.FldRecycleMoney).HasColumnName("FLD_RECYCLE_MONEY");
            entity.Property(e => e.FldReside1).HasColumnName("FLD_RESIDE1");
            entity.Property(e => e.FldReside2).HasColumnName("FLD_RESIDE2");
            entity.Property(e => e.FldSaleMoney).HasColumnName("FLD_SALE_MONEY");
            entity.Property(e => e.FldSellType)
                .HasDefaultValue(0)
                .HasColumnName("FLD_SELL_TYPE");
            entity.Property(e => e.FldSeries).HasColumnName("FLD_SERIES");
            entity.Property(e => e.FldSex).HasColumnName("FLD_SEX");
            entity.Property(e => e.FldSide).HasColumnName("FLD_SIDE");
            entity.Property(e => e.FldType).HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldUpLevel).HasColumnName("FLD_UP_LEVEL");
            entity.Property(e => e.FldWeight).HasColumnName("FLD_WEIGHT");
            entity.Property(e => e.FldWx).HasColumnName("FLD_WX");
            entity.Property(e => e.FldWxjd).HasColumnName("FLD_WXJD");
            entity.Property(e => e.FldZx).HasColumnName("FLD_ZX");
        });

        modelBuilder.Entity<TblXwwlKongfu>(entity =>
        {
            entity.HasKey(e => e.FldPid).HasName("PK_TBL_XWWL_KONGFU_copy1_copy1");

            entity.ToTable("TBL_XWWL_KONGFU");

            entity.Property(e => e.FldPid)
                .ValueGeneratedNever()
                .HasColumnName("FLD_PID");
            entity.Property(e => e.FldAt).HasColumnName("FLD_AT");
            entity.Property(e => e.FldCdtime).HasColumnName("FLD_CDTIME");
            entity.Property(e => e.FldCongKichSoLuong).HasColumnName("FLD_CongKichSoLuong");
            entity.Property(e => e.FldDeathtime).HasColumnName("FLD_DEATHTIME");
            entity.Property(e => e.FldEffert).HasColumnName("FLD_EFFERT");
            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldJob).HasColumnName("FLD_JOB");
            entity.Property(e => e.FldJoblevel).HasColumnName("FLD_JOBLEVEL");
            entity.Property(e => e.FldLevel).HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldMoiCapNguyHai)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_MoiCapNguyHai");
            entity.Property(e => e.FldMoiCapThemLichLuyen).HasColumnName("FLD_MoiCapThemLichLuyen");
            entity.Property(e => e.FldMoiCapThemMp).HasColumnName("FLD_MoiCapThemMP");
            entity.Property(e => e.FldMoiCapThemNguyHai).HasColumnName("FLD_MoiCapThemNguyHai");
            entity.Property(e => e.FldMoiCapThemTuLuyenDangCap).HasColumnName("FLD_MoiCapThemTuLuyenDangCap");
            entity.Property(e => e.FldMoiCapVoCongDiemSo).HasColumnName("FLD_MoiCapVoCongDiemSo");
            entity.Property(e => e.FldMp).HasColumnName("FLD_MP");
            entity.Property(e => e.FldName).HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNeedexp).HasColumnName("FLD_NEEDEXP");
            entity.Property(e => e.FldSourceAt).HasColumnName("FLD_SOURCE_AT");
            entity.Property(e => e.FldTime).HasColumnName("FLD_TIME");
            entity.Property(e => e.FldType).HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldVoCongLoaiHinh).HasColumnName("FLD_VoCongLoaiHinh");
            entity.Property(e => e.FldVoCongToiCaoDangCap).HasColumnName("FLD_VoCongToiCaoDangCap");
            entity.Property(e => e.FldZx).HasColumnName("FLD_ZX");
            entity.Property(e => e.TimeAnimation).HasColumnName("Time_Animation");
        });

        modelBuilder.Entity<TblXwwlMap>(entity =>
        {
            entity.HasKey(e => e.FldMid);

            entity.ToTable("TBL_XWWL_MAP");

            entity.Property(e => e.FldMid)
                .ValueGeneratedNever()
                .HasColumnName("FLD_MID");
            entity.Property(e => e.FldFile)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_FILE");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
        });

        modelBuilder.Entity<TblXwwlMission>(entity =>
        {
            entity.HasKey(e => e.FldId);

            entity.ToTable("TBL_XWWL_MISSION");

            entity.Property(e => e.FldId).HasColumnName("FLD_ID");
            entity.Property(e => e.FldData)
                .HasMaxLength(8000)
                .HasDefaultValueSql("(0x00)")
                .HasColumnName("FLD_DATA");
            entity.Property(e => e.FldGetItem)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("FLD_GET_ITEM");
            entity.Property(e => e.FldJob).HasColumnName("FLD_JOB");
            entity.Property(e => e.FldLevel).HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldMap).HasColumnName("FLD_MAP");
            entity.Property(e => e.FldMsg)
                .HasMaxLength(1000)
                .IsUnicode(false)
                .HasColumnName("FLD_MSG");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNeedItem)
                .HasMaxLength(200)
                .IsUnicode(false)
                .HasColumnName("FLD_NEED_ITEM");
            entity.Property(e => e.FldNpcid).HasColumnName("FLD_NPCID");
            entity.Property(e => e.FldNpcname)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NPCNAME");
            entity.Property(e => e.FldOn).HasColumnName("FLD_ON");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldStages).HasColumnName("FLD_STAGES");
            entity.Property(e => e.FldType).HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldX).HasColumnName("FLD_X");
            entity.Property(e => e.FldY).HasColumnName("FLD_Y");
            entity.Property(e => e.FldZx).HasColumnName("FLD_ZX");
        });

        modelBuilder.Entity<TblXwwlMonster>(entity =>
        {
            entity.HasKey(e => e.FldPid);

            entity.ToTable("TBL_XWWL_MONSTER");

            entity.Property(e => e.FldPid)
                .ValueGeneratedNever()
                .HasColumnName("FLD_PID");
            entity.Property(e => e.FldAt)
                .HasDefaultValue(9999)
                .HasColumnName("FLD_AT");
            entity.Property(e => e.FldAuto)
                .HasDefaultValue(9999)
                .HasColumnName("FLD_AUTO");
            entity.Property(e => e.FldBoss)
                .HasDefaultValue(9999)
                .HasColumnName("FLD_BOSS");
            entity.Property(e => e.FldDf)
                .HasDefaultValue(9999)
                .HasColumnName("FLD_DF");
            entity.Property(e => e.FldExp)
                .HasDefaultValue(9999)
                .HasColumnName("FLD_EXP");
            entity.Property(e => e.FldHp).HasColumnName("FLD_HP");
            entity.Property(e => e.FldLevel)
                .HasDefaultValue(1)
                .HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNpc)
                .HasDefaultValue(0)
                .HasColumnName("FLD_NPC");
            entity.Property(e => e.FldPp)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PP");
            entity.Property(e => e.FldQuest)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QUEST");
            entity.Property(e => e.FldQuestid)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QUESTID");
            entity.Property(e => e.FldQuestitem)
                .HasDefaultValue(0)
                .HasColumnName("FLD_QUESTITEM");
            entity.Property(e => e.FldStages)
                .HasDefaultValue(0)
                .HasColumnName("FLD_STAGES");
        });

        modelBuilder.Entity<TblXwwlMonsterSetBase>(entity =>
        {
            entity.HasKey(e => e.FldIndex).HasName("PK_TBL_XWWL_MONSTER_SET_BASE_FLD_INDEX");

            entity.ToTable("TBL_XWWL_MONSTER_SET_BASE");

            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldAccuracy).HasColumnName("FLD_Accuracy");
            entity.Property(e => e.FldActive)
                .HasDefaultValue(1)
                .HasColumnName("FLD_ACTIVE");
            entity.Property(e => e.FldAmount)
                .HasDefaultValue(20)
                .HasColumnName("FLD_Amount");
            entity.Property(e => e.FldAoe)
                .HasDefaultValue(50)
                .HasColumnName("FLD_Aoe");
            entity.Property(e => e.FldAt).HasColumnName("FLD_AT");
            entity.Property(e => e.FldAuto)
                .HasDefaultValue(1)
                .HasColumnName("FLD_AUTO");
            entity.Property(e => e.FldBoss).HasColumnName("FLD_BOSS");
            entity.Property(e => e.FldDf).HasColumnName("FLD_DF");
            entity.Property(e => e.FldEvasion).HasColumnName("FLD_Evasion");
            entity.Property(e => e.FldExp).HasColumnName("FLD_EXP");
            entity.Property(e => e.FldFace).HasColumnName("FLD_FACE");
            entity.Property(e => e.FldFace0).HasColumnName("FLD_FACE0");
            entity.Property(e => e.FldFreeDrop).HasColumnName("FLD_FreeDrop");
            entity.Property(e => e.FldGold).HasColumnName("FLD_GOLD");
            entity.Property(e => e.FldHp).HasColumnName("FLD_HP");
            entity.Property(e => e.FldLevel).HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldMid).HasColumnName("FLD_MID");
            entity.Property(e => e.FldName)
                .HasMaxLength(200)
                .UseCollation("SQL_Latin1_General_CP1_CI_AS")
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNewtime).HasColumnName("FLD_NEWTIME");
            entity.Property(e => e.FldNpc).HasColumnName("FLD_NPC");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldQdropPp).HasColumnName("FLD_QDropPP");
            entity.Property(e => e.FldQitemDrop).HasColumnName("FLD_QItemDrop");
            entity.Property(e => e.FldX).HasColumnName("FLD_X");
            entity.Property(e => e.FldY).HasColumnName("FLD_Y");
            entity.Property(e => e.FldZ).HasColumnName("FLD_Z");
        });

        modelBuilder.Entity<TblXwwlNpc>(entity =>
        {
            entity.HasKey(e => e.FldIndex).HasName("PK_TBL_XWWL_NPC1_copy1_copy2_copy1_copy2_copy1_copy3_copy1_copy1");

            entity.ToTable("TBL_XWWL_NPC");

            entity.Property(e => e.FldIndex)
                .ValueGeneratedNever()
                .HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldAt).HasColumnName("FLD_AT");
            entity.Property(e => e.FldAuto)
                .HasDefaultValue(1)
                .HasColumnName("FLD_AUTO");
            entity.Property(e => e.FldBoss).HasColumnName("FLD_BOSS");
            entity.Property(e => e.FldDf).HasColumnName("FLD_DF");
            entity.Property(e => e.FldExp).HasColumnName("FLD_EXP");
            entity.Property(e => e.FldFace).HasColumnName("FLD_FACE");
            entity.Property(e => e.FldFace0).HasColumnName("FLD_FACE0");
            entity.Property(e => e.FldHp).HasColumnName("FLD_HP");
            entity.Property(e => e.FldLevel).HasColumnName("FLD_LEVEL");
            entity.Property(e => e.FldMid).HasColumnName("FLD_MID");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNewtime).HasColumnName("FLD_NEWTIME");
            entity.Property(e => e.FldNpc).HasColumnName("FLD_NPC");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldX).HasColumnName("FLD_X");
            entity.Property(e => e.FldY).HasColumnName("FLD_Y");
            entity.Property(e => e.FldZ).HasColumnName("FLD_Z");
        });

        modelBuilder.Entity<TblXwwlOpen>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_OPEN");

            entity.Property(e => e.FldBd).HasColumnName("FLD_BD");
            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldFjThucTinh).HasColumnName("FLD_FJ_ThucTinh");
            entity.Property(e => e.FldFjTienHoa).HasColumnName("FLD_FJ_TienHoa");
            entity.Property(e => e.FldFjTrungCapPhuHon).HasColumnName("FLD_FJ_TrungCapPhuHon");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5).HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNamex)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAMEX");
            entity.Property(e => e.FldNumber).HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPidx).HasColumnName("FLD_PIDX");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
            entity.Property(e => e.SttHopEvent).HasColumnName("STT_Hop_Event");
        });

        modelBuilder.Entity<TblXwwlOpn>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_OPN");

            entity.Property(e => e.FldBd).HasColumnName("FLD_BD");
            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldFj中级附魂).HasColumnName("FLD_FJ_中级附魂");
            entity.Property(e => e.FldFj觉醒).HasColumnName("FLD_FJ_觉醒");
            entity.Property(e => e.FldFj进化).HasColumnName("FLD_FJ_进化");
            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMagic5).HasColumnName("FLD_MAGIC5");
            entity.Property(e => e.FldName)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAME");
            entity.Property(e => e.FldNamex)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NAMEX");
            entity.Property(e => e.FldNumber).HasColumnName("FLD_NUMBER");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.FldPidx).HasColumnName("FLD_PIDX");
            entity.Property(e => e.FldPp).HasColumnName("FLD_PP");
        });

        modelBuilder.Entity<TblXwwlSell>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_SELL");

            entity.Property(e => e.FldBd).HasColumnName("FLD_BD");
            entity.Property(e => e.FldCanVoHuan).HasColumnName("FLD_CanVoHuan");
            entity.Property(e => e.FldDays).HasColumnName("FLD_DAYS");
            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldMagic0).HasColumnName("FLD_MAGIC0");
            entity.Property(e => e.FldMagic1).HasColumnName("FLD_MAGIC1");
            entity.Property(e => e.FldMagic2).HasColumnName("FLD_MAGIC2");
            entity.Property(e => e.FldMagic3).HasColumnName("FLD_MAGIC3");
            entity.Property(e => e.FldMagic4).HasColumnName("FLD_MAGIC4");
            entity.Property(e => e.FldMoney).HasColumnName("FLD_MONEY");
            entity.Property(e => e.FldNid).HasColumnName("FLD_NID");
            entity.Property(e => e.FldNpcname)
                .HasMaxLength(50)
                .IsUnicode(false)
                .HasColumnName("FLD_NPCNAME");
            entity.Property(e => e.FldPid).HasColumnName("FLD_PID");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlSkill>(entity =>
        {
            entity.HasKey(e => e.FldId);

            entity.ToTable("TBL_XWWL_SKILL");

            entity.Property(e => e.FldId).HasColumnName("FLD_ID");
            entity.Property(e => e.FldBonusRateValuePerPoint1).HasColumnName("FLD_BonusRateValuePerPoint1");
            entity.Property(e => e.FldBonusRateValuePerPoint2).HasColumnName("FLD_BonusRateValuePerPoint2");
            entity.Property(e => e.FldDes)
                .HasMaxLength(2000)
                .IsUnicode(false)
                .HasColumnName("FLD_DES");
            entity.Property(e => e.FldIndex).HasColumnName("FLD_INDEX");
            entity.Property(e => e.FldJob).HasColumnName("FLD_JOB");
            entity.Property(e => e.FldName).HasColumnName("FLD_NAME");
            entity.Property(e => e.FldPid)
                .HasDefaultValue(0)
                .HasColumnName("FLD_PID");
        });

        modelBuilder.Entity<TblXwwlStone>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_STONE");

            entity.Property(e => e.FldTangGiam).HasColumnName("FLD_TangGiam");
            entity.Property(e => e.FldType).HasColumnName("FLD_TYPE");
            entity.Property(e => e.FldValue).HasColumnName("FLD_VALUE");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<TblXwwlVome>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_XWWL_VOME");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.Map).HasColumnName("map");
            entity.Property(e => e.Tomap).HasColumnName("tomap");
            entity.Property(e => e.Tox).HasColumnName("tox");
            entity.Property(e => e.Toy).HasColumnName("toy");
            entity.Property(e => e.Toz).HasColumnName("toz");
            entity.Property(e => e.X).HasColumnName("x");
            entity.Property(e => e.Y).HasColumnName("y");
            entity.Property(e => e.Z).HasColumnName("z");
        });

        modelBuilder.Entity<Tbl制作物品>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("TBL_制作物品");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.物品id).HasColumnName("物品ID");
            entity.Property(e => e.物品名).HasMaxLength(50);
            entity.Property(e => e.需要物品).HasMaxLength(500);
        });

        modelBuilder.Entity<ThangThienKhiCong>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("ThangThienKhiCong");

            entity.Property(e => e.FldBonusRateValuePerPoint).HasColumnName("FLD_BonusRateValuePerPoint");
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd()
                .HasColumnName("ID");
            entity.Property(e => e.KhiCongId).HasColumnName("KhiCongID");
            entity.Property(e => e.KhiCongTen).HasMaxLength(255);
            entity.Property(e => e.NhanVatNgheNghiep1).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep10).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep11).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep12).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep2).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep3).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep4).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep5).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep6).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep7).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep8).HasDefaultValue(0);
            entity.Property(e => e.NhanVatNgheNghiep9).HasDefaultValue(0);
            entity.Property(e => e.VatPhamId).HasColumnName("VatPham_ID");
        });

        modelBuilder.Entity<VatPhamTraoDoi>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("VatPhamTraoDoi");

            entity.Property(e => e.CanVatPham).IsUnicode(false);
            entity.Property(e => e.GoiVatPham).IsUnicode(false);
            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.MieuTa)
                .HasMaxLength(50)
                .IsUnicode(false);
            entity.Property(e => e.SetId).HasColumnName("Set_ID");
            entity.Property(e => e.TienBac)
                .HasMaxLength(50)
                .IsUnicode(false);
        });

        modelBuilder.Entity<XwwlKill>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("XWWL_kill");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.Sffh).HasColumnName("sffh");
            entity.Property(e => e.Txt)
                .HasMaxLength(50)
                .HasColumnName("txt");
        });

        modelBuilder.Entity<任务数据>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("任务数据");

            entity.Property(e => e.Id).HasColumnName("ID");
            entity.Property(e => e.任务id).HasColumnName("任务ID");
            entity.Property(e => e.备注).IsUnicode(false);
            entity.Property(e => e.怪物id).HasColumnName("怪物ID");
        });

        modelBuilder.Entity<挂机控制>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("挂机控制");

            entity.Property(e => e.挂机坐标x).HasColumnName("挂机坐标X");
            entity.Property(e => e.挂机坐标y).HasColumnName("挂机坐标Y");
            entity.Property(e => e.挂机提示)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<武勋buff增幅>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("武勋BUFF增幅");

            entity.Property(e => e.FldAt).HasColumnName("FLD_AT");
            entity.Property(e => e.FldDf).HasColumnName("FLD_DF");
            entity.Property(e => e.FldHb).HasColumnName("FLD_HB");
            entity.Property(e => e.FldHp).HasColumnName("FLD_HP");
            entity.Property(e => e.FldMp).HasColumnName("FLD_MP");
            entity.Property(e => e.FldMz).HasColumnName("FLD_MZ");
            entity.Property(e => e.FldWxlevel).HasColumnName("FLD_WXLEVEL");
            entity.Property(e => e.Fld所有气功增加).HasColumnName("FLD_所有气功增加");
            entity.Property(e => e.Fld武勋量).HasColumnName("FLD_武勋量");
            entity.Property(e => e.Id).HasColumnName("ID");
        });

        modelBuilder.Entity<消耗提示>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("消耗提示");

            entity.Property(e => e.Id).HasColumnName("id");
            entity.Property(e => e.上线提示)
                .HasMaxLength(200)
                .IsUnicode(false);
            entity.Property(e => e.自定开头内容)
                .HasMaxLength(200)
                .IsUnicode(false);
        });

        modelBuilder.Entity<物品回收>(entity =>
        {
            entity
                .HasNoKey()
                .ToTable("物品回收");

            entity.Property(e => e.Id)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("ID");
            entity.Property(e => e.元宝)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.内功)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.单件物品)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.命中)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.回避)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.套装id)
                .HasMaxLength(10)
                .IsFixedLength()
                .HasColumnName("套装ID");
            entity.Property(e => e.攻击)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.武勋)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.生命)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.积分)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.说明)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.金钱)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.防御)
                .HasMaxLength(10)
                .IsFixedLength();
            entity.Property(e => e.需要物品)
                .HasMaxLength(10)
                .IsFixedLength();
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}
