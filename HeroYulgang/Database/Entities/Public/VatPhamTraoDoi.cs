﻿using System;
using System.Collections.Generic;

namespace HeroYulgang.Database.Entities.Public;

public partial class VatPhamTraoDoi
{
    public int Id { get; set; }

    public string? CanVatPham { get; set; }

    public int? VoHuan { get; set; }

    public int? <PERSON><PERSON><PERSON><PERSON><PERSON> { get; set; }

    public string? TienBac { get; set; }

    public int? SinhMenh { get; set; }

    public int? CongKich { get; set; }

    public int? PhongNgu { get; set; }

    public int? NeTranh { get; set; }

    public int? TrungDich { get; set; }

    public int? NoiCong { get; set; }

    public int? SetId { get; set; }

    public string? GoiVatPham { get; set; }

    public string? MieuTa { get; set; }
}
