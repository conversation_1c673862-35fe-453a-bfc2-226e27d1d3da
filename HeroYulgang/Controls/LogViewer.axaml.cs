using Avalonia;
using Avalonia.Controls;
using Avalonia.Input.Platform;
using Avalonia.Interactivity;
using Avalonia.Markup.Xaml;
using Avalonia.Threading;
using HeroYulgang.Helpers;
using HeroYulgang.Services;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Linq;

namespace HeroYulgang.Controls
{
    public partial class LogViewer : UserControl
    {
        private readonly ComboBox? _logLevelFilter;
        private readonly ListBox? _logListBox;
        private readonly Button? _clearButton;
        private readonly Button? _openLogFolderButton;
        private readonly TextBlock? _logCountText;
        private readonly ObservableCollection<LogEntry> _filteredLogs = [];

        public LogViewer()
        {
            InitializeComponent();

            _logLevelFilter = this.FindControl<ComboBox>("LogLevelFilter");
            _logListBox = this.FindControl<ListBox>("LogListBox");
            _clearButton = this.FindControl<Button>("ClearButton");
            _openLogFolderButton = this.FindControl<Button>("OpenLogFolderButton");
            _logCountText = this.FindControl<TextBlock>("LogCountText");

            if (_logLevelFilter != null)
                _logLevelFilter.SelectionChanged += LogLevelFilter_SelectionChanged;

            if (_clearButton != null)
                _clearButton.Click += ClearButton_Click;

            if (_openLogFolderButton != null)
                _openLogFolderButton.Click += OpenLogFolderButton_Click;

            if (_logListBox != null)
            {
                _logListBox.ItemsSource = _filteredLogs;
                _logListBox.SelectionChanged += LogListBox_SelectionChanged;
            }

            // Subscribe to log changes
            Logger.Instance.Logs.CollectionChanged += Logs_CollectionChanged;

            // Initial filter
            UpdateFilteredLogs();

            // Đảm bảo thư mục logs tồn tại
            EnsureLogDirectoryExists();
        }

        private void InitializeComponent()
        {
            AvaloniaXamlLoader.Load(this);
        }

        private void LogLevelFilter_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            // Đảm bảo thực hiện trên UI thread
            Dispatcher.UIThread.Post(UpdateFilteredLogs);
        }

        private void ClearButton_Click(object? sender, RoutedEventArgs e)
        {
            // Đảm bảo thực hiện trên UI thread
            Dispatcher.UIThread.Post(ClearLogs);
        }

        private void ClearLogs()
        {
            Logger.Instance.Clear();
        }

        private void Logs_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // Đảm bảo cập nhật UI trên UI thread
            Dispatcher.UIThread.Post(UpdateFilteredLogs);
        }

        private void UpdateFilteredLogs()
        {
            // Đảm bảo phương thức này chạy trên UI thread
            if (!Dispatcher.UIThread.CheckAccess())
            {
                Dispatcher.UIThread.Post(UpdateFilteredLogs);
                return;
            }

            if (_logLevelFilter == null || _logCountText == null)
                return;

            _filteredLogs.Clear();

            var selectedIndex = _logLevelFilter.SelectedIndex;
            var logs = Logger.Instance.Logs;

            IEnumerable<LogEntry> filteredLogs = selectedIndex switch
            {
                0 => logs, // Tất cả
                1 => logs.Where(l => l.Level == LogLevel.Debug),
                2 => logs.Where(l => l.Level == LogLevel.Info),
                3 => logs.Where(l => l.Level == LogLevel.Warning),
                4 => logs.Where(l => l.Level == LogLevel.Error),
                5 => logs.Where(l => l.Level == LogLevel.Fatal),
                _ => logs
            };

            foreach (var log in filteredLogs)
            {
                _filteredLogs.Add(log);
            }

            _logCountText.Text = _filteredLogs.Count.ToString();

            // Scroll to the bottom
            if (_logListBox != null && _filteredLogs.Count > 0)
            {
                _logListBox.ScrollIntoView(_filteredLogs[^1]);
            }
        }

        private void LogListBox_SelectionChanged(object? sender, SelectionChangedEventArgs e)
        {
            // Đảm bảo thực hiện trên UI thread
            if (!Dispatcher.UIThread.CheckAccess())
            {
                Dispatcher.UIThread.Post(() => LogListBox_SelectionChanged(sender, e));
                return;
            }

            if (_logListBox?.SelectedItem is LogEntry selectedLog)
            {
                // Copy log message to clipboard
                var clipboard = TopLevel.GetTopLevel(this)?.Clipboard;
                if (clipboard != null)
                {
                    // Sử dụng Post để tránh deadlock
                    Dispatcher.UIThread.Post(async () =>
                    {
                        await clipboard.SetTextAsync(selectedLog.FormattedMessage);
                    });
                }
            }
        }

        private void OpenLogFolderButton_Click(object? sender, RoutedEventArgs e)
        {
            // Đảm bảo thực hiện trên UI thread
            Dispatcher.UIThread.Post(OpenLogFolder);
        }

        private void OpenLogFolder()
        {
            try
            {
                string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");

                // Đảm bảo thư mục tồn tại
                if (!Directory.Exists(logDirectory))
                {
                    Directory.CreateDirectory(logDirectory);
                }

                // Mở thư mục logs bằng ứng dụng mặc định của hệ thống
                string osCommand;

                if (OperatingSystem.IsWindows())
                {
                    osCommand = $"explorer \"{logDirectory}\"";
                }
                else if (OperatingSystem.IsLinux())
                {
                    osCommand = $"xdg-open \"{logDirectory}\"";
                }
                else if (OperatingSystem.IsMacOS())
                {
                    osCommand = $"open \"{logDirectory}\"";
                }
                else
                {
                    Logger.Instance.Warning("Không thể xác định hệ điều hành để mở thư mục log");
                    return;
                }

                // Khởi chạy lệnh để mở thư mục
                Process.Start(new ProcessStartInfo
                {
                    FileName = osCommand.Split(' ')[0],
                    Arguments = string.Join(" ", osCommand.Split(' ').Skip(1)),
                    UseShellExecute = true
                });

                Logger.Instance.Info($"Đã mở thư mục log: {logDirectory}");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Không thể mở thư mục log: {ex.Message}");
            }
        }

        private static void EnsureLogDirectoryExists()
        {
            string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            if (!Directory.Exists(logDirectory))
            {
                Directory.CreateDirectory(logDirectory);
            }
        }
    }
}
