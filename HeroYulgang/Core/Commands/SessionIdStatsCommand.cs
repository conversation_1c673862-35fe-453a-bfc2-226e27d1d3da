using System;
using System.Collections.Generic;
using System.Linq;
using HeroYulgang.Core.Managers;
using HeroYulgang.Helpers;
using HeroYulgang.Services;

namespace HeroYulgang.Core.Commands
{
    /// <summary>
    /// Console command để hiển thị thống kê SessionID
    /// </summary>
    public static class SessionIdStatsCommand
    {
        /// <summary>
        /// Thực hiện lệnh hiển thị thống kê SessionID
        /// </summary>
        /// <param name="args">Tham số lệnh</param>
        public static void Execute(string[] args)
        {
            try
            {
                var stats = SessionIdManager.Instance.GetPoolStats();
                
                Console.WriteLine("=== THỐNG KÊ SESSIONID POOLS ===");
                Console.WriteLine();
                Console.WriteLine($"Player Pool (1-9999):");
                Console.WriteLine($"  - Tổng cộng: {stats.PlayerPoolTotal:N0} slots");
                Console.WriteLine($"  - <PERSON><PERSON> sử dụng: {stats.PlayerPoolUsed:N0} slots");
                Console.WriteLine($"  - Còn lại: {stats.PlayerPoolAvailable:N0} slots");
                Console.WriteLine($"  - Tỷ lệ sử dụng: {(double)stats.PlayerPoolUsed / stats.PlayerPoolTotal * 100:F2}%");
                Console.WriteLine();
                
                Console.WriteLine($"NPC/Monster Pool (10000-39999):");
                Console.WriteLine($"  - Tổng cộng: {stats.NpcMonsterPoolTotal:N0} slots");
                Console.WriteLine($"  - Đã sử dụng: {stats.NpcMonsterPoolUsed:N0} slots");
                Console.WriteLine($"  - Còn lại: {stats.NpcMonsterPoolAvailable:N0} slots");
                Console.WriteLine($"  - Tỷ lệ sử dụng: {(double)stats.NpcMonsterPoolUsed / stats.NpcMonsterPoolTotal * 100:F2}%");
                Console.WriteLine();
                
                Console.WriteLine($"Pet Pool (40000-65535):");
                Console.WriteLine($"  - Tổng cộng: {stats.PetPoolTotal:N0} slots");
                Console.WriteLine($"  - Đã sử dụng: {stats.PetPoolUsed:N0} slots");
                Console.WriteLine($"  - Còn lại: {stats.PetPoolAvailable:N0} slots");
                Console.WriteLine($"  - Tỷ lệ sử dụng: {(double)stats.PetPoolUsed / stats.PetPoolTotal * 100:F2}%");
                Console.WriteLine();
                
                Console.WriteLine($"Tổng SessionID đã cấp phát: {stats.TotalAllocated:N0}");
                Console.WriteLine($"Tổng SessionID có thể cấp phát: {stats.PlayerPoolTotal + stats.NpcMonsterPoolTotal + stats.PetPoolTotal:N0}");
                
                var totalUsagePercent = (double)stats.TotalAllocated / (stats.PlayerPoolTotal + stats.NpcMonsterPoolTotal + stats.PetPoolTotal) * 100;
                Console.WriteLine($"Tỷ lệ sử dụng tổng thể: {totalUsagePercent:F2}%");
                
                Console.WriteLine();
                Console.WriteLine("=== CẢNH BÁO ===");
                
                // Cảnh báo nếu pool sắp hết
                if (stats.PlayerPoolAvailable < 100)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"⚠️  CẢNH BÁO: Player pool sắp hết! Chỉ còn {stats.PlayerPoolAvailable} slots");
                    Console.ResetColor();
                }
                else if (stats.PlayerPoolAvailable < 500)
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine($"⚠️  CHÚ Ý: Player pool đang thấp! Còn {stats.PlayerPoolAvailable} slots");
                    Console.ResetColor();
                }
                
                if (stats.NpcMonsterPoolAvailable < 1000)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"⚠️  CẢNH BÁO: NPC/Monster pool sắp hết! Chỉ còn {stats.NpcMonsterPoolAvailable} slots");
                    Console.ResetColor();
                }
                else if (stats.NpcMonsterPoolAvailable < 5000)
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine($"⚠️  CHÚ Ý: NPC/Monster pool đang thấp! Còn {stats.NpcMonsterPoolAvailable} slots");
                    Console.ResetColor();
                }
                
                if (stats.PetPoolAvailable < 1000)
                {
                    Console.ForegroundColor = ConsoleColor.Red;
                    Console.WriteLine($"⚠️  CẢNH BÁO: Pet pool sắp hết! Chỉ còn {stats.PetPoolAvailable} slots");
                    Console.ResetColor();
                }
                else if (stats.PetPoolAvailable < 5000)
                {
                    Console.ForegroundColor = ConsoleColor.Yellow;
                    Console.WriteLine($"⚠️  CHÚ Ý: Pet pool đang thấp! Còn {stats.PetPoolAvailable} slots");
                    Console.ResetColor();
                }
                
                if (stats.PlayerPoolAvailable >= 500 && stats.NpcMonsterPoolAvailable >= 5000 && stats.PetPoolAvailable >= 5000)
                {
                    Console.ForegroundColor = ConsoleColor.Green;
                    Console.WriteLine("✅ Tất cả pools đều ở mức an toàn");
                    Console.ResetColor();
                }
                
                Console.WriteLine();
                LogHelper.WriteLine(LogLevel.Info, "Đã hiển thị thống kê SessionID pools");
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Lỗi khi hiển thị thống kê SessionID: {ex.Message}");
                Console.ResetColor();
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi SessionIdStatsCommand: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Hiển thị help cho command
        /// </summary>
        public static void ShowHelp()
        {
            Console.WriteLine("=== SESSIONID STATS COMMAND ===");
            Console.WriteLine();
            Console.WriteLine("Cách sử dụng:");
            Console.WriteLine("  sessionid-stats    - Hiển thị thống kê SessionID pools");
            Console.WriteLine();
            Console.WriteLine("Mô tả:");
            Console.WriteLine("  Command này hiển thị thông tin chi tiết về việc sử dụng SessionID pools:");
            Console.WriteLine("  - Player pool (1-9999): Cho online và offline players");
            Console.WriteLine("  - NPC/Monster pool (10000-39999): Cho NPCs và monsters");
            Console.WriteLine("  - Pet pool (40000-65535): Cho pets của players");
            Console.WriteLine();
            Console.WriteLine("  Thông tin bao gồm:");
            Console.WriteLine("  - Số lượng SessionID đã sử dụng và còn lại");
            Console.WriteLine("  - Tỷ lệ sử dụng của từng pool");
            Console.WriteLine("  - Cảnh báo khi pool sắp hết");
            Console.WriteLine();
        }
        
        /// <summary>
        /// Test command để cấp phát và giải phóng SessionID
        /// </summary>
        /// <param name="args">Tham số: [type] [count]</param>
        public static void TestAllocate(string[] args)
        {
            try
            {
                if (args.Length < 2)
                {
                    Console.WriteLine("Cách sử dụng: sessionid-test [player|npc|pet] [count]");
                    return;
                }
                
                var type = args[0].ToLower();
                if (!int.TryParse(args[1], out int count) || count <= 0)
                {
                    Console.WriteLine("Count phải là số nguyên dương");
                    return;
                }
                
                var manager = SessionIdManager.Instance;
                var allocatedIds = new List<int>();
                
                Console.WriteLine($"Đang cấp phát {count} SessionID loại {type}...");
                
                for (int i = 0; i < count; i++)
                {
                    int sessionId = type switch
                    {
                        "player" => manager.AllocatePlayerSessionId(),
                        "npc" => manager.AllocateNpcMonsterSessionId(),
                        "pet" => manager.AllocatePetSessionId(),
                        _ => -1
                    };
                    
                    if (sessionId == -1)
                    {
                        Console.WriteLine($"Không thể cấp phát SessionID thứ {i + 1}");
                        break;
                    }
                    
                    allocatedIds.Add(sessionId);
                }
                
                Console.WriteLine($"Đã cấp phát {allocatedIds.Count} SessionID");
                if (allocatedIds.Count > 0)
                {
                    Console.WriteLine($"Range: {allocatedIds.Min()} - {allocatedIds.Max()}");
                }
                
                // Giải phóng tất cả
                Console.WriteLine("Đang giải phóng tất cả SessionID...");
                foreach (var id in allocatedIds)
                {
                    manager.ReleaseSessionId(id);
                }
                
                Console.WriteLine("Hoàn thành test!");
                LogHelper.WriteLine(LogLevel.Info, $"Test cấp phát {count} SessionID loại {type} hoàn thành");
            }
            catch (Exception ex)
            {
                Console.ForegroundColor = ConsoleColor.Red;
                Console.WriteLine($"Lỗi khi test SessionID: {ex.Message}");
                Console.ResetColor();
                LogHelper.WriteLine(LogLevel.Error, $"Lỗi SessionIdTestCommand: {ex.Message}");
            }
        }
    }
}
