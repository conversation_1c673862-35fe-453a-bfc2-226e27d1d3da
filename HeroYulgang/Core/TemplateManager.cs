using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Database.Entities.Public;
using HeroYulgang.Services;
using Microsoft.EntityFrameworkCore;

namespace HeroYulgang.Core
{
    public class TemplateManager
    {
        private static TemplateManager? _instance;
        private readonly DatabaseManager _databaseManager;

        // Các collection lưu trữ template
        private Dictionary<int, TblXwwlNpc> _npcTemplates = [];
        private Dictionary<int, TblXwwlMonster> _monsterTemplates = [];
        private Dictionary<int, TblXwwlItem> _itemTemplates = [];
        private Dictionary<int, TblXwwlSkill> _skillTemplates = [];
        private Dictionary<int, ThangThienKhiCong> _abilityTemplates = [];
        private Dictionary<int, TblXwwlMap> _mapTemplates = [];
        private Dictionary<int, TblXwwlKongfu> _kongfuTemplates = [];

        public static TemplateManager Instance => _instance ??= new TemplateManager();

        private TemplateManager()
        {
            _databaseManager = DatabaseManager.Instance;
        }

        public async Task<bool> LoadAllTemplatesAsync()
        {
            try
            {
                Logger.Instance.Info("Bắt đầu tải các template từ cơ sở dữ liệu...");
                
                bool success = true;
                
                // Tải các template song song để tăng tốc độ
                // var tasks = new List<Task>
                // {
                //     LoadNpcTemplatesAsync(),
                //     LoadMonsterTemplatesAsync(),
                //     LoadItemTemplatesAsync(),
                //     LoadSkillTemplatesAsync(),
                //     LoadAbilityTemplatesAsync(),
                //     LoadMapTemplatesAsync(),
                //     LoadKongfuTemplatesAsync()
                // };
                
                // await Task.WhenAll(tasks);
                await LoadNpcTemplatesAsync();
                await LoadMonsterTemplatesAsync();
                await LoadItemTemplatesAsync();
                await LoadSkillTemplatesAsync();
                await LoadAbilityTemplatesAsync();
                await LoadMapTemplatesAsync();
                await LoadKongfuTemplatesAsync();
                
                Logger.Instance.Info("Đã tải tất cả template thành công");
                return success;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải template: {ex.Message}");
                return false;
            }
        }

        private async Task LoadNpcTemplatesAsync()
        {
            try
            {
                var npcs = await _databaseManager.PublicDb.TblXwwlNpcs.ToListAsync();
                _npcTemplates = npcs.ToDictionary(n => n.FldIndex);
                Logger.Instance.Info($"Đã tải {_npcTemplates.Count} NPC template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải NPC template: {ex.Message}");
            }
        }

        private async Task LoadMonsterTemplatesAsync()
        {
            try
            {
                var monsters = await _databaseManager.PublicDb.TblXwwlMonsters.ToListAsync();
                _monsterTemplates = monsters.ToDictionary(m => m.FldPid);
                Logger.Instance.Info($"Đã tải {_monsterTemplates.Count} Monster template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Monster template: {ex.Message}");
            }
        }

        private async Task LoadItemTemplatesAsync()
        {
            try
            {
                var items = await _databaseManager.PublicDb.TblXwwlItems.ToListAsync();
                _itemTemplates = items.ToDictionary(i => i.FldPid);
                Logger.Instance.Info($"Đã tải {_itemTemplates.Count} Item template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Item template: {ex.Message}");
            }
        }

        private async Task LoadSkillTemplatesAsync()
        {
            try
            {
                var skills = await _databaseManager.PublicDb.TblXwwlSkills.ToListAsync();
                _skillTemplates = skills.ToDictionary(s => s.FldId);
                Logger.Instance.Info($"Đã tải {_skillTemplates.Count} Skill template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Skill template: {ex.Message}");
            }
        }

        private async Task LoadAbilityTemplatesAsync()
        {
            try
            {
                var abilities = await _databaseManager.PublicDb.ThangThienKhiCongs.ToListAsync();
                _abilityTemplates = abilities.ToDictionary(a => a.KhiCongId);
                Logger.Instance.Info($"Đã tải {_abilityTemplates.Count} Ability template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Ability template: {ex.Message}");
            }
        }

        private async Task LoadMapTemplatesAsync()
        {
            try
            {
                var maps = await _databaseManager.PublicDb.TblXwwlMaps.ToListAsync();
                _mapTemplates = maps.ToDictionary(m => m.FldMid);
                Logger.Instance.Info($"Đã tải {_mapTemplates.Count} Map template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Map template: {ex.Message}");
            }
        }

        private async Task LoadKongfuTemplatesAsync()
        {
            try
            {
                var kongfus = await _databaseManager.PublicDb.TblXwwlKongfus.ToListAsync();
                _kongfuTemplates = kongfus.ToDictionary(k => k.FldPid);
                Logger.Instance.Info($"Đã tải {_kongfuTemplates.Count} Kongfu template");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Lỗi khi tải Kongfu template: {ex.Message}");
            }
        }

        // Các phương thức để lấy template
        public TblXwwlNpc? GetNpcTemplate(int id) => _npcTemplates.TryGetValue(id, out var npc) ? npc : null;
        public TblXwwlMonster? GetMonsterTemplate(int id) => _monsterTemplates.TryGetValue(id, out var monster) ? monster : null;
        public TblXwwlItem? GetItemTemplate(int id) => _itemTemplates.TryGetValue(id, out var item) ? item : null;
        public TblXwwlSkill? GetSkillTemplate(int id) => _skillTemplates.TryGetValue(id, out var skill) ? skill : null;
        public ThangThienKhiCong? GetAbilityTemplate(int id) => _abilityTemplates.TryGetValue(id, out var ability) ? ability : null;
        public TblXwwlMap? GetMapTemplate(int id) => _mapTemplates.TryGetValue(id, out var map) ? map : null;
        public TblXwwlKongfu? GetKongfuTemplate(int id) => _kongfuTemplates.TryGetValue(id, out var kongfu) ? kongfu : null;

        // Các phương thức để lấy tất cả template
        public IReadOnlyDictionary<int, TblXwwlNpc> GetAllNpcTemplates() => _npcTemplates;
        public IReadOnlyDictionary<int, TblXwwlMonster> GetAllMonsterTemplates() => _monsterTemplates;
        public IReadOnlyDictionary<int, TblXwwlItem> GetAllItemTemplates() => _itemTemplates;
        public IReadOnlyDictionary<int, TblXwwlSkill> GetAllSkillTemplates() => _skillTemplates;
        public IReadOnlyDictionary<int, ThangThienKhiCong> GetAllAbilityTemplates() => _abilityTemplates;
        public IReadOnlyDictionary<int, TblXwwlMap> GetAllMapTemplates() => _mapTemplates;
        public IReadOnlyDictionary<int, TblXwwlKongfu> GetAllKongfuTemplates() => _kongfuTemplates;
    }
}
