using System;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using Avalonia.Media;
using Avalonia.Threading;

namespace HeroYulgang.Services
{
    public enum ServerState
    {
        Offline,
        Starting,
        Online,
        Stopping
    }

    public class ServerStatus : INotifyPropertyChanged
    {
        private static ServerStatus? _instance;
        private ServerState _state = ServerState.Offline;
        private DateTime _lastStateChange = DateTime.Now;
        private string _statusMessage = "Máy chủ đang offline";
        private TimeSpan _uptime = TimeSpan.Zero;
        private float _averageUpdateTimeMs = 0;
        private int _connectionCount = 0;
        private string _detailsMessage = "Chưa có thông tin";
        private bool _loginServerConnected = false;

        public static ServerStatus Instance => _instance ??= new ServerStatus();

        public ServerState State
        {
            get => _state;
            private set
            {
                if (_state != value)
                {
                    _state = value;
                    LastStateChange = DateTime.Now;
                    UpdateStatusMessage();
                    OnPropertyChanged();
                    OnPropertyChanged(nameof(StatusColor));
                }
            }
        }

        public DateTime LastStateChange
        {
            get => _lastStateChange;
            private set
            {
                if (_lastStateChange != value)
                {
                    _lastStateChange = value;
                    OnPropertyChanged();
                }
            }
        }

        public string StatusMessage
        {
            get => _statusMessage;
            private set
            {
                if (_statusMessage != value)
                {
                    _statusMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public TimeSpan Uptime
        {
            get => _uptime;
            private set
            {
                if (_uptime != value)
                {
                    _uptime = value;
                    UpdateDetailsMessage();
                    OnPropertyChanged();
                }
            }
        }

        public float AverageUpdateTimeMs
        {
            get => _averageUpdateTimeMs;
            private set
            {
                if (_averageUpdateTimeMs != value)
                {
                    _averageUpdateTimeMs = value;
                    UpdateDetailsMessage();
                    OnPropertyChanged();
                }
            }
        }

        public int ConnectionCount
        {
            get => _connectionCount;
            private set
            {
                if (_connectionCount != value)
                {
                    _connectionCount = value;
                    UpdateDetailsMessage();
                    OnPropertyChanged();
                }
            }
        }

        public string DetailsMessage
        {
            get => _detailsMessage;
            private set
            {
                if (_detailsMessage != value)
                {
                    _detailsMessage = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool LoginServerConnected
        {
            get => _loginServerConnected;
            set
            {
                if (_loginServerConnected != value)
                {
                    _loginServerConnected = value;
                    UpdateDetailsMessage();
                    OnPropertyChanged();
                }
            }
        }

        public IBrush StatusColor => State switch
        {
            ServerState.Offline => Brushes.Red,
            ServerState.Starting => Brushes.Gold,
            ServerState.Online => Brushes.Green,
            ServerState.Stopping => Brushes.Orange,
            _ => Brushes.Gray
        };

        private ServerStatus()
        {
            // Private constructor for singleton pattern
        }

        public void SetOffline()
        {
            State = ServerState.Offline;
            Logger.Instance.Info("Máy chủ hiện đang offline");
        }

        public void SetStarting()
        {
            State = ServerState.Starting;
            Logger.Instance.Info("Máy chủ đang khởi động...");
        }

        public void SetOnline()
        {
            State = ServerState.Online;
            Logger.Instance.Info("Máy chủ hiện đang online");
        }

        public void SetStopping()
        {
            State = ServerState.Stopping;
            Logger.Instance.Info("Máy chủ đang dừng...");
        }

        private void UpdateStatusMessage()
        {
            StatusMessage = State switch
            {
                ServerState.Offline => "Máy chủ đang offline",
                ServerState.Starting => "Máy chủ đang khởi động...",
                ServerState.Online => "Máy chủ đang hoạt động",
                ServerState.Stopping => "Máy chủ đang dừng...",
                _ => "Trạng thái không xác định"
            };
        }

        private void UpdateDetailsMessage()
        {
            if (State == ServerState.Online)
            {
                string loginServerStatus = LoginServerConnected ? "Đã kết nối" : "Chưa kết nối";
                DetailsMessage = $"Uptime: {Uptime:hh\\:mm\\:ss}, Avg Update: {AverageUpdateTimeMs:F2}ms, Kết nối: {ConnectionCount}, LoginServer: {loginServerStatus}";
            }
            else
            {
                DetailsMessage = "Chưa có thông tin";
            }
        }

        public void UpdateServerStats(TimeSpan uptime, float avgUpdateTimeMs, int connectionCount, bool loginServerConnected = false)
        {
            // Đảm bảo cập nhật UI trên UI thread
            if (Dispatcher.UIThread.CheckAccess())
            {
                UpdateStatsInternal(uptime, avgUpdateTimeMs, connectionCount, loginServerConnected);
            }
            else
            {
                Dispatcher.UIThread.Post(() => UpdateStatsInternal(uptime, avgUpdateTimeMs, connectionCount, loginServerConnected));
            }
        }

        private void UpdateStatsInternal(TimeSpan uptime, float avgUpdateTimeMs, int connectionCount, bool loginServerConnected)
        {
            Uptime = uptime;
            AverageUpdateTimeMs = avgUpdateTimeMs;
            ConnectionCount = connectionCount;
            LoginServerConnected = loginServerConnected;
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
